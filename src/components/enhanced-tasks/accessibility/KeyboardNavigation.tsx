import React, { useEffect, useRef, useState, createContext, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Keyboard shortcuts configuration
export const KEYBOARD_SHORTCUTS = {
  // Navigation
  NEXT_TASK: 'ArrowDown',
  PREV_TASK: 'ArrowUp',
  NEXT_COLUMN: 'ArrowRight',
  PREV_COLUMN: 'ArrowLeft',
  
  // Actions
  CREATE_TASK: 'KeyN',
  EDIT_TASK: 'KeyE',
  DELETE_TASK: 'Delete',
  COMPLETE_TASK: 'Space',
  SAVE: 'KeyS',
  CANCEL: 'Escape',
  
  // Views
  TOGGLE_VIEW: 'KeyV',
  SEARCH: 'KeyF',
  FILTER: 'KeyG',
  
  // Bulk operations
  SELECT_ALL: 'KeyA',
  SELECT_TASK: 'KeyX',
  
  // Quick actions
  QUICK_ADD: 'KeyQ',
  FOCUS_MODE: 'KeyM',
  ANALYTICS: 'KeyR',
} as const;

interface KeyboardNavigationContextType {
  focusedElement: string | null;
  setFocusedElement: (id: string | null) => void;
  registerElement: (id: string, element: HTMLElement) => void;
  unregisterElement: (id: string) => void;
  navigateToNext: () => void;
  navigateToPrev: () => void;
  executeAction: (action: string) => void;
}

const KeyboardNavigationContext = createContext<KeyboardNavigationContextType | null>(null);

export function useKeyboardNavigation() {
  const context = useContext(KeyboardNavigationContext);
  if (!context) {
    throw new Error('useKeyboardNavigation must be used within KeyboardNavigationProvider');
  }
  return context;
}

interface KeyboardNavigationProviderProps {
  children: React.ReactNode;
  onAction?: (action: string, focusedElement?: string) => void;
}

export function KeyboardNavigationProvider({ 
  children, 
  onAction 
}: KeyboardNavigationProviderProps) {
  const [focusedElement, setFocusedElement] = useState<string | null>(null);
  const [registeredElements, setRegisteredElements] = useState<Map<string, HTMLElement>>(new Map());
  const [showShortcuts, setShowShortcuts] = useState(false);

  const registerElement = (id: string, element: HTMLElement) => {
    setRegisteredElements(prev => new Map(prev).set(id, element));
  };

  const unregisterElement = (id: string) => {
    setRegisteredElements(prev => {
      const newMap = new Map(prev);
      newMap.delete(id);
      return newMap;
    });
  };

  const navigateToNext = () => {
    const elements = Array.from(registeredElements.keys());
    if (elements.length === 0) return;

    const currentIndex = focusedElement ? elements.indexOf(focusedElement) : -1;
    const nextIndex = (currentIndex + 1) % elements.length;
    const nextElement = elements[nextIndex];
    
    setFocusedElement(nextElement);
    registeredElements.get(nextElement)?.focus();
  };

  const navigateToPrev = () => {
    const elements = Array.from(registeredElements.keys());
    if (elements.length === 0) return;

    const currentIndex = focusedElement ? elements.indexOf(focusedElement) : -1;
    const prevIndex = currentIndex <= 0 ? elements.length - 1 : currentIndex - 1;
    const prevElement = elements[prevIndex];
    
    setFocusedElement(prevElement);
    registeredElements.get(prevElement)?.focus();
  };

  const executeAction = (action: string) => {
    onAction?.(action, focusedElement || undefined);
  };

  // Global keyboard event handler
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const { code, ctrlKey, metaKey, shiftKey, altKey } = event;
      const isModifierPressed = ctrlKey || metaKey;

      // Show shortcuts help
      if (code === 'Slash' && shiftKey) {
        event.preventDefault();
        setShowShortcuts(true);
        return;
      }

      // Hide shortcuts on escape
      if (code === 'Escape' && showShortcuts) {
        setShowShortcuts(false);
        return;
      }

      // Ignore if typing in input fields
      const target = event.target as HTMLElement;
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
        return;
      }

      // Navigation shortcuts
      switch (code) {
        case KEYBOARD_SHORTCUTS.NEXT_TASK:
          event.preventDefault();
          navigateToNext();
          break;
        case KEYBOARD_SHORTCUTS.PREV_TASK:
          event.preventDefault();
          navigateToPrev();
          break;
      }

      // Action shortcuts (with modifiers)
      if (isModifierPressed) {
        switch (code) {
          case KEYBOARD_SHORTCUTS.CREATE_TASK:
            event.preventDefault();
            executeAction('create_task');
            break;
          case KEYBOARD_SHORTCUTS.SAVE:
            event.preventDefault();
            executeAction('save');
            break;
          case KEYBOARD_SHORTCUTS.SEARCH:
            event.preventDefault();
            executeAction('search');
            break;
          case KEYBOARD_SHORTCUTS.TOGGLE_VIEW:
            event.preventDefault();
            executeAction('toggle_view');
            break;
          case KEYBOARD_SHORTCUTS.SELECT_ALL:
            event.preventDefault();
            executeAction('select_all');
            break;
        }
      } else {
        // Direct action shortcuts
        switch (code) {
          case KEYBOARD_SHORTCUTS.COMPLETE_TASK:
            if (focusedElement) {
              event.preventDefault();
              executeAction('complete_task');
            }
            break;
          case KEYBOARD_SHORTCUTS.EDIT_TASK:
            if (focusedElement) {
              event.preventDefault();
              executeAction('edit_task');
            }
            break;
          case KEYBOARD_SHORTCUTS.DELETE_TASK:
            if (focusedElement) {
              event.preventDefault();
              executeAction('delete_task');
            }
            break;
          case KEYBOARD_SHORTCUTS.CANCEL:
            event.preventDefault();
            executeAction('cancel');
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [focusedElement, registeredElements, showShortcuts]);

  return (
    <KeyboardNavigationContext.Provider
      value={{
        focusedElement,
        setFocusedElement,
        registerElement,
        unregisterElement,
        navigateToNext,
        navigateToPrev,
        executeAction,
      }}
    >
      {children}
      
      {/* Keyboard Shortcuts Help Modal */}
      <AnimatePresence>
        {showShortcuts && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
            onClick={() => setShowShortcuts(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gray-900 border border-gray-700 rounded-lg p-6 max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <h2 className="text-xl font-semibold text-white mb-4">Keyboard Shortcuts</h2>
              
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-300 mb-2">Navigation</h3>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Next task</span>
                      <kbd className="px-2 py-1 bg-gray-800 rounded text-xs">↓</kbd>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Previous task</span>
                      <kbd className="px-2 py-1 bg-gray-800 rounded text-xs">↑</kbd>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-300 mb-2">Actions</h3>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Complete task</span>
                      <kbd className="px-2 py-1 bg-gray-800 rounded text-xs">Space</kbd>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Edit task</span>
                      <kbd className="px-2 py-1 bg-gray-800 rounded text-xs">E</kbd>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Delete task</span>
                      <kbd className="px-2 py-1 bg-gray-800 rounded text-xs">Del</kbd>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-300 mb-2">With Ctrl/Cmd</h3>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">New task</span>
                      <kbd className="px-2 py-1 bg-gray-800 rounded text-xs">Ctrl+N</kbd>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Search</span>
                      <kbd className="px-2 py-1 bg-gray-800 rounded text-xs">Ctrl+F</kbd>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Save</span>
                      <kbd className="px-2 py-1 bg-gray-800 rounded text-xs">Ctrl+S</kbd>
                    </div>
                  </div>
                </div>

                <div className="pt-2 border-t border-gray-700">
                  <p className="text-xs text-gray-500">
                    Press <kbd className="px-1 py-0.5 bg-gray-800 rounded text-xs">?</kbd> to show/hide this help
                  </p>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </KeyboardNavigationContext.Provider>
  );
}

// Hook for registering keyboard-navigable elements
export function useKeyboardNavigable(id: string, enabled: boolean = true) {
  const { registerElement, unregisterElement, focusedElement } = useKeyboardNavigation();
  const elementRef = useRef<HTMLElement>(null);
  const isFocused = focusedElement === id;

  useEffect(() => {
    if (enabled && elementRef.current) {
      registerElement(id, elementRef.current);
      return () => unregisterElement(id);
    }
  }, [id, enabled, registerElement, unregisterElement]);

  return {
    elementRef,
    isFocused,
    tabIndex: enabled ? 0 : -1,
    'data-keyboard-id': id,
  };
}

// Focus indicator component
interface FocusIndicatorProps {
  isVisible: boolean;
  className?: string;
}

export function FocusIndicator({ isVisible, className = '' }: FocusIndicatorProps) {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className={`absolute inset-0 border-2 border-violet-500 rounded-lg pointer-events-none ${className}`}
          style={{ zIndex: 10 }}
        />
      )}
    </AnimatePresence>
  );
}

// Keyboard shortcut display component
interface KeyboardShortcutProps {
  keys: string[];
  description: string;
  className?: string;
}

export function KeyboardShortcut({ keys, description, className = '' }: KeyboardShortcutProps) {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <div className="flex gap-1">
        {keys.map((key, index) => (
          <kbd
            key={index}
            className="px-2 py-1 bg-gray-800 border border-gray-600 rounded text-xs font-mono text-gray-300"
          >
            {key}
          </kbd>
        ))}
      </div>
      <span className="text-sm text-gray-400">{description}</span>
    </div>
  );
}
