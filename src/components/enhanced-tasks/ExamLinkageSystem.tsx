import React, { useState, useMemo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { PresetExamType, PresetExam } from '@/types/todo';
import { PRESET_EXAMS, getPresetExam, getExamCategoryColor } from '@/data/presetExams';
import {
  Target,
  Calendar,
  Clock,
  BookOpen,
  TrendingUp,
  Award,
  AlertTriangle,
  CheckCircle2,
  BarChart3,
  Filter,
  Search,
  Plus,
  Link,
  Unlink,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ExamLinkageSystemProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ExamProgress {
  examType: PresetExamType;
  totalTasks: number;
  completedTasks: number;
  completionPercentage: number;
  averageDifficulty: number;
  totalTimeEstimate: number;
  subjects: string[];
}

export function ExamLinkageSystem({ isOpen, onClose }: ExamLinkageSystemProps) {
  const {
    getFilteredTasks,
    linkTaskToExam,
    subjects,
    presetExams,
  } = useEnhancedTodoStore();

  const [selectedExam, setSelectedExam] = useState<PresetExamType | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const allTasks = getFilteredTasks();

  // Calculate exam progress for each exam type
  const examProgress = useMemo(() => {
    const progress: Record<PresetExamType, ExamProgress> = {} as any;

    Object.keys(PRESET_EXAMS).forEach(examType => {
      const exam = PRESET_EXAMS[examType as PresetExamType];
      const examTasks = allTasks.filter(task => task.examType === examType);
      
      const completedTasks = examTasks.filter(task => task.completionPercentage === 100);
      const totalTimeEstimate = examTasks.reduce((sum, task) => sum + (task.timeEstimate || 0), 0);
      const averageDifficulty = examTasks.length > 0 
        ? examTasks.reduce((sum, task) => {
            const difficultyScore = task.difficultyLevel === 'easy' ? 1 : task.difficultyLevel === 'medium' ? 2 : 3;
            return sum + difficultyScore;
          }, 0) / examTasks.length
        : 0;

      const uniqueSubjects = [...new Set(examTasks.map(task => task.subjectId).filter(Boolean))];

      progress[examType as PresetExamType] = {
        examType: examType as PresetExamType,
        totalTasks: examTasks.length,
        completedTasks: completedTasks.length,
        completionPercentage: examTasks.length > 0 ? (completedTasks.length / examTasks.length) * 100 : 0,
        averageDifficulty,
        totalTimeEstimate,
        subjects: uniqueSubjects,
      };
    });

    return progress;
  }, [allTasks]);

  // Filter exams based on search and category
  const filteredExams = useMemo(() => {
    return Object.values(PRESET_EXAMS).filter(exam => {
      const matchesSearch = exam.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           exam.fullName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           exam.description.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesCategory = selectedCategory === 'all' || exam.category === selectedCategory;
      
      return matchesSearch && matchesCategory;
    });
  }, [searchQuery, selectedCategory]);

  // Get tasks for selected exam
  const selectedExamTasks = useMemo(() => {
    if (!selectedExam) return [];
    return allTasks.filter(task => task.examType === selectedExam);
  }, [selectedExam, allTasks]);

  // Get unlinked tasks (tasks without exam type)
  const unlinkedTasks = useMemo(() => {
    return allTasks.filter(task => !task.examType);
  }, [allTasks]);

  const handleLinkTask = useCallback(async (taskId: string, examType: PresetExamType) => {
    try {
      await linkTaskToExam(taskId, examType);
    } catch (error) {
      console.error('Failed to link task to exam:', error);
    }
  }, [linkTaskToExam]);

  const handleUnlinkTask = useCallback(async (taskId: string) => {
    try {
      await linkTaskToExam(taskId, undefined as any);
    } catch (error) {
      console.error('Failed to unlink task from exam:', error);
    }
  }, [linkTaskToExam]);

  const getDifficultyColor = useCallback((difficulty: number) => {
    if (difficulty <= 1.5) return 'text-green-600';
    if (difficulty <= 2.5) return 'text-yellow-600';
    return 'text-red-600';
  }, []);

  const getDifficultyLabel = useCallback((difficulty: number) => {
    if (difficulty <= 1.5) return 'Easy';
    if (difficulty <= 2.5) return 'Medium';
    return 'Hard';
  }, []);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Exam Linkage & Tracking
          </DialogTitle>
        </DialogHeader>

        <div className="flex h-[70vh]">
          {/* Left Panel - Exam List */}
          <div className="w-1/2 pr-4 border-r">
            <div className="space-y-4">
              {/* Search and filters */}
              <div className="space-y-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search exams..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="Engineering">Engineering</SelectItem>
                    <SelectItem value="Medical">Medical</SelectItem>
                    <SelectItem value="Board">Board Exams</SelectItem>
                    <SelectItem value="International">International</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Exam cards */}
              <div className="space-y-3 max-h-[calc(70vh-120px)] overflow-y-auto">
                <AnimatePresence>
                  {filteredExams.map((exam, index) => {
                    const progress = examProgress[exam.id];
                    const isSelected = selectedExam === exam.id;
                    
                    return (
                      <motion.div
                        key={exam.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ delay: index * 0.05 }}
                      >
                        <Card 
                          className={cn(
                            "cursor-pointer transition-all duration-200 hover:shadow-md",
                            isSelected && "ring-2 ring-primary"
                          )}
                          onClick={() => setSelectedExam(exam.id)}
                        >
                          <CardHeader className="pb-3">
                            <div className="flex items-start justify-between">
                              <div>
                                <CardTitle className="text-lg">{exam.name}</CardTitle>
                                <p className="text-sm text-muted-foreground">{exam.fullName}</p>
                              </div>
                              <Badge 
                                variant="outline" 
                                style={{ 
                                  borderColor: getExamCategoryColor(exam.category),
                                  color: getExamCategoryColor(exam.category)
                                }}
                              >
                                {exam.category}
                              </Badge>
                            </div>
                          </CardHeader>
                          
                          <CardContent className="space-y-3">
                            {/* Progress */}
                            <div className="space-y-2">
                              <div className="flex items-center justify-between text-sm">
                                <span>Progress</span>
                                <span>{Math.round(progress.completionPercentage)}%</span>
                              </div>
                              <Progress value={progress.completionPercentage} className="h-2" />
                            </div>

                            {/* Stats */}
                            <div className="grid grid-cols-2 gap-4 text-xs">
                              <div className="flex items-center gap-2">
                                <CheckCircle2 className="h-3 w-3 text-green-500" />
                                <span>{progress.completedTasks}/{progress.totalTasks} tasks</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Clock className="h-3 w-3 text-blue-500" />
                                <span>{Math.floor(progress.totalTimeEstimate / 60)}h {progress.totalTimeEstimate % 60}m</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <BookOpen className="h-3 w-3 text-purple-500" />
                                <span>{progress.subjects.length} subjects</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <TrendingUp className={cn("h-3 w-3", getDifficultyColor(progress.averageDifficulty))} />
                                <span>{getDifficultyLabel(progress.averageDifficulty)}</span>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    );
                  })}
                </AnimatePresence>
              </div>
            </div>
          </div>

          {/* Right Panel - Exam Details */}
          <div className="w-1/2 pl-4">
            {selectedExam ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-semibold">
                    {PRESET_EXAMS[selectedExam].name} Tasks
                  </h3>
                  <Badge variant="secondary">
                    {selectedExamTasks.length} tasks
                  </Badge>
                </div>

                {/* Exam info */}
                <Card>
                  <CardContent className="pt-6">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <Label className="text-muted-foreground">Duration</Label>
                        <p>{Math.floor(PRESET_EXAMS[selectedExam].duration / 60)}h {PRESET_EXAMS[selectedExam].duration % 60}m</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">Total Marks</Label>
                        <p>{PRESET_EXAMS[selectedExam].totalMarks}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">Difficulty</Label>
                        <p className="capitalize">{PRESET_EXAMS[selectedExam].difficulty}</p>
                      </div>
                      <div>
                        <Label className="text-muted-foreground">Category</Label>
                        <p>{PRESET_EXAMS[selectedExam].category}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Tasks list */}
                <div className="space-y-2 max-h-[calc(70vh-300px)] overflow-y-auto">
                  {selectedExamTasks.length > 0 ? (
                    selectedExamTasks.map(task => (
                      <Card key={task.id} className="p-3">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{task.title}</span>
                              <Badge variant={task.priority === 'high' ? 'destructive' : task.priority === 'medium' ? 'default' : 'secondary'}>
                                {task.priority}
                              </Badge>
                              <Badge variant="outline" className="capitalize">
                                {task.difficultyLevel}
                              </Badge>
                            </div>
                            {task.description && (
                              <p className="text-xs text-muted-foreground mt-1 line-clamp-1">
                                {task.description}
                              </p>
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            {task.completionPercentage === 100 && (
                              <CheckCircle2 className="h-4 w-4 text-green-500" />
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleUnlinkTask(task.id)}
                              className="h-8 w-8 p-0"
                            >
                              <Unlink className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </Card>
                    ))
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No tasks linked to this exam</p>
                      <p className="text-sm">Link tasks from the unlinked section below</p>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-full text-muted-foreground">
                <div className="text-center">
                  <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Select an exam to view details</p>
                  <p className="text-sm">Choose from the exam list on the left</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Unlinked tasks section */}
        {unlinkedTasks.length > 0 && (
          <div className="border-t pt-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium">Unlinked Tasks ({unlinkedTasks.length})</h4>
              <Badge variant="outline">
                <AlertTriangle className="h-3 w-3 mr-1" />
                No exam assigned
              </Badge>
            </div>
            
            <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
              {unlinkedTasks.slice(0, 10).map(task => (
                <Card key={task.id} className="p-2">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <span className="text-sm font-medium truncate block">{task.title}</span>
                    </div>
                    <Select onValueChange={(value) => handleLinkTask(task.id, value as PresetExamType)}>
                      <SelectTrigger className="w-20 h-6 text-xs">
                        <Link className="h-3 w-3" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(PRESET_EXAMS).map(exam => (
                          <SelectItem key={exam.id} value={exam.id}>
                            {exam.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </Card>
              ))}
              {unlinkedTasks.length > 10 && (
                <div className="col-span-2 text-center text-xs text-muted-foreground">
                  ... and {unlinkedTasks.length - 10} more tasks
                </div>
              )}
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
