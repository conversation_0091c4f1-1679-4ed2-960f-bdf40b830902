import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card';
import { Timer, AlertCircle, Plus, Calendar, Target } from 'lucide-react';

export const DDayCountdownWidget: React.FC = () => {
  return (
    <Card className="bg-white dark:bg-gray-900/50 border-l-4 border-l-pink-500 hover:shadow-lg transition-all duration-300 h-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-3 text-xl">
          <Timer className="h-6 w-6 text-pink-500" />
          <span className="text-gray-800 dark:text-gray-200">
            Final D-Day Countdown
          </span>
        </CardTitle>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Track time remaining for your most important goals
        </p>
      </CardHeader>
      <CardContent className="pb-6">
        {/* Main Countdown Display */}
        <div className="text-center mb-6">
          <div className="mb-3">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-1">
              Final Board Exams
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">March 15, 2025</p>
          </div>
          
          {/* Countdown Timer */}
          <div className="grid grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div className="text-3xl font-bold text-pink-600 dark:text-pink-400">87</div>
              <div className="text-xs text-gray-600 dark:text-gray-400 uppercase tracking-wide">Days</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-pink-600 dark:text-pink-400">14</div>
              <div className="text-xs text-gray-600 dark:text-gray-400 uppercase tracking-wide">Hours</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-pink-600 dark:text-pink-400">32</div>
              <div className="text-xs text-gray-600 dark:text-gray-400 uppercase tracking-wide">Minutes</div>
            </div>
          </div>

          {/* Progress Ring Placeholder */}
          <div className="relative w-24 h-24 mx-auto mb-4">
            <div className="w-24 h-24 rounded-full border-4 border-gray-200 dark:border-gray-700"></div>
            <div className="absolute inset-0 w-24 h-24 rounded-full border-4 border-pink-500 border-t-transparent transform rotate-45"></div>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-sm font-medium text-pink-600 dark:text-pink-400">72%</span>
            </div>
          </div>

          {/* Urgency Indicator */}
          <div className="flex items-center justify-center gap-2 mb-4">
            <AlertCircle className="h-4 w-4 text-amber-500" />
            <span className="text-sm font-medium text-amber-600 dark:text-amber-400">APPROACHING</span>
          </div>
        </div>

        {/* Additional Goals */}
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Other Goals</h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-blue-500" />
                <span className="text-sm text-gray-700 dark:text-gray-300">Mock Test Series</span>
              </div>
              <span className="text-sm font-medium text-blue-600 dark:text-blue-400">15 days</span>
            </div>
            <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-green-500" />
                <span className="text-sm text-gray-700 dark:text-gray-300">Project Submission</span>
              </div>
              <span className="text-sm font-medium text-green-600 dark:text-green-400">7 days</span>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <button className="flex-1 px-3 py-2 bg-pink-600 hover:bg-pink-700 text-white text-sm rounded-lg transition-colors duration-200 flex items-center justify-center gap-2">
            <Plus className="h-4 w-4" />
            Add Goal
          </button>
          <button className="px-3 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 text-sm rounded-lg transition-colors duration-200">
            Manage
          </button>
        </div>
      </CardContent>
    </Card>
  );
};
