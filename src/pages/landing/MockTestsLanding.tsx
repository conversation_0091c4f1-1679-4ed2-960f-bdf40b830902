import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { ArrowRight, BarChart2, FileText, Pie<PERSON>hart, Sparkles, ChartBar, Activity, TrendingUp, BarChart } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { SignIn } from "@/components/SignIn";
import { Header, Footer } from "@/components/shared";
import { Helmet } from "react-helmet";
import { useInView } from "framer-motion";

// Animation variants
const fadeUpVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      duration: 1,
      delay: 0.5 + i * 0.2,
      ease: [0.25, 0.4, 0.25, 1],
    },
  }),
};

const MockTestsLanding = () => {
  const { user, signInWithGoogle } = useSupabaseAuth();
  const navigate = useNavigate();
  const [scrolled, setScrolled] = useState(false);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);

    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const handleGetStarted = () => {
    if (user) {
      navigate('/mock-tests');
    } else {
      signInWithGoogle();
    }
  };

  return (
    <div className="relative w-full overflow-x-hidden bg-[#030303] font-onest">
      <Helmet>
        <title>Mock Test Analytics & Performance Tracking | IsotopeAI</title>
        <meta
          name="description"
          content="Track your mock test performance with detailed analytics, subject-wise breakdowns, and trend analysis. Improve your exam preparation with IsotopeAI's mock test tracking tools."
        />
        <meta
          name="keywords"
          content="mock tests, test analytics, performance tracking, exam preparation, JEE mock tests, NEET mock tests, BITSAT mock tests, test score analysis, subject performance, competitive exam preparation, test improvement, study analytics"
        />
        <meta property="og:title" content="Mock Test Analytics & Performance Tracking | IsotopeAI" />
        <meta property="og:description" content="Track your mock test performance with detailed analytics, subject-wise breakdowns, and trend analysis. Improve your exam preparation with IsotopeAI." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://isotopeai.com/mocktest-landing" />
        <meta name="twitter:card" content="summary_large_image" />
        <link rel="canonical" href="https://isotopeai.com/mocktest-landing" />
      </Helmet>

      {/* Background Components */}
      <GlowingBackground />
      <FloatingElements />

      {/* Header */}
      <AnimatedHeader scrolled={scrolled} />

      {/* Main Content */}
      <main className="flex-grow">
        {/* Hero Section */}
        <HeroSection handleGetStarted={handleGetStarted} user={user} />

        {/* Features Section */}
        <FeaturesSection />

        {/* Analytics Showcase */}
        <AnalyticsShowcaseSection />

        {/* How It Works */}
        <HowItWorksSection />

        {/* CTA Section */}
        <CTASection handleGetStarted={handleGetStarted} user={user} />
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
};

// Background Components
const GlowingBackground = () => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Large gradient orbs */}
      <div className="absolute -top-40 -left-40 w-80 h-80 bg-gradient-to-br from-violet-500/20 via-purple-500/10 to-transparent rounded-full blur-3xl opacity-70" />
      <div className="absolute top-1/4 -right-40 w-96 h-96 bg-gradient-to-bl from-purple-500/20 via-indigo-500/10 to-transparent rounded-full blur-3xl opacity-70" />
      <div className="absolute -bottom-40 left-1/4 w-80 h-80 bg-gradient-to-tr from-indigo-500/20 via-violet-500/10 to-transparent rounded-full blur-3xl opacity-70" />

      {/* Radial gradients */}
      <div className="absolute top-0 left-1/2 -translate-x-1/2 w-full h-full bg-gradient-radial from-purple-500/5 via-transparent to-transparent" />
      <div className="absolute bottom-0 right-0 w-1/2 h-1/2 bg-gradient-radial from-violet-500/5 via-transparent to-transparent" />
    </div>
  );
};

const FloatingElements = () => {
  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Floating geometric shapes */}
      <motion.div
        className="absolute top-1/4 left-1/4 w-4 h-4 bg-violet-400/30 rounded-full"
        animate={{
          y: [0, -20, 0],
          opacity: [0.3, 0.8, 0.3],
        }}
        transition={{
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />
      <motion.div
        className="absolute top-1/3 right-1/3 w-6 h-6 bg-purple-400/20 rounded-full"
        animate={{
          y: [0, 30, 0],
          x: [0, 10, 0],
          opacity: [0.2, 0.6, 0.2],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1,
        }}
      />
      <motion.div
        className="absolute bottom-1/4 left-1/3 w-3 h-3 bg-indigo-400/40 rounded-full"
        animate={{
          y: [0, -15, 0],
          x: [0, -10, 0],
          opacity: [0.4, 0.9, 0.4],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2,
        }}
      />
    </div>
  );
};

const AnimatedHeader = ({ scrolled }: { scrolled: boolean }) => {
  return (
    <motion.header
      className={`fixed w-full z-50 transition-all duration-500 ${scrolled ? 'py-2 backdrop-blur-md' : 'py-4 bg-transparent'}`}
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className={`absolute inset-0 ${scrolled ? 'bg-[#030303]/80 backdrop-blur-xl border-b border-white/10' : 'bg-transparent'}`}></div>
      <Header />
    </motion.header>
  );
};

const HeroSection = ({ handleGetStarted, user }: { handleGetStarted: () => void; user: any }) => {
  return (
    <section className="relative py-20 md:py-32 overflow-hidden min-h-screen flex items-center">
      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center">
          {/* Left side - Text content */}
          <motion.div
            initial="hidden"
            animate="visible"
            className="text-center lg:text-left"
          >
            <motion.div
              custom={0}
              variants={fadeUpVariants}
              className="mb-6 inline-flex items-center gap-2 rounded-full bg-white/[0.08] px-4 py-2 text-sm text-white/70 ring-1 ring-white/[0.12] backdrop-blur-md shadow-lg"
            >
              <BarChart2 className="h-4 w-4 text-violet-400" />
              <span className="font-medium">Analytics Dashboard</span>
            </motion.div>

            <motion.h1
              custom={1}
              variants={fadeUpVariants}
              className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-6 md:mb-8 tracking-tight leading-tight"
            >
              <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">
                Analyze Your{" "}
              </span>
              <motion.span
                className="relative bg-clip-text text-transparent bg-gradient-to-r from-violet-400 via-purple-400 to-indigo-400"
                initial={{ backgroundPosition: "0% 50%" }}
                animate={{ backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"] }}
                transition={{ duration: 6, repeat: Infinity, ease: "linear" }}
              >
                Mock Test Results
              </motion.span>
            </motion.h1>

            <motion.p
              custom={2}
              variants={fadeUpVariants}
              className="text-lg sm:text-xl md:text-2xl text-white/60 mb-8 md:mb-12 max-w-3xl mx-auto lg:mx-0 leading-relaxed"
            >
              Track your performance, identify strengths and weaknesses, and improve your scores with our comprehensive mock test analytics
            </motion.p>

            <motion.div
              custom={3}
              variants={fadeUpVariants}
              className="flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-4 mb-8"
            >
              {user ? (
                <Button
                  asChild
                  className="bg-violet-500/80 hover:bg-violet-600/90 text-white relative overflow-hidden group px-8 py-6 text-lg"
                >
                  <div onClick={handleGetStarted} className="cursor-pointer">
                    <span className="relative z-10 flex items-center">
                      Go to Mock Tests
                      <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                    </span>
                    <span className="absolute inset-0 bg-gradient-to-r from-violet-600 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                </Button>
              ) : (
                <div className="flex justify-center">
                  <SignIn />
                </div>
              )}
            </motion.div>

            {/* Trust indicators */}
            <motion.div
              custom={4}
              variants={fadeUpVariants}
              className="flex flex-wrap justify-center lg:justify-start gap-6 text-sm text-white/60"
            >
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-violet-400 rounded-full animate-pulse"></div>
                <span>Track multiple subjects</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                <span>Visualize performance trends</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-indigo-400 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                <span>Identify weak areas</span>
              </div>
            </motion.div>
        </motion.div>

          {/* Right side - Analytics Demo */}
          <motion.div
            custom={5}
            variants={fadeUpVariants}
            className="lg:mt-0 mt-8"
          >
            <div className="bg-white/[0.03] backdrop-blur-md rounded-2xl p-6 md:p-8 shadow-2xl border border-white/[0.08] relative overflow-hidden">
              {/* Gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-violet-500/10 via-transparent to-purple-500/10 pointer-events-none"></div>

              <div className="relative z-10">
                <div className="flex items-center gap-3 mb-6">
                  <div className="bg-gradient-to-br from-violet-500/30 to-purple-500/30 p-3 rounded-xl border border-white/10">
                    <BarChart2 className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-white/90">Mock Test Analytics</h3>
                    <p className="text-sm text-white/60">Performance Dashboard</p>
                  </div>
                </div>

                <div className="bg-white/[0.02] rounded-xl p-4 border border-white/5">
                  <img
                    src="/mocktest-analytics-preview.png"
                    alt="Mock Test Analytics Dashboard"
                    className="rounded-lg shadow-lg border border-white/10 w-full"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = "https://placehold.co/600x400/0a0a2a/white?text=Mock+Test+Analytics";
                    }}
                  />
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

const FeaturesSection = () => {
  return (
    <section className="relative py-20 md:py-32 overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-violet-950/10 to-transparent"></div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <motion.h2
            custom={0}
            variants={fadeUpVariants}
            className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 tracking-tight"
          >
            <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">
              Powerful{" "}
            </span>
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-violet-400 via-purple-400 to-indigo-400">
              Analytics Tools
            </span>
          </motion.h2>
          <motion.p
            custom={1}
            variants={fadeUpVariants}
            className="text-white/60 max-w-2xl mx-auto text-lg"
          >
            Track, analyze, and improve your mock test performance with our comprehensive analytics suite
          </motion.p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          <FeatureCard
            icon={<BarChart className="w-6 h-6" />}
            title="Subject Performance"
            description="Track your performance across different subjects to identify strengths and weaknesses"
            delay={0.1}
            gradient="from-violet-500/30 to-purple-500/30"
          />
          <FeatureCard
            icon={<TrendingUp className="w-6 h-6" />}
            title="Performance Trends"
            description="Visualize your progress over time with detailed trend analysis"
            delay={0.2}
            gradient="from-purple-500/30 to-indigo-500/30"
          />
          <FeatureCard
            icon={<Activity className="w-6 h-6" />}
            title="Subject Strengths"
            description="Identify your strongest and weakest subjects with radar charts"
            delay={0.3}
            gradient="from-indigo-500/30 to-violet-500/30"
          />
          <FeatureCard
            icon={<PieChart className="w-6 h-6" />}
            title="Score Distribution"
            description="Analyze how your scores are distributed across different subjects"
            delay={0.4}
            gradient="from-violet-500/30 to-blue-500/30"
          />
          <FeatureCard
            icon={<FileText className="w-6 h-6" />}
            title="Test Management"
            description="Easily add and manage your mock test results with our intuitive interface"
            delay={0.5}
            gradient="from-purple-500/30 to-violet-500/30"
          />
          <FeatureCard
            icon={<ChartBar className="w-6 h-6" />}
            title="Performance Metrics"
            description="Track key performance metrics like average scores, improvement rate, and more"
            delay={0.6}
            gradient="from-indigo-500/30 to-purple-500/30"
          />
        </div>
      </div>
    </section>
  );
};

const FeatureCard = ({
  icon,
  title,
  description,
  delay,
  gradient
}: {
  icon: React.ReactNode;
  title: string;
  description: string;
  delay: number;
  gradient: string;
}) => {
  return (
    <motion.div
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      custom={delay}
      variants={fadeUpVariants}
      whileHover={{
        y: -5,
        transition: { duration: 0.2 }
      }}
      className="relative group"
    >
      <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-lg`}></div>
      <div className="bg-white/[0.03] backdrop-blur-md p-6 md:p-8 rounded-2xl shadow-lg border border-white/[0.08] relative h-full flex flex-col group-hover:border-white/20 transition-colors duration-300">
        <div className="flex items-start gap-4 mb-5">
          <div className={`bg-gradient-to-br ${gradient} p-4 rounded-xl relative border border-white/10 shrink-0`}>
            <motion.div
              animate={{ rotate: [0, 5, 0, -5, 0] }}
              transition={{
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="text-white"
            >
              {icon}
            </motion.div>
          </div>

          <div>
            <h3 className="text-xl font-semibold text-white/90 mb-2">{title}</h3>
            <p className="text-white/60">{description}</p>
          </div>
        </div>

        {/* Bottom flourish */}
        <div className={`absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r ${gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-b-2xl`}></div>
      </div>
    </motion.div>
  );
};

const AnalyticsShowcaseSection = () => {
  return (
    <section className="container mx-auto px-4 py-28 relative z-10">
      <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/5 via-transparent to-purple-600/5 z-[-1]"></div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="text-center mb-16"
      >
        <div className="inline-flex items-center mb-3 bg-indigo-500/10 px-4 py-1.5 rounded-full text-white/80 text-sm border border-indigo-500/20">
          <Sparkles className="h-4 w-4 mr-2 text-indigo-400" />
          Visualize Your Progress
        </div>
        <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 via-purple-400 to-indigo-400">
            Comprehensive
          </span>{" "}
          Analytics Dashboard
        </h2>
        <p className="text-white/60 max-w-2xl mx-auto text-lg">
          Get detailed insights into your performance with our intuitive analytics dashboard
        </p>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="relative"
        >
          <div className="bg-white/5 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-xl">
            <h3 className="text-xl font-semibold mb-6 flex items-center">
              <BarChart className="w-5 h-5 text-indigo-400 mr-3" />
              Subject Performance
            </h3>
            <div className="aspect-video bg-gradient-to-br from-indigo-900/30 to-purple-900/30 rounded-lg flex items-center justify-center">
              <img
                src="/subject-performance-chart.png"
                alt="Subject Performance Chart"
                className="rounded-lg max-w-full max-h-full"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = "https://placehold.co/600x400/0a0a2a/white?text=Subject+Performance+Chart";
                }}
              />
            </div>
            <p className="mt-4 text-white/60 text-sm">
              Track your performance across different subjects to identify your strengths and areas for improvement
            </p>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, x: 20 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="relative"
        >
          <div className="bg-white/5 backdrop-blur-md rounded-2xl p-6 border border-white/10 shadow-xl">
            <h3 className="text-xl font-semibold mb-6 flex items-center">
              <TrendingUp className="w-5 h-5 text-indigo-400 mr-3" />
              Performance Trends
            </h3>
            <div className="aspect-video bg-gradient-to-br from-indigo-900/30 to-purple-900/30 rounded-lg flex items-center justify-center">
              <img
                src="/performance-trend-chart.png"
                alt="Performance Trend Chart"
                className="rounded-lg max-w-full max-h-full"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = "https://placehold.co/600x400/0a0a2a/white?text=Performance+Trend+Chart";
                }}
              />
            </div>
            <p className="mt-4 text-white/60 text-sm">
              Visualize your progress over time and identify patterns in your performance
            </p>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

const HowItWorksSection = () => {
  return (
    <section className="container mx-auto px-4 py-28 relative z-10">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.8 }}
        className="text-center mb-16"
      >
        <div className="inline-flex items-center mb-3 bg-indigo-500/10 px-4 py-1.5 rounded-full text-white/80 text-sm border border-indigo-500/20">
          <Sparkles className="h-4 w-4 mr-2 text-indigo-400" />
          Simple Process
        </div>
        <h2 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
          <span className="bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 via-purple-400 to-indigo-400">
            How It Works
          </span>
        </h2>
        <p className="text-white/60 max-w-2xl mx-auto text-lg">
          Start tracking your mock test performance in just a few simple steps
        </p>
      </motion.div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <StepCard
          number={1}
          title="Add Your Test Results"
          description="Enter your mock test details including subject-wise marks and test date"
          delay={0.1}
        />
        <StepCard
          number={2}
          title="View Analytics"
          description="Get instant access to comprehensive analytics and visualizations"
          delay={0.2}
        />
        <StepCard
          number={3}
          title="Track Progress"
          description="Monitor your improvement over time and identify areas for focus"
          delay={0.3}
        />
      </div>
    </section>
  );
};

const StepCard = ({
  number,
  title,
  description,
  delay
}: {
  number: number;
  title: string;
  description: string;
  delay: number;
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay }}
      className="group"
    >
      <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-8 h-full transition-all duration-300 hover:shadow-lg hover:shadow-indigo-500/10 hover:border-indigo-500/30 relative overflow-hidden">
        {/* Step number */}
        <div className="absolute -top-6 -left-6 w-20 h-20 bg-gradient-to-br from-indigo-500/20 to-purple-600/20 rounded-full flex items-center justify-center">
          <div className="text-3xl font-bold text-indigo-400 mt-6 ml-6">{number}</div>
        </div>

        <div className="pt-4 pl-4">
          <h3 className="text-xl font-semibold mb-3 text-white/90">{title}</h3>
          <p className="text-white/60">{description}</p>
        </div>
      </div>
    </motion.div>
  );
};

const CTASection = ({ handleGetStarted, user }: { handleGetStarted: () => void; user: any }) => {
  return (
    <section className="relative py-20 md:py-32 overflow-hidden">
      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="relative"
        >
          <div className="bg-white/[0.03] backdrop-blur-md rounded-3xl p-8 md:p-12 border border-white/[0.08] shadow-2xl relative overflow-hidden">
            {/* Background gradient */}
            <div className="absolute inset-0 bg-gradient-to-br from-violet-500/10 via-purple-500/5 to-indigo-500/10 pointer-events-none"></div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center relative z-10">
              <div>
                <motion.h2
                  custom={0}
                  variants={fadeUpVariants}
                  className="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 tracking-tight"
                >
                  <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">
                    Ready to{" "}
                  </span>
                  <span className="bg-clip-text text-transparent bg-gradient-to-r from-violet-400 via-purple-400 to-indigo-400">
                    Track Your Progress
                  </span>
                  <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">
                    {" "}and Improve Your Scores?
                  </span>
                </motion.h2>

                <motion.p
                  custom={1}
                  variants={fadeUpVariants}
                  className="text-white/60 text-lg mb-8"
                >
                  Start tracking your mock test performance today and get valuable insights to help you improve your scores.
                </motion.p>

                <motion.div
                  custom={2}
                  variants={fadeUpVariants}
                  className="flex justify-start"
                >
                  {user ? (
                    <Button
                      asChild
                      className="bg-violet-500/80 hover:bg-violet-600/90 text-white relative overflow-hidden group px-8 py-6 text-lg"
                    >
                      <div onClick={handleGetStarted} className="cursor-pointer">
                        <span className="relative z-10 flex items-center">
                          Go to Mock Tests
                          <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                        </span>
                        <span className="absolute inset-0 bg-gradient-to-r from-violet-600 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </div>
                    </Button>
                  ) : (
                    <div className="flex justify-start">
                      <SignIn />
                    </div>
                  )}
                </motion.div>
              </div>

            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="relative"
            >
              <div className="bg-gradient-to-br from-indigo-900/30 to-purple-900/30 rounded-2xl p-6 border border-white/10">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white/5 rounded-xl p-4 border border-white/10">
                    <div className="flex items-center mb-2">
                      <BarChart className="w-4 h-4 text-indigo-400 mr-2" />
                      <h4 className="text-sm font-medium">Physics</h4>
                    </div>
                    <div className="text-2xl font-bold text-indigo-400">87%</div>
                    <div className="text-xs text-white/50 mt-1">+12% from last test</div>
                  </div>

                  <div className="bg-white/5 rounded-xl p-4 border border-white/10">
                    <div className="flex items-center mb-2">
                      <BarChart className="w-4 h-4 text-purple-400 mr-2" />
                      <h4 className="text-sm font-medium">Chemistry</h4>
                    </div>
                    <div className="text-2xl font-bold text-purple-400">78%</div>
                    <div className="text-xs text-white/50 mt-1">+5% from last test</div>
                  </div>

                  <div className="bg-white/5 rounded-xl p-4 border border-white/10">
                    <div className="flex items-center mb-2">
                      <BarChart className="w-4 h-4 text-blue-400 mr-2" />
                      <h4 className="text-sm font-medium">Mathematics</h4>
                    </div>
                    <div className="text-2xl font-bold text-blue-400">92%</div>
                    <div className="text-xs text-white/50 mt-1">+8% from last test</div>
                  </div>

                  <div className="bg-white/5 rounded-xl p-4 border border-white/10">
                    <div className="flex items-center mb-2">
                      <TrendingUp className="w-4 h-4 text-emerald-400 mr-2" />
                      <h4 className="text-sm font-medium">Overall</h4>
                    </div>
                    <div className="text-2xl font-bold text-emerald-400">85%</div>
                    <div className="text-xs text-white/50 mt-1">+9% from last test</div>
                  </div>
                </div>

                <div className="mt-4 p-4 bg-white/5 rounded-xl border border-white/10">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium">Progress Trend</h4>
                    <span className="text-xs text-emerald-400">+8.5% avg</span>
                  </div>
                  <div className="h-12 flex items-end gap-1">
                    {[65, 72, 68, 75, 79, 82, 85].map((value, index) => (
                      <div
                        key={index}
                        className="bg-gradient-to-t from-indigo-600 to-purple-600 rounded-sm"
                        style={{
                          height: `${value}%`,
                          width: '12%'
                        }}
                      ></div>
                    ))}
                  </div>
                  <div className="flex justify-between mt-2 text-xs text-white/50">
                    <span>Test 1</span>
                    <span>Test 7</span>
                  </div>
                </div>
              </div>
            </motion.div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default MockTestsLanding;