import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '../ui/card';
import { PieChart, Activity, TrendingUp, Clock, BookOpen, CheckCircle } from 'lucide-react';

export const AnalyticsOverviewWidget: React.FC = () => {
  return (
    <Card className="bg-white dark:bg-gray-900/50 border-l-4 border-l-slate-500 hover:shadow-lg transition-all duration-300 h-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-3 text-xl">
          <PieChart className="h-6 w-6 text-slate-500" />
          <span className="text-gray-800 dark:text-gray-200">
            Analytics Overview
          </span>
        </CardTitle>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Comprehensive performance metrics and trends
        </p>
      </CardHeader>
      <CardContent className="pb-6">
        {/* Today's Study Time */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Total Time Studied Today</span>
            <Clock className="h-4 w-4 text-slate-500" />
          </div>
          <div className="text-3xl font-bold text-slate-900 dark:text-slate-100 mb-2">4h 32m</div>
          <div className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-green-500" />
            <span className="text-sm text-green-600">+18% from yesterday</span>
          </div>
        </div>

        {/* Subject Breakdown */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Subject Breakdown</h4>
          <div className="space-y-3">
            {/* Subject 1 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Mathematics</span>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">1h 45m</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div className="bg-blue-500 h-2 rounded-full" style={{ width: '38%' }}></div>
            </div>

            {/* Subject 2 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Physics</span>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">1h 30m</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div className="bg-green-500 h-2 rounded-full" style={{ width: '33%' }}></div>
            </div>

            {/* Subject 3 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Chemistry</span>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-gray-100">1h 17m</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div className="bg-purple-500 h-2 rounded-full" style={{ width: '29%' }}></div>
            </div>
          </div>
        </div>

        {/* Quick Stats Grid */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="p-3 bg-blue-50 dark:bg-blue-900/10 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center gap-2 mb-1">
              <BookOpen className="h-4 w-4 text-blue-600" />
              <span className="text-xs font-medium text-blue-700 dark:text-blue-400">Chapters</span>
            </div>
            <p className="text-lg font-bold text-blue-900 dark:text-blue-100">24/30</p>
            <p className="text-xs text-blue-600 dark:text-blue-500">Completed</p>
          </div>

          <div className="p-3 bg-green-50 dark:bg-green-900/10 rounded-lg border border-green-200 dark:border-green-800">
            <div className="flex items-center gap-2 mb-1">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <span className="text-xs font-medium text-green-700 dark:text-green-400">Tasks</span>
            </div>
            <p className="text-lg font-bold text-green-900 dark:text-green-100">8/12</p>
            <p className="text-xs text-green-600 dark:text-green-500">Today</p>
          </div>
        </div>

        {/* View Details Button */}
        <button className="w-full px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white text-sm rounded-lg transition-colors duration-200 flex items-center justify-center gap-2">
          <Activity className="h-4 w-4" />
          View Detailed Analytics
        </button>
      </CardContent>
    </Card>
  );
};
