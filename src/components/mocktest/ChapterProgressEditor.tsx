import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  Plus, 
  Trash2, 
  BookO<PERSON>, 
  Target,
  TrendingUp,
  Star,
  Check
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ChapterProgress, chapterProgressUtils } from "@/utils/mockTestLocalStorage";
import { cn } from "@/lib/utils";

interface ChapterProgressEditorProps {
  chapters: ChapterProgress[];
  onChaptersChange: (chapters: ChapterProgress[]) => void;
  className?: string;
}

export function ChapterProgressEditor({ 
  chapters, 
  onChaptersChange, 
  className 
}: ChapterProgressEditorProps) {
  const [newChapterName, setNewChapterName] = useState("");
  const [isAddingChapter, setIsAddingChapter] = useState(false);

  const handleAddChapter = () => {
    if (newChapterName.trim()) {
      const updatedChapters = chapterProgressUtils.addChapter(chapters, newChapterName.trim());
      onChaptersChange(updatedChapters);
      setNewChapterName("");
      setIsAddingChapter(false);
    }
  };

  const handleRemoveChapter = (chapterName: string) => {
    const updatedChapters = chapterProgressUtils.removeChapter(chapters, chapterName);
    onChaptersChange(updatedChapters);
  };

  const handleUpdateChapter = (
    chapterName: string, 
    updates: Partial<Pick<ChapterProgress, 'completionPercentage' | 'confidenceLevel'>>
  ) => {
    const updatedChapters = chapterProgressUtils.updateChapterProgress(chapters, chapterName, updates);
    onChaptersChange(updatedChapters);
  };

  const getConfidenceLabel = (level: number): string => {
    switch (level) {
      case 1: return "Very Low";
      case 2: return "Low";
      case 3: return "Medium";
      case 4: return "High";
      case 5: return "Very High";
      default: return "Medium";
    }
  };

  const getConfidenceColor = (level: number): string => {
    switch (level) {
      case 1: return "text-red-600 bg-red-50 border-red-200 dark:bg-red-950/30 dark:border-red-800";
      case 2: return "text-orange-600 bg-orange-50 border-orange-200 dark:bg-orange-950/30 dark:border-orange-800";
      case 3: return "text-yellow-600 bg-yellow-50 border-yellow-200 dark:bg-yellow-950/30 dark:border-yellow-800";
      case 4: return "text-blue-600 bg-blue-50 border-blue-200 dark:bg-blue-950/30 dark:border-blue-800";
      case 5: return "text-green-600 bg-green-50 border-green-200 dark:bg-green-950/30 dark:border-green-800";
      default: return "text-gray-600 bg-gray-50 border-gray-200 dark:bg-gray-950/30 dark:border-gray-800";
    }
  };

  const overallProgress = chapterProgressUtils.calculateOverallProgress(chapters);
  const completionStatus = chapterProgressUtils.getCompletionStatus(chapters);

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5 text-violet-600" />
              Chapter Progress
            </CardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              Track your preparation progress for each chapter
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            <div className="text-right">
              <div className="text-2xl font-bold text-violet-600">{overallProgress}%</div>
              <div className="text-xs text-muted-foreground">Overall Progress</div>
            </div>
            <div className="text-right">
              <div className="text-lg font-semibold">{completionStatus.completed}/{completionStatus.total}</div>
              <div className="text-xs text-muted-foreground">Completed</div>
            </div>
          </div>
        </div>
        
        <Progress value={overallProgress} className="h-2" />
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Chapter List */}
        <AnimatePresence>
          {chapters.map((chapter, index) => (
            <motion.div
              key={chapter.chapterName}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ delay: index * 0.1 }}
              className="p-4 rounded-lg border bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-900/50 dark:to-slate-800/50"
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h4 className="font-semibold text-lg">{chapter.chapterName}</h4>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge className={cn("text-xs", getConfidenceColor(chapter.confidenceLevel))}>
                      {getConfidenceLabel(chapter.confidenceLevel)}
                    </Badge>
                    {chapter.completionPercentage >= 80 && (
                      <Badge className="text-xs bg-green-100 text-green-700 border-green-200 dark:bg-green-950/30 dark:text-green-400 dark:border-green-800">
                        <Check className="h-3 w-3 mr-1" />
                        Complete
                      </Badge>
                    )}
                  </div>
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveChapter(chapter.chapterName)}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/30"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-4">
                {/* Completion Percentage - Responsive */}
                <div className="space-y-2">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                    <Label className="text-sm font-medium">Completion</Label>
                    <div className="flex items-center gap-2 flex-1 sm:flex-initial">
                      <span className="text-sm font-semibold text-violet-600 min-w-[3rem]">{chapter.completionPercentage}%</span>
                      <div className="flex-1 sm:w-24">
                        <Slider
                          value={[chapter.completionPercentage]}
                          onValueChange={([value]) => handleUpdateChapter(chapter.chapterName, { completionPercentage: value })}
                          max={100}
                          step={5}
                          className="w-full"
                        />
                      </div>
                    </div>
                  </div>
                  <Progress value={chapter.completionPercentage} className="h-2" />
                </div>

                {/* Confidence Level - Responsive */}
                <div className="space-y-2">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                    <Label className="text-sm font-medium">Confidence</Label>
                    <div className="flex-1 sm:flex-initial sm:w-32">
                      <Select
                        value={chapter.confidenceLevel.toString()}
                        onValueChange={(value) => handleUpdateChapter(chapter.chapterName, { confidenceLevel: parseInt(value) as 1 | 2 | 3 | 4 | 5 })}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 - Very Low</SelectItem>
                          <SelectItem value="2">2 - Low</SelectItem>
                          <SelectItem value="3">3 - Medium</SelectItem>
                          <SelectItem value="4">4 - High</SelectItem>
                          <SelectItem value="5">5 - Very High</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Add New Chapter */}
        {isAddingChapter ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="p-4 rounded-lg border-2 border-dashed border-violet-300 dark:border-violet-700"
          >
            <div className="space-y-3">
              <Label htmlFor="new-chapter">Chapter Name</Label>
              <Input
                id="new-chapter"
                value={newChapterName}
                onChange={(e) => setNewChapterName(e.target.value)}
                placeholder="Enter chapter name..."
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleAddChapter();
                  if (e.key === 'Escape') setIsAddingChapter(false);
                }}
                autoFocus
              />
              <div className="flex gap-2">
                <Button onClick={handleAddChapter} size="sm" disabled={!newChapterName.trim()}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Chapter
                </Button>
                <Button variant="outline" onClick={() => setIsAddingChapter(false)} size="sm">
                  Cancel
                </Button>
              </div>
            </div>
          </motion.div>
        ) : (
          <Button
            variant="outline"
            onClick={() => setIsAddingChapter(true)}
            className="w-full border-dashed border-violet-300 dark:border-violet-700 text-violet-600 hover:text-violet-700 hover:bg-violet-50 dark:hover:bg-violet-950/30"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Chapter
          </Button>
        )}

        {chapters.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <BookOpen className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>No chapters added yet</p>
            <p className="text-sm">Add chapters to track your preparation progress</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
