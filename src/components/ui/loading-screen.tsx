import React from 'react';
import { motion } from 'framer-motion';
import { LoadingSpinner } from './loading-spinner';
import { cn } from '@/lib/utils';

interface LoadingScreenProps {
  title?: string;
  subtitle?: string;
  variant?: 'default' | 'minimal' | 'branded' | 'gradient';
  className?: string;
  showLogo?: boolean;
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.3,
      staggerChildren: 0.1,
    },
  },
  exit: {
    opacity: 0,
    transition: { duration: 0.2 },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.4 },
  },
};

export const LoadingScreen: React.FC<LoadingScreenProps> = ({
  title = 'Loading...',
  subtitle,
  variant = 'default',
  className,
  showLogo = true,
}) => {
  const getBackgroundClasses = () => {
    switch (variant) {
      case 'minimal':
        return 'bg-background';
      case 'branded':
        return 'bg-gradient-to-br from-primary/5 to-secondary/5';
      case 'gradient':
        return 'bg-gradient-to-br from-indigo-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900';
      default:
        return 'bg-background';
    }
  };

  return (
    <motion.div
      className={cn(
        'fixed inset-0 z-50 flex flex-col items-center justify-center',
        getBackgroundClasses(),
        className
      )}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      {/* Background Effects */}
      {variant === 'gradient' && (
        <>
          <div className="absolute inset-0 bg-grid-pattern opacity-5" />
          <motion.div
            className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/10 rounded-full blur-3xl"
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.5, 0.3],
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
            }}
          />
          <motion.div
            className="absolute bottom-1/4 right-1/4 w-48 h-48 bg-secondary/10 rounded-full blur-3xl"
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.5, 0.3, 0.5],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
            }}
          />
        </>
      )}

      <motion.div
        className="relative flex flex-col items-center space-y-6"
        variants={itemVariants}
      >
        {/* Logo */}
        {showLogo && (
          <motion.div
            className="flex items-center space-x-3"
            variants={itemVariants}
          >
            <motion.div
              className="w-12 h-12 bg-gradient-to-br from-primary to-primary/80 rounded-xl flex items-center justify-center"
              animate={{
                rotate: [0, 360],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: 'linear',
              }}
            >
              <span className="text-white font-bold text-xl">I</span>
            </motion.div>
            <span className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
              IsotopeAI
            </span>
          </motion.div>
        )}

        {/* Loading Spinner */}
        <motion.div variants={itemVariants}>
          <LoadingSpinner
            size="lg"
            variant={variant === 'minimal' ? 'default' : 'orbit'}
            color="primary"
          />
        </motion.div>

        {/* Text Content */}
        <motion.div
          className="text-center space-y-2"
          variants={itemVariants}
        >
          <h2 className="text-lg font-semibold text-foreground">{title}</h2>
          {subtitle && (
            <p className="text-sm text-muted-foreground max-w-md">{subtitle}</p>
          )}
        </motion.div>

        {/* Progress Dots */}
        {variant !== 'minimal' && (
          <motion.div
            className="flex space-x-2"
            variants={itemVariants}
          >
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="w-2 h-2 bg-primary/40 rounded-full"
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.4, 1, 0.4],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: i * 0.2,
                }}
              />
            ))}
          </motion.div>
        )}
      </motion.div>
    </motion.div>
  );
};