import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>R<PERSON>, Brain, Clock, MessageSquare, Sparkles, ListTodo, Zap, Check, Star, User, Menu, X, <PERSON><PERSON><PERSON>2, Mail } from 'lucide-react';
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { useInView } from 'framer-motion';
import { useRef, useState, useEffect } from 'react';
import { SignIn } from '../components/SignIn';
import { useSupabaseAuth } from '../contexts/SupabaseAuthContext';
import { useNavigate } from 'react-router-dom';
import { FeedbackWidget } from '@/components/FeedbackWidget';
import { Helmet } from "react-helmet";

// Add fonts import
import '@/styles/fonts.css';

// Add custom cursor styles
const CustomCursor = () => {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isVisible, setIsVisible] = useState(false);
  const [isClicking, setIsClicking] = useState(false);

  useEffect(() => {
    const updatePosition = (e: MouseEvent) => {
      setPosition({ x: e.clientX, y: e.clientY });
      setIsVisible(true);
    };

    const handleMouseDown = () => setIsClicking(true);
    const handleMouseUp = () => setIsClicking(false);
    const handleMouseLeave = () => setIsVisible(false);
    const handleMouseEnter = () => setIsVisible(true);

    window.addEventListener('mousemove', updatePosition);
    window.addEventListener('mousedown', handleMouseDown);
    window.addEventListener('mouseup', handleMouseUp);
    window.addEventListener('mouseleave', handleMouseLeave);
    window.addEventListener('mouseenter', handleMouseEnter);

    return () => {
      window.removeEventListener('mousemove', updatePosition);
      window.removeEventListener('mousedown', handleMouseDown);
      window.removeEventListener('mouseup', handleMouseUp);
      window.removeEventListener('mouseleave', handleMouseLeave);
      window.removeEventListener('mouseenter', handleMouseEnter);
    };
  }, []);

  return (
    <>
      <div
        className="fixed pointer-events-none z-[9999] transform -translate-x-1/2 -translate-y-1/2 hidden sm:block"
        style={{
          left: `${position.x}px`,
          top: `${position.y}px`,
          opacity: isVisible ? 1 : 0,
          transition: 'opacity 0.15s ease'
        }}
      >
        <div
          className={`relative ${isClicking ? 'scale-90' : 'scale-100'} transition-transform duration-150`}
        >
          <div className="absolute -inset-3 bg-emerald-500/20 rounded-full blur-md animate-pulse"
            style={{ animationDuration: '2s' }} />
          <Brain
            className={`w-6 h-6 text-emerald-400 drop-shadow-[0_0_6px_rgba(52,211,153,0.6)] animate-pulse ${isClicking ? 'text-teal-300' : ''}`}
            style={{ animationDuration: '4s' }}
          />
        </div>
      </div>
      <div
        className="fixed pointer-events-none z-[9998] transform -translate-x-1/2 -translate-y-1/2 hidden sm:block"
        style={{
          left: `${position.x}px`,
          top: `${position.y}px`,
          opacity: isVisible ? 0.5 : 0,
          transition: 'opacity 0.3s ease'
        }}
      >
        <div className="w-10 h-10 rounded-full bg-gradient-to-r from-emerald-400/20 via-teal-400/20 to-cyan-400/20"></div>
      </div>
    </>
  );
};

function GlowingBackground() {
  return (
    <div className="absolute inset-0 overflow-hidden">
      <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-indigo-500/20 via-transparent to-rose-500/20 blur-3xl opacity-60" />
      <div className="absolute -top-[25%] -left-[25%] w-[150%] h-[150%] bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.08),transparent_50%)] animate-[spin_20s_linear_infinite]" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_80%_20%,rgba(120,119,198,0.4),transparent_25%)]" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_20%_80%,rgba(205,133,255,0.4),transparent_25%)]" />

      {/* Animated gradient orbs */}
      <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-500/30 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '8s' }} />
      <div className="absolute bottom-1/3 right-1/3 w-56 h-56 bg-purple-500/20 rounded-full blur-3xl animate-pulse" style={{ animationDuration: '10s' }} />
    </div>
  );
}

function FloatingElements() {
  return (
    <>
      <div className="absolute top-1/4 left-10 w-3 h-3 bg-violet-500/80 rounded-full animate-ping" style={{ animationDuration: '3s', animationDelay: '1s' }} />
      <div className="absolute top-1/3 right-20 w-2 h-2 bg-rose-500/80 rounded-full animate-ping" style={{ animationDuration: '4s', animationDelay: '0.5s' }} />
      <div className="absolute bottom-1/4 left-1/4 w-2 h-2 bg-emerald-500/80 rounded-full animate-ping" style={{ animationDuration: '5s', animationDelay: '2s' }} />

      {/* Floating shapes */}
      <ElegantShape
        className="left-[10%] top-[20%]"
        gradient="from-violet-500/[0.1]"
        width={100}
        height={50}
      />
      <ElegantShape
        className="right-[15%] top-[25%]"
        gradient="from-rose-500/[0.1]"
        width={120}
        height={60}
        rotate={30}
        delay={0.3}
      />
      <ElegantShape
        className="left-[20%] bottom-[30%]"
        gradient="from-blue-500/[0.1]"
        width={150}
        height={70}
        rotate={-20}
        delay={0.6}
      />
      <ElegantShape
        className="right-[10%] bottom-[20%]"
        gradient="from-emerald-500/[0.1]"
        width={80}
        height={40}
        rotate={15}
        delay={0.9}
      />
    </>
  );
}

function GlowingBrains() {
  return (
    <>
      {/* Glowing Brain 1 */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 1.5, delay: 0.2 }}
        className="absolute left-[10%] top-[20%] z-10"
      >
        <motion.div
          animate={{ y: [0, -15, 0] }}
          transition={{ duration: 6, repeat: Infinity, ease: "easeInOut" }}
        >
          <div className="relative">
            <div className="absolute -inset-4 bg-violet-500/20 rounded-full blur-md animate-pulse"
              style={{ animationDuration: '3s' }} />
            <Brain className="w-10 h-10 text-violet-400 drop-shadow-[0_0_8px_rgba(139,92,246,0.8)] animate-pulse"
              style={{ animationDuration: '4s' }} />
          </div>
        </motion.div>
      </motion.div>

      {/* Glowing Brain 2 */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 1.5, delay: 0.5 }}
        className="absolute right-[15%] top-[25%] z-10"
      >
        <motion.div
          animate={{ y: [0, -15, 0] }}
          transition={{ duration: 7, repeat: Infinity, ease: "easeInOut", delay: 0.5 }}
        >
          <div className="relative">
            <div className="absolute -inset-4 bg-rose-500/20 rounded-full blur-md animate-pulse"
              style={{ animationDuration: '4s' }} />
            <Brain className="w-12 h-12 text-rose-400 drop-shadow-[0_0_8px_rgba(251,113,133,0.8)] animate-pulse"
              style={{ animationDuration: '5s' }} />
          </div>
        </motion.div>
      </motion.div>

      {/* Glowing Brain 3 */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 1.5, delay: 0.8 }}
        className="absolute left-[20%] bottom-[30%] z-10"
      >
        <motion.div
          animate={{ y: [0, -15, 0] }}
          transition={{ duration: 8, repeat: Infinity, ease: "easeInOut", delay: 1 }}
        >
          <div className="relative">
            <div className="absolute -inset-4 bg-blue-500/20 rounded-full blur-md animate-pulse"
              style={{ animationDuration: '3.5s' }} />
            <Brain className="w-14 h-14 text-blue-400 drop-shadow-[0_0_8px_rgba(96,165,250,0.8)] animate-pulse"
              style={{ animationDuration: '4.5s' }} />
          </div>
        </motion.div>
      </motion.div>

      {/* Glowing Brain 4 */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 1.5, delay: 1.1 }}
        className="absolute right-[10%] bottom-[20%] z-10"
      >
        <motion.div
          animate={{ y: [0, -15, 0] }}
          transition={{ duration: 5.5, repeat: Infinity, ease: "easeInOut", delay: 1.5 }}
        >
          <div className="relative">
            <div className="absolute -inset-4 bg-emerald-500/20 rounded-full blur-md animate-pulse"
              style={{ animationDuration: '4.2s' }} />
            <Brain className="w-8 h-8 text-emerald-400 drop-shadow-[0_0_8px_rgba(52,211,153,0.8)] animate-pulse"
              style={{ animationDuration: '3.8s' }} />
          </div>
        </motion.div>
      </motion.div>

      {/* Glowing Brain 5 - Smaller one in the center */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 1.5, delay: 1.4 }}
        className="absolute left-[50%] top-[40%] z-10 -translate-x-1/2"
      >
        <motion.div
          animate={{ y: [0, -10, 0] }}
          transition={{ duration: 4, repeat: Infinity, ease: "easeInOut", delay: 0.8 }}
        >
          <div className="relative">
            <div className="absolute -inset-3 bg-indigo-500/20 rounded-full blur-md animate-pulse"
              style={{ animationDuration: '3s' }} />
            <Brain className="w-6 h-6 text-indigo-400 drop-shadow-[0_0_8px_rgba(99,102,241,0.8)] animate-pulse"
              style={{ animationDuration: '4s' }} />
          </div>
        </motion.div>
      </motion.div>
    </>
  );
}

function ElegantShape({
  className,
  delay = 0,
  width = 400,
  height = 100,
  rotate = 0,
  gradient = "from-white/[0.08]",
}: {
  className?: string;
  delay?: number;
  width?: number;
  height?: number;
  rotate?: number;
  gradient?: string;
}) {
  return (
    <motion.div
      initial={{
        opacity: 0,
        y: -150,
        rotate: rotate - 15,
      }}
      animate={{
        opacity: 1,
        y: 0,
        rotate: rotate,
      }}
      transition={{
        duration: 2.4,
        delay,
        ease: [0.23, 0.86, 0.39, 0.96],
        opacity: { duration: 1.2 },
      }}
      className={cn("absolute", className)}
    >
      <motion.div
        animate={{
          y: [0, 15, 0],
        }}
        transition={{
          duration: 12,
          repeat: Number.POSITIVE_INFINITY,
          ease: "easeInOut",
        }}
        style={{
          width,
          height,
        }}
        className="relative"
      >
        <div
          className={cn(
            "absolute inset-0 rounded-full",
            "bg-gradient-to-r to-transparent",
            gradient,
            "backdrop-blur-[2px] border-2 border-white/[0.15]",
            "shadow-[0_8px_32px_0_rgba(255,255,255,0.1)]",
            "after:absolute after:inset-0 after:rounded-full",
            "after:bg-[radial-gradient(circle_at_50%_50%,rgba(255,255,255,0.2),transparent_70%)]",
            "hover:border-white/[0.3] hover:shadow-[0_8px_32px_0_rgba(255,255,255,0.2)] transition-all duration-500"
          )}
        />
      </motion.div>
    </motion.div>
  );
}

const Index = () => {
  const { user } = useSupabaseAuth();
  const navigate = useNavigate();
  const footerRef = useRef(null);
  const isFooterInView = useInView(footerRef, { once: true, margin: "-100px" });
  const [scrolled, setScrolled] = useState(false);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);

    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const fadeUpVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        duration: 1,
        delay: 0.5 + i * 0.2,
        ease: [0.25, 0.4, 0.25, 1],
      },
    }),
  };

  const handleGetStarted = () => {
    if (user) {
      navigate('/ai');
    }
  };

  return (
    <div className="relative w-full overflow-x-hidden bg-[#030303] font-onest cursor-none">
      <Helmet>
        <title>IsotopeAI - AI-Powered Learning Platform for Students | PCMB Doubt Solver</title>
        <meta
          name="description"
          content="IsotopeAI is an all-in-one platform for students with AI-powered learning, study groups, productivity tools, and task management. Get help with Physics, Chemistry, and Mathematics."
        />
        <meta
          name="keywords"
          content="IsotopeAI, AI learning platform, PCMB doubt solver, study groups, productivity tools, task management, JEE preparation, NEET preparation, BITSAT preparation, IIT JEE Main, JEE Advanced, NEET UG, AIIMS preparation, educational AI, physics help, chemistry help, mathematics help, competitive exam preparation, entrance exam preparation, medical entrance preparation, engineering entrance preparation"
        />
        <meta property="og:title" content="IsotopeAI - AI-Powered Learning Platform for Students | PCMB Doubt Solver" />
        <meta property="og:description" content="IsotopeAI is an all-in-one platform for students with AI-powered learning, study groups, productivity tools, and task management. Get help with Physics, Chemistry, and Mathematics." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://isotopeai.com" />
        <meta name="twitter:card" content="summary_large_image" />
        <link rel="canonical" href="https://isotopeai.com" />
      </Helmet>

      {/* Custom Brain Cursor */}
      <CustomCursor />

      {/* Header */}
      <motion.header
        className={`fixed w-full z-50 transition-all duration-300 ${scrolled ? 'py-2 bg-black/80 backdrop-blur-md shadow-md' : 'py-4 bg-transparent'}`}
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className={`absolute inset-0 ${scrolled ? 'bg-gradient-to-r from-violet-950/30 via-black/80 to-indigo-950/30 backdrop-blur-md' : 'bg-gradient-to-b from-black/50 via-black/20 to-transparent backdrop-blur-sm'}`}></div>

        <div className="container mx-auto px-4 md:px-6 relative">
          <div className="flex justify-between items-center">
            {/* Logo */}
            <Link to="/" className="flex items-center space-x-2 group">
              <div className={`relative ${scrolled ? 'w-8 h-8' : 'w-10 h-10'} transition-all duration-300`}>
                <div className="absolute inset-0 bg-gradient-to-br from-violet-500/20 to-indigo-500/20 rounded-full blur-md group-hover:opacity-100 opacity-0 transition-opacity"></div>
                <img
                  src="/icon-192x192.png"
                  alt="IsotopeAI Logo"
                  className="w-full h-full rounded-full border border-white/10 shadow-lg relative z-10 group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <span className={`font-bold ${scrolled ? 'text-lg sm:text-xl' : 'text-xl sm:text-2xl'} bg-clip-text text-transparent bg-gradient-to-r from-white to-white/70 transition-all duration-300`}>
                IsotopeAI
              </span>
            </Link>

            {/* Navigation - Desktop */}
            <div className="hidden md:flex items-center space-x-8">
              <motion.div className="flex space-x-1">
                {[
                  { name: 'AI Assistant', path: '/ai-landing' },
                  { name: 'Study Groups', path: '/groups-landing' },
                  { name: 'Productivity', path: '/productivity-landing' },
                  { name: 'Tasks', path: '/tasks-landing' },
                  { name: 'Analytics', path: '/analytics-landing' },
                  { name: 'Mock Tests', path: '/mocktest-landing' },
                  { name: 'About Us', path: '/about-us' },
                  { name: 'Contact', path: '/contact-us' },
                ].map((item, index) => (
                  <motion.div
                    key={item.name}
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 * index }}
                  >
                    <Link
                      to={item.path}
                      className="text-white/70 hover:text-white font-medium text-sm transition-colors duration-200 px-4 py-2 rounded-full hover:bg-white/5"
                    >
                      {item.name}
                    </Link>
                  </motion.div>
                ))}
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.5 }}
                >
                  <a
                    href="https://isotopeai.featurebase.app/changelog"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-white/70 hover:text-white font-medium text-sm transition-colors duration-200 px-4 py-2 rounded-full hover:bg-white/5"
                  >
                    Changelog
                  </a>
                </motion.div>
              </motion.div>
            </div>

            {/* Auth Buttons and Mobile Menu */}
            <div className="flex items-center space-x-4">
              {user ? (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.6 }}
                  className="hidden md:block"
                >

                </motion.div>
              ) : (
                <div className="hidden md:block"></div>
              )}
              <div className="relative">
                <MobileMenu />
              </div>
            </div>
          </div>
        </div>

        {/* Glowing border at the bottom */}
        <div className="h-px w-full bg-gradient-to-r from-transparent via-white/20 to-transparent"></div>
      </motion.header>

      {/* Floating decorative elements */}
      <GlowingBrains />

      {/* Hero Section */}
      <div className="relative min-h-screen flex flex-col items-center justify-center overflow-hidden py-16 md:py-24 pt-28 md:pt-32">
        <GlowingBackground />

        <div className="relative z-10 container mx-auto px-4 md:px-6">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.2 }}
              className="mb-6 inline-flex items-center gap-2 rounded-full bg-white/[0.08] px-4 py-2 text-sm text-white/70 ring-1 ring-white/[0.12] backdrop-blur-md shadow-lg"
            >
              <Sparkles className="h-4 w-4 text-violet-400" />
              <span className="font-medium">Your AI-powered PCMB companion</span>
              <span className="ml-2 flex h-5 items-center justify-center rounded-full bg-green-500/20 px-2 text-xs font-medium text-green-400 ring-1 ring-inset ring-green-500/30">100% Free</span>
            </motion.div>

            <motion.div custom={1} variants={fadeUpVariants} initial="hidden" animate="visible">
              <h1 className="text-4xl sm:text-6xl md:text-7xl font-bold mb-6 md:mb-8 tracking-tight leading-tight">
                <span className="bg-clip-text text-transparent bg-gradient-to-b from-white to-white/80">
                  Ace Your Exams
                </span>
                <br />
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.5 }}
                  className="mt-2 relative inline-flex"
                >
                  <span className="text-white/80 mr-3">With</span>
                  <div className="relative inline-block">
                    <div className="absolute -inset-1 bg-gradient-to-r from-emerald-400/30 via-violet-400/30 to-rose-400/30 rounded-lg blur-lg opacity-70 group-hover:opacity-100 transition duration-1000 animate-pulse"></div>
                    <motion.span
                      className="relative bg-clip-text text-transparent bg-gradient-to-r from-emerald-400 via-violet-400 to-rose-400 px-1"
                      initial={{ backgroundPosition: "0% 50%" }}
                      animate={{ backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"] }}
                      transition={{ duration: 6, repeat: Infinity, ease: "linear" }}
                    >
                      IsotopeAI
                    </motion.span>
                    <motion.span
                      className="absolute -right-2 -top-6 transform rotate-12"
                      initial={{ rotate: -15, scale: 0.8, opacity: 0 }}
                      animate={{ rotate: 12, scale: 1, opacity: 1 }}
                      transition={{ duration: 0.6, delay: 1.5 }}
                    >
                      <Star className="h-8 w-8 text-yellow-400 fill-yellow-400 filter drop-shadow-[0_0_3px_rgba(250,204,21,0.5)]" />
                    </motion.span>
                  </div>
                </motion.div>
              </h1>
            </motion.div>

            <motion.div custom={2} variants={fadeUpVariants} initial="hidden" animate="visible">
              <p className="text-base sm:text-lg md:text-xl text-white/60 mb-8 leading-relaxed font-light tracking-wide max-w-2xl mx-auto px-4">
                Your complete learning ecosystem with <span className="text-violet-400 font-medium">AI-powered tutoring</span>, <span className="text-rose-400 font-medium">collaborative study groups</span>, and <span className="text-emerald-400 font-medium">productivity tools</span> designed to help you excel in Physics, Chemistry, and Mathematics.
              </p>
            </motion.div>

            <motion.div
              custom={3}
              variants={fadeUpVariants}
              initial="hidden"
              animate="visible"
              className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-10 w-full"
            >
              {user ? (
                <Button
                  onClick={() => navigate('/ai')}
                  className="relative group bg-white/10 hover:bg-white/20 text-white py-6 px-8 rounded-lg text-lg font-semibold flex items-center gap-3 mx-auto overflow-hidden"
                >
                  <span className="relative z-10">Continue to Website</span>
                  <ArrowRight className="w-5 h-5 relative z-10 group-hover:translate-x-1 transition-transform" />
                  <div className="absolute inset-0 bg-gradient-to-r from-violet-600/40 to-rose-600/40 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </Button>
              ) : (
                <div className="w-full sm:max-w-lg md:max-w-xl lg:max-w-2xl">
                  <SignIn />
                  <div className="mt-4 flex items-center justify-center gap-2 text-white/40 text-sm">
                    <Check className="w-4 h-4 text-green-400" /> No credit card required
                  </div>
                </div>
              )}
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.8 }}
              className="flex flex-wrap justify-center gap-8 mt-12"
            >
              <div className="flex items-center gap-2">
                <div className="w-10 h-10 rounded-full bg-violet-500/10 flex items-center justify-center">
                  <Zap className="w-5 h-5 text-violet-400" />
                </div>
                <div className="text-left">
                  <div className="text-white/80 font-medium">1000+</div>
                  <div className="text-white/40 text-sm">Questions Solved</div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <div className="w-10 h-10 rounded-full bg-rose-500/10 flex items-center justify-center">
                  <User className="w-5 h-5 text-rose-400" />
                </div>
                <div className="text-left">
                  <div className="text-white/80 font-medium">500+</div>
                  <div className="text-white/40 text-sm">Active Students</div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <div className="w-10 h-10 rounded-full bg-emerald-500/10 flex items-center justify-center">
                  <MessageSquare className="w-5 h-5 text-emerald-400" />
                </div>
                <div className="text-left">
                  <div className="text-white/80 font-medium">50+</div>
                  <div className="text-white/40 text-sm">Study Groups</div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Decorative Separator */}
      <div className="relative py-6">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-white/[0.08]" />
        </div>
        <div className="relative flex justify-center">
          <span className="bg-[#030303] px-4">
            <div className="w-3 h-3 rounded-full bg-white/[0.08] transform rotate-45" />
          </span>
        </div>
      </div>

      {/* How it Works Section */}
      <div className="relative py-16 md:py-24 overflow-hidden">
        {/* Background gradient orb */}
        <div className="absolute -left-20 top-1/3 w-64 h-64 bg-violet-500/10 rounded-full blur-3xl" />
        <div className="absolute -right-20 bottom-1/3 w-64 h-64 bg-rose-500/10 rounded-full blur-3xl" />

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <div className="inline-flex items-center mb-3 bg-white/5 px-3 py-1 rounded-full text-white/60 text-sm border border-white/[0.08]">
              <span className="mr-2">🇮🇳</span> Built by a student, for the students
            </div>
            <h2 className="text-3xl md:text-5xl font-bold text-white/90 mb-4">Start Learning in Minutes</h2>
            <p className="text-white/60 max-w-2xl mx-auto text-lg">
              Get started with IsotopeAI in three simple steps - no complicated setup required
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="relative group h-full"
            >
              <div className="p-8 rounded-2xl bg-gradient-to-br from-orange-500/20 to-orange-600/10 border border-orange-500/20 transition-all duration-500 hover:bg-orange-500/[0.15] hover:border-orange-500/[0.30] h-full flex flex-col">
                <div className="w-16 h-16 rounded-2xl bg-orange-500/20 flex items-center justify-center mb-6 border border-orange-500/30">
                  <span className="text-2xl font-bold text-orange-400">1</span>
                </div>
                <h3 className="text-2xl font-semibold text-white/90 mb-4">Sign Up</h3>
                <p className="text-white/60 mb-6 flex-grow">
                  Create a free account to unlock all features. No credit card required, just your email or Google account.
                </p>
                <div className="flex items-center mt-auto text-orange-400 font-medium">
                  <span>Get Started</span>
                  <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="relative group h-full"
            >
              <div className="p-8 rounded-2xl bg-gradient-to-br from-slate-200/20 to-slate-300/10 border border-slate-200/20 transition-all duration-500 hover:bg-slate-200/[0.15] hover:border-slate-200/[0.30] h-full flex flex-col">
                <div className="w-16 h-16 rounded-2xl bg-slate-200/20 flex items-center justify-center mb-6 border border-slate-200/30">
                  <span className="text-2xl font-bold text-slate-300">2</span>
                </div>
                <h3 className="text-2xl font-semibold text-white/90 mb-4">Ask Questions</h3>
                <p className="text-white/60 mb-6 flex-grow">
                  Get instant help with your PCMB doubts with detailed step-by-step solutions from our AI tutor.
                </p>
                <div className="flex items-center mt-auto text-slate-300 font-medium">
                  <span>Learn More</span>
                  <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="relative group h-full"
            >
              <div className="p-8 rounded-2xl bg-gradient-to-br from-green-600/20 to-green-700/10 border border-green-600/20 transition-all duration-500 hover:bg-green-600/[0.15] hover:border-green-600/[0.30] h-full flex flex-col">
                <div className="w-16 h-16 rounded-2xl bg-green-600/20 flex items-center justify-center mb-6 border border-green-600/30">
                  <span className="text-2xl font-bold text-green-500">3</span>
                </div>
                <h3 className="text-2xl font-semibold text-white/90 mb-4">Excel Together</h3>
                <p className="text-white/60 mb-6 flex-grow">
                  Join study groups, share resources and collaborate with peers preparing for the same exams.
                </p>
                <div className="flex items-center mt-auto text-green-500 font-medium">
                  <span>Join Community</span>
                  <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                </div>
              </div>
            </motion.div>
          </div>

          {/* CTA Button */}
          <div className="mt-12"></div>
        </div>
      </div>

      {/* Decorative Separator with Gradient Background */}
      <div className="relative py-10">
        <div className="absolute inset-0 bg-gradient-to-r from-violet-500/5 via-transparent to-rose-500/5" />
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-white/[0.08]" />
        </div>
        <div className="relative flex justify-center">
          <span className="bg-[#030303] px-4">
            <div className="w-3 h-3 rounded-full bg-white/[0.08] transform rotate-45" />
          </span>
        </div>
      </div>

      {/* Feature Landing Pages Section */}
      <div className="relative py-16 md:py-24 overflow-hidden">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <div className="inline-flex items-center mb-3 bg-white/5 px-3 py-1 rounded-full text-white/60 text-sm border border-white/[0.08]">
              <span className="mr-2">✨</span> All-In-One Platform
            </div>
            <h2 className="text-3xl md:text-5xl font-bold text-white/90 mb-4">Powerful Features for PCMB Students</h2>
            <p className="text-white/60 max-w-2xl mx-auto text-lg">
              Discover how our complete learning ecosystem empowers you to excel in your studies
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="group"
            >
              <Link to="/ai-landing" className="block h-full">
                <div className="p-8 rounded-2xl bg-gradient-to-br from-violet-500/10 to-transparent border border-white/[0.08] transition-all duration-500 hover:bg-white/[0.05] hover:border-violet-500/30 h-full flex flex-col relative overflow-hidden">
                  {/* Hover gradient effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-violet-600/20 to-violet-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none" />

                  <div className="flex items-center gap-4 mb-6 relative z-10">
                    <div className="w-14 h-14 rounded-2xl bg-violet-500/10 flex items-center justify-center border border-violet-500/20">
                      <Brain className="w-7 h-7 text-violet-400" />
                    </div>
                    <h3 className="text-2xl font-semibold text-white/90">AI Assistant</h3>
                  </div>
                  <p className="text-white/60 mb-6 flex-grow">
                    Get instant step-by-step solutions for any Physics, Chemistry, or Math problem.
                    Upload images, handwritten notes, or type your questions for detailed explanations.
                  </p>
                  <ul className="space-y-3 mb-6">
                    <li className="flex items-start gap-2">
                      <Check className="w-5 h-5 text-violet-400 shrink-0 mt-0.5" />
                      <span className="text-white/60 text-sm">Text & image-based questions</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Check className="w-5 h-5 text-violet-400 shrink-0 mt-0.5" />
                      <span className="text-white/60 text-sm">Conceptual explanations</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Check className="w-5 h-5 text-violet-400 shrink-0 mt-0.5" />
                      <span className="text-white/60 text-sm">Unlimited 24/7 help</span>
                    </li>
                  </ul>
                  <div className="flex items-center text-violet-400 group-hover:translate-x-1 transition-transform">
                    Learn more <ArrowRight className="w-4 h-4 ml-2" />
                  </div>
                </div>
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="group"
            >
              <Link to="/groups-landing" className="block h-full">
                <div className="p-8 rounded-2xl bg-gradient-to-br from-rose-500/10 to-transparent border border-white/[0.08] transition-all duration-500 hover:bg-white/[0.05] hover:border-rose-500/30 h-full flex flex-col relative overflow-hidden">
                  {/* Hover gradient effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-rose-600/20 to-rose-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none" />

                  <div className="flex items-center gap-4 mb-6 relative z-10">
                    <div className="w-14 h-14 rounded-2xl bg-rose-500/10 flex items-center justify-center border border-rose-500/20">
                      <MessageSquare className="w-7 h-7 text-rose-400" />
                    </div>
                    <h3 className="text-2xl font-semibold text-white/90">Study Groups</h3>
                  </div>
                  <p className="text-white/60 mb-6 flex-grow">
                    Connect with fellow students, create or join subject-specific groups,
                    and collaborate effectively to master challenging concepts together.
                  </p>
                  <ul className="space-y-3 mb-6">
                    <li className="flex items-start gap-2">
                      <Check className="w-5 h-5 text-rose-400 shrink-0 mt-0.5" />
                      <span className="text-white/60 text-sm">Collaborative learning</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Check className="w-5 h-5 text-rose-400 shrink-0 mt-0.5" />
                      <span className="text-white/60 text-sm">Resource sharing</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Check className="w-5 h-5 text-rose-400 shrink-0 mt-0.5" />
                      <span className="text-white/60 text-sm">Group progress tracking</span>
                    </li>
                  </ul>
                  <div className="flex items-center text-rose-400 group-hover:translate-x-1 transition-transform">
                    Learn more <ArrowRight className="w-4 h-4 ml-2" />
                  </div>
                </div>
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="group"
            >
              <Link to="/productivity-landing" className="block h-full">
                <div className="p-8 rounded-2xl bg-gradient-to-br from-emerald-500/10 to-transparent border border-white/[0.08] transition-all duration-500 hover:bg-white/[0.05] hover:border-emerald-500/30 h-full flex flex-col relative overflow-hidden">
                  {/* Hover gradient effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-emerald-600/20 to-emerald-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none" />

                  <div className="flex items-center gap-4 mb-6 relative z-10">
                    <div className="w-14 h-14 rounded-2xl bg-emerald-500/10 flex items-center justify-center border border-emerald-500/20">
                      <Clock className="w-7 h-7 text-emerald-400" />
                    </div>
                    <h3 className="text-2xl font-semibold text-white/90">Productivity Tools</h3>
                  </div>
                  <p className="text-white/60 mb-6 flex-grow">
                    Boost your study efficiency with our powerful productivity tools. Track progress,
                    manage time effectively, and visualize your performance improvements.
                  </p>
                  <ul className="space-y-3 mb-6">
                    <li className="flex items-start gap-2">
                      <Check className="w-5 h-5 text-emerald-400 shrink-0 mt-0.5" />
                      <span className="text-white/60 text-sm">Pomodoro timer</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Check className="w-5 h-5 text-emerald-400 shrink-0 mt-0.5" />
                      <span className="text-white/60 text-sm">Progress analytics</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Check className="w-5 h-5 text-emerald-400 shrink-0 mt-0.5" />
                      <span className="text-white/60 text-sm">Study pattern insights</span>
                    </li>
                  </ul>
                  <div className="flex items-center text-emerald-400 group-hover:translate-x-1 transition-transform">
                    Learn more <ArrowRight className="w-4 h-4 ml-2" />
                  </div>
                </div>
              </Link>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="group"
            >
              <Link to="/tasks-landing" className="block h-full">
                <div className="p-8 rounded-2xl bg-gradient-to-br from-blue-500/10 to-transparent border border-white/[0.08] transition-all duration-500 hover:bg-white/[0.05] hover:border-blue-500/30 h-full flex flex-col relative overflow-hidden">
                  {/* Hover gradient effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-blue-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none" />

                  <div className="flex items-center gap-4 mb-6 relative z-10">
                    <div className="w-14 h-14 rounded-2xl bg-blue-500/10 flex items-center justify-center border border-blue-500/20">
                      <ListTodo className="w-7 h-7 text-blue-400" />
                    </div>
                    <h3 className="text-2xl font-semibold text-white/90">Task Management</h3>
                  </div>
                  <p className="text-white/60 mb-6 flex-grow">
                    Never miss a deadline with our comprehensive task management system. Organize assignments,
                    prioritize work, and keep track of all your academic responsibilities.
                  </p>
                  <ul className="space-y-3 mb-6">
                    <li className="flex items-start gap-2">
                      <Check className="w-5 h-5 text-blue-400 shrink-0 mt-0.5" />
                      <span className="text-white/60 text-sm">Kanban board organization</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Check className="w-5 h-5 text-blue-400 shrink-0 mt-0.5" />
                      <span className="text-white/60 text-sm">Priority-based sorting</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <Check className="w-5 h-5 text-blue-400 shrink-0 mt-0.5" />
                      <span className="text-white/60 text-sm">Deadline notifications</span>
                    </li>
                  </ul>
                  <div className="flex items-center text-blue-400 group-hover:translate-x-1 transition-transform">
                    Learn more <ArrowRight className="w-4 h-4 ml-2" />
                  </div>
                </div>
              </Link>
            </motion.div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="mt-12 text-center"
          >
            <Button
              asChild
              variant="outline"
              className="bg-white/5 hover:bg-white/10 border-white/10 hover:border-white/20 text-white"
            >
              <Link to="https://isotopeai.featurebase.app/changelog">
                View Our Changelog <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </motion.div>
        </div>
      </div>

      {/* Decorative Separator */}
      <div className="relative py-6">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-white/[0.08]" />
        </div>
        <div className="relative flex justify-center">
          <span className="bg-[#030303] px-4">
            <div className="w-3 h-3 rounded-full bg-white/[0.08] transform rotate-45" />
          </span>
        </div>
      </div>

      {/* Showcase Sections */}
      {/* AI Section */}
      <div className="relative py-16 md:py-24 overflow-hidden">
        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-violet-500/10 rounded-full blur-3xl opacity-60" />

        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="relative"
            >
              <div className="relative rounded-2xl overflow-hidden border border-violet-500/20 shadow-[0_0_25px_rgba(124,58,237,0.1)]">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-violet-500/20 to-fuchsia-500/20 blur-sm"></div>
                <div className="relative rounded-2xl overflow-hidden">
                  <img
                    src="/ai-showcase.png"
                    alt="AI Interface"
                    className="w-full h-auto"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />

                  {/* Feature annotation badges */}
                  <div className="absolute top-6 right-6 bg-violet-500/90 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
                    Image Upload
                  </div>
                  <div className="absolute bottom-24 left-6 bg-indigo-500/90 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
                    Step-by-Step Solutions
                  </div>
                  <div className="absolute bottom-12 right-8 bg-fuchsia-500/90 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
                    Conceptual Explanations
                  </div>
                </div>
              </div>

              {/* Floating label */}
              <div className="absolute -top-5 -left-5 bg-violet-500/90 text-white px-4 py-2 rounded-lg font-semibold shadow-lg">
                AI-Powered Learning
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="space-y-6"
            >
              <div className="inline-flex items-center bg-violet-500/10 px-3 py-1 rounded-full text-violet-400 text-sm border border-violet-500/20">
                <Brain className="h-4 w-4 mr-2" /> AI-Powered Learning
              </div>

              <h2 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-violet-400 to-indigo-300">
                Get Instant Help with Any PCMB Question
              </h2>

              <p className="text-xl text-white/60">
                Struggling with a tough physics problem or complex chemistry equation? Our advanced AI tutor provides instant,
                detailed solutions customized for competitive exams like JEE and NEET.
              </p>

              <ul className="space-y-4">
                {[
                  "Step-by-step solutions with detailed explanations",
                  "Support for text and image-based questions",
                  "24/7 availability for instant help",
                  "Conceptual clarity for difficult topics",
                ].map((feature, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <div className="h-6 w-6 mt-1 rounded-full bg-violet-500/10 flex items-center justify-center border border-violet-500/20">
                      <Check className="h-3 w-3 text-violet-400" />
                    </div>
                    <span className="text-white/80">{feature}</span>
                  </li>
                ))}
              </ul>

              {/* CTA button */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                <Button
                  asChild
                  className="mt-4 bg-violet-500/80 hover:bg-violet-600/90 text-white relative overflow-hidden group"
                >
                  <Link to="/ai-landing">
                    <span className="relative z-10 flex items-center">
                      Explore AI Assistant
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </span>
                    <span className="absolute inset-0 bg-gradient-to-r from-violet-600 to-indigo-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </Link>
                </Button>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Productivity Section */}
      <div className="relative py-16 md:py-24 overflow-hidden bg-white/[0.02]">
        {/* Background decoration */}
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-rose-500/10 rounded-full blur-3xl opacity-60" />

        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="space-y-6 lg:order-2"
            >
              <div className="inline-flex items-center bg-rose-500/10 px-3 py-1 rounded-full text-rose-400 text-sm border border-rose-500/20">
                <Clock className="h-4 w-4 mr-2" /> Productivity Tools
              </div>

              <h2 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-rose-400 to-orange-300">
                Maximize Your Study Efficiency
              </h2>

              <p className="text-xl text-white/60">
                Stay focused and motivated with our suite of productivity tools designed specifically for PCMB students.
                Track your progress, maintain focus, and visualize your improvement over time.
              </p>

              <ul className="space-y-4">
                {[
                  "Customizable Pomodoro timer for focused study sessions",
                  "Detailed analytics to track your learning progress",
                  "Visual insights into your study patterns",
                  "Performance tracking across subjects and topics",
                ].map((feature, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <div className="h-6 w-6 mt-1 rounded-full bg-rose-500/10 flex items-center justify-center border border-rose-500/20">
                      <Check className="h-3 w-3 text-rose-400" />
                    </div>
                    <span className="text-white/80">{feature}</span>
                  </li>
                ))}
              </ul>

              {/* CTA button */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                <Button
                  asChild
                  className="mt-4 bg-rose-500/80 hover:bg-rose-600/90 text-white relative overflow-hidden group"
                >
                  <Link to="/productivity-landing">
                    <span className="relative z-10 flex items-center">
                      Explore Productivity Tools
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </span>
                    <span className="absolute inset-0 bg-gradient-to-r from-rose-600 to-orange-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </Link>
                </Button>
              </motion.div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="relative lg:order-1"
            >
              <div className="relative rounded-2xl overflow-hidden border border-rose-500/20 shadow-[0_0_25px_rgba(244,63,94,0.1)]">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-rose-500/20 to-orange-500/20 blur-sm"></div>
                <div className="relative rounded-2xl overflow-hidden">
                  <img
                    src="/productivity-showcase.png"
                    alt="Productivity Tools"
                    className="w-full h-auto"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />

                  {/* Feature annotation badges */}
                  <div className="absolute top-6 left-6 bg-rose-500/90 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
                    Pomodoro Timer
                  </div>
                  <div className="absolute bottom-24 right-6 bg-orange-500/90 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
                    Progress Analytics
                  </div>
                  <div className="absolute bottom-12 left-8 bg-amber-500/90 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
                    Study Insights
                  </div>
                </div>
              </div>

              {/* Floating label */}
              <div className="absolute -top-5 -right-5 bg-rose-500/90 text-white px-4 py-2 rounded-lg font-semibold shadow-lg">
                Boost Productivity
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Tasks Section */}
      <div className="relative py-16 md:py-24 overflow-hidden">
        {/* Background decoration */}
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl opacity-60" />

        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="relative"
            >
              <div className="relative rounded-2xl overflow-hidden border border-blue-500/20 shadow-[0_0_25px_rgba(59,130,246,0.1)]">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 blur-sm"></div>
                <div className="relative rounded-2xl overflow-hidden">
                  <img
                    src="/tasks-showcase.png"
                    alt="Task Management"
                    className="w-full h-auto"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />

                  {/* Feature annotation badges */}
                  <div className="absolute top-6 right-6 bg-blue-500/90 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
                    Kanban Board
                  </div>
                  <div className="absolute bottom-24 left-6 bg-sky-500/90 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
                    Priority Management
                  </div>
                  <div className="absolute bottom-12 right-8 bg-cyan-500/90 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
                    Deadline Tracking
                  </div>
                </div>
              </div>

              {/* Floating label */}
              <div className="absolute -top-5 -left-5 bg-blue-500/90 text-white px-4 py-2 rounded-lg font-semibold shadow-lg">
                Task Management
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="space-y-6"
            >
              <div className="inline-flex items-center bg-blue-500/10 px-3 py-1 rounded-full text-blue-400 text-sm border border-blue-500/20">
                <ListTodo className="h-4 w-4 mr-2" /> Task Management
              </div>

              <h2 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-cyan-300">
                Never Miss a Deadline Again
              </h2>

              <p className="text-xl text-white/60">
                Stay organized with our powerful task management system designed for students. Track assignments,
                manage deadlines, and prioritize your academic responsibilities effectively.
              </p>

              <ul className="space-y-4">
                {[
                  "Flexible Kanban board for visual task organization",
                  "Priority-based task management with due dates",
                  "Filter and sort tasks by status, priority, and deadlines",
                  "Track overdue tasks and upcoming deadlines",
                  "Daily study plan generation based on your priorities",
                ].map((feature, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <div className="h-6 w-6 mt-1 rounded-full bg-blue-500/10 flex items-center justify-center border border-blue-500/20">
                      <Check className="h-3 w-3 text-blue-400" />
                    </div>
                    <span className="text-white/80">{feature}</span>
                  </li>
                ))}
              </ul>

              {/* CTA button */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                <Button
                  asChild
                  className="mt-4 bg-blue-500/80 hover:bg-blue-600/90 text-white relative overflow-hidden group"
                >
                  <Link to="/tasks-landing">
                    <span className="relative z-10 flex items-center">
                      Explore Task Management
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </span>
                    <span className="absolute inset-0 bg-gradient-to-r from-blue-600 to-cyan-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </Link>
                </Button>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Groups Section */}
      <div className="relative py-16 md:py-24 overflow-hidden bg-white/[0.02]">
        {/* Background decoration */}
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-emerald-500/10 rounded-full blur-3xl opacity-60" />

        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="space-y-6 lg:order-2"
            >
              <div className="inline-flex items-center bg-emerald-500/10 px-3 py-1 rounded-full text-emerald-400 text-sm border border-emerald-500/20">
                <MessageSquare className="h-4 w-4 mr-2" /> Study Groups
              </div>

              <h2 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-emerald-400 to-teal-300">
                Learn Better Together
              </h2>

              <p className="text-xl text-white/60">
                Join forces with fellow students to tackle challenging concepts and achieve your academic goals together.
                Create or join subject-specific study groups and collaborate effectively.
              </p>

              <ul className="space-y-4">
                {[
                  "Create or join subject-specific study groups",
                  "Track group progress and achievements",
                  "Share resources and collaborate in real-time",
                  "Organize virtual study sessions with peers",
                  "Get help from high-performing students in your subjects",
                ].map((feature, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <div className="h-6 w-6 mt-1 rounded-full bg-emerald-500/10 flex items-center justify-center border border-emerald-500/20">
                      <Check className="h-3 w-3 text-emerald-400" />
                    </div>
                    <span className="text-white/80">{feature}</span>
                  </li>
                ))}
              </ul>

              {/* CTA button */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                <Button
                  asChild
                  className="mt-4 bg-emerald-500/80 hover:bg-emerald-600/90 text-white relative overflow-hidden group"
                >
                  <Link to="/groups-landing">
                    <span className="relative z-10 flex items-center">
                      Explore Study Groups
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </span>
                    <span className="absolute inset-0 bg-gradient-to-r from-emerald-600 to-teal-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </Link>
                </Button>
              </motion.div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="relative lg:order-1"
            >
              <div className="relative rounded-2xl overflow-hidden border border-emerald-500/20 shadow-[0_0_25px_rgba(16,185,129,0.1)]">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 blur-sm"></div>
                <div className="relative rounded-2xl overflow-hidden">
                  <img
                    src="/groups-showcase.png"
                    alt="Study Groups"
                    className="w-full h-auto"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />

                  {/* Feature annotation badges */}
                  <div className="absolute top-6 left-6 bg-emerald-500/90 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
                    Collaborative Learning
                  </div>
                  <div className="absolute bottom-24 right-6 bg-teal-500/90 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
                    Resource Sharing
                  </div>
                  <div className="absolute bottom-12 left-8 bg-green-500/90 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
                    Group Progress
                  </div>
                </div>
              </div>

              {/* Floating label */}
              <div className="absolute -top-5 -right-5 bg-emerald-500/90 text-white px-4 py-2 rounded-lg font-semibold shadow-lg">
                Collaborative Study
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Analytics Section */}
      <div className="relative py-16 md:py-24 overflow-hidden">
        {/* Background decoration */}
        <div className="absolute top-0 right-1/3 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl opacity-60" />

        <div className="container mx-auto px-4 md:px-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="relative"
            >
              <div className="relative rounded-2xl overflow-hidden border border-purple-500/20 shadow-[0_0_25px_rgba(147,51,234,0.1)]">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-purple-500/20 to-violet-500/20 blur-sm"></div>
                <div className="relative rounded-2xl overflow-hidden">
                  <img
                    src="/analytics-showcase.png"
                    alt="Analytics Dashboard"
                    className="w-full h-auto"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = "https://placehold.co/600x400/0a0a2a/white?text=Analytics+Dashboard";
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />

                  {/* Feature annotation badges */}
                  <div className="absolute top-6 right-6 bg-purple-500/90 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
                    Progress Tracking
                  </div>
                  <div className="absolute bottom-24 left-6 bg-violet-500/90 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
                    Study Patterns
                  </div>
                  <div className="absolute bottom-12 right-8 bg-indigo-500/90 text-white text-xs font-medium px-3 py-1 rounded-full shadow-lg">
                    Performance Insights
                  </div>
                </div>
              </div>

              {/* Floating label */}
              <div className="absolute -top-5 -left-5 bg-purple-500/90 text-white px-4 py-2 rounded-lg font-semibold shadow-lg">
                Data-Driven Learning
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
              className="space-y-6"
            >
              <div className="inline-flex items-center bg-purple-500/10 px-3 py-1 rounded-full text-purple-400 text-sm border border-purple-500/20">
                <BarChart2 className="h-4 w-4 mr-2" /> Study Analytics
              </div>

              <h2 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-400 to-violet-300">
                Understand Your Learning Patterns
              </h2>

              <p className="text-xl text-white/60">
                Transform your study habits with comprehensive analytics. Track progress, identify patterns,
                and make data-driven decisions to optimize your learning efficiency.
              </p>

              <ul className="space-y-4">
                {[
                  "Detailed progress tracking across all subjects",
                  "Visual insights into your study patterns and habits",
                  "Performance metrics and improvement suggestions",
                  "Goal setting and achievement tracking",
                  "Time allocation analysis and optimization tips",
                ].map((feature, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <div className="h-6 w-6 mt-1 rounded-full bg-purple-500/10 flex items-center justify-center border border-purple-500/20">
                      <Check className="h-3 w-3 text-purple-400" />
                    </div>
                    <span className="text-white/80">{feature}</span>
                  </li>
                ))}
              </ul>

              {/* CTA button */}
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                <Button
                  asChild
                  className="mt-4 bg-purple-500/80 hover:bg-purple-600/90 text-white relative overflow-hidden group"
                >
                  <Link to="/analytics-landing">
                    <span className="relative z-10 flex items-center">
                      Explore Analytics
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </span>
                    <span className="absolute inset-0 bg-gradient-to-r from-purple-600 to-violet-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </Link>
                </Button>
              </motion.div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Stats Section with Enhanced Design */}
      <div className="relative py-16 md:py-24 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/[0.03] to-transparent" />
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <div className="inline-flex items-center mb-3 bg-white/5 px-3 py-1 rounded-full text-white/60 text-sm border border-white/[0.08]">
              <span className="mr-2">📊</span> Our Impact
            </div>
            <h2 className="text-3xl md:text-5xl font-bold text-white/90 mb-4">Trusted by Students Across India</h2>
            <p className="text-white/60 max-w-2xl mx-auto text-lg">
              Join thousands of students who are already using IsotopeAI to excel in their studies
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="relative group h-full"
            >
              <div className="text-center p-8 rounded-2xl bg-gradient-to-br from-violet-500/10 to-transparent border border-white/[0.08] transition-all duration-500 hover:bg-white/[0.04] hover:border-violet-500/30 h-full flex flex-col justify-center">
                <div className="w-16 h-16 rounded-full bg-violet-500/10 flex items-center justify-center mb-4 mx-auto border border-violet-500/20">
                  <Zap className="w-8 h-8 text-violet-400" />
                </div>
                <h3 className="text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-violet-400 to-indigo-300 mb-2">1000+</h3>
                <p className="text-white/60">Questions Solved Daily</p>
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="relative group h-full"
            >
              <div className="text-center p-8 rounded-2xl bg-gradient-to-br from-rose-500/10 to-transparent border border-white/[0.08] transition-all duration-500 hover:bg-white/[0.04] hover:border-rose-500/30 h-full flex flex-col justify-center">
                <div className="w-16 h-16 rounded-full bg-rose-500/10 flex items-center justify-center mb-4 mx-auto border border-rose-500/20">
                  <User className="w-8 h-8 text-rose-400" />
                </div>
                <h3 className="text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-rose-400 to-orange-300 mb-2">500+</h3>
                <p className="text-white/60">Active Students</p>
              </div>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="relative group h-full"
            >
              <div className="text-center p-8 rounded-2xl bg-gradient-to-br from-emerald-500/10 to-transparent border border-white/[0.08] transition-all duration-500 hover:bg-white/[0.04] hover:border-emerald-500/30 h-full flex flex-col justify-center">
                <div className="w-16 h-16 rounded-full bg-emerald-500/10 flex items-center justify-center mb-4 mx-auto border border-emerald-500/20">
                  <MessageSquare className="w-8 h-8 text-emerald-400" />
                </div>
                <h3 className="text-5xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-emerald-400 to-teal-300 mb-2">50+</h3>
                <p className="text-white/60">Study Groups</p>
              </div>
            </motion.div>
          </div>

          {/* Final CTA */}
          {!user && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="mt-16 text-center"
            >
              <div className="p-8 rounded-2xl bg-gradient-to-r from-violet-500/10 via-indigo-500/10 to-rose-500/10 backdrop-blur-sm border border-white/10 max-w-4xl mx-auto">
                <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">Ready to Excel in PCMB?</h3>
                <p className="text-white/60 text-lg mb-8 max-w-2xl mx-auto">
                  Join IsotopeAI today and transform your learning experience with our comprehensive suite of tools designed for students.
                </p>
                <div className="w-full sm:max-w-lg md:max-w-xl lg:max-w-2xl mx-auto">
                  <SignIn />
                  <p className="text-white/40 text-sm mt-4">No credit card required • 100% free</p>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </div>

      {/* Supporters Section */}
      <SupportersSection />

      {/* Footer */}
      <motion.footer
        ref={footerRef}
        variants={fadeUpVariants}
        initial="hidden"
        animate={isFooterInView ? "visible" : "hidden"}
        custom={0}
        className="relative z-10 border-t border-white/[0.08] bg-black/20 backdrop-blur-sm"
      >
        {/* Indian Flag Tricolor */}
        <div className="w-full flex flex-row h-4 relative z-20">
          <div className="w-1/3 h-full" style={{ backgroundColor: "#FF671F" }} /> {/* India Saffron */}
          <div className="w-1/3 h-full" style={{ backgroundColor: "#FFFFFF" }} /> {/* White */}
          <div className="w-1/3 h-full" style={{ backgroundColor: "#046A38" }} /> {/* India Green */}
        </div>

        <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/50 pointer-events-none" />

        {/* Decorative glow elements */}
        <div className="absolute bottom-0 left-1/4 w-64 h-32 bg-violet-500/10 rounded-full blur-3xl opacity-40" />
        <div className="absolute bottom-0 right-1/4 w-64 h-32 bg-rose-500/10 rounded-full blur-3xl opacity-40" />

        <div className="container mx-auto px-4 md:px-6 py-12 md:py-16 relative">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-8 items-start">
            {/* Logo and description */}
            <div className="md:col-span-4 flex flex-col space-y-6">
              <div className="flex items-center space-x-3">
                <img src="/icon-192x192.png" alt="IsotopeAI Logo" className="w-12 h-12 rounded-full border border-white/10 shadow-lg" />
                <span className="font-semibold text-2xl bg-clip-text text-transparent bg-gradient-to-r from-white/90 to-white/70">
                  IsotopeAI
                </span>
              </div>

              <p className="text-white/40 max-w-md leading-relaxed">
                Your all-in-one platform for AI-powered learning, productivity tools, and collaborative study.
                We help PCMB students excel in their competitive exams through innovative technology and community support.
              </p>

              <div className="flex items-center space-x-4 pt-2">
                <a
                  href="https://www.instagram.com/isotope.ai/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-white/40 hover:text-white/90 transition-colors p-2 rounded-full hover:bg-white/5"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="lucide lucide-instagram"
                  >
                    <rect width="20" height="20" x="2" y="2" rx="5" ry="5" />
                    <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                    <path d="M17.5 6.5h.01" />
                  </svg>
                </a>
                <a
                  href="https://www.reddit.com/r/Isotope/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-white/40 hover:text-white/90 transition-colors p-2 rounded-full hover:bg-white/5"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <circle cx="12" cy="12" r="10" />
                    <circle cx="12" cy="9" r="1" />
                    <circle cx="12" cy="15" r="1" />
                    <path d="M8.5 9a2 2 0 0 0-2 2v0c0 1.1.9 2 2 2" />
                    <path d="M15.5 9a2 2 0 0 1 2 2v0c0 1.1-.9 2-2 2" />
                    <path d="M7.5 13h9" />
                    <path d="M10 16v-3" />
                    <path d="M14 16v-3" />
                  </svg>
                </a>
                <a
                  href="mailto:<EMAIL>"
                  className="text-white/40 hover:text-white/90 transition-colors p-2 rounded-full hover:bg-white/5"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <rect width="20" height="16" x="2" y="4" rx="2" />
                    <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                  </svg>
                </a>
              </div>
            </div>

            {/* Features column */}
            <div className="md:col-span-2">
              <h3 className="text-white/90 font-semibold mb-4">Features</h3>
              <ul className="space-y-2">
                <li>
                  <Link to="/ai-landing" className="text-white/50 hover:text-white/90 transition-colors">AI Assistant</Link>
                </li>
                <li>
                  <Link to="/groups-landing" className="text-white/50 hover:text-white/90 transition-colors">Study Groups</Link>
                </li>
                <li>
                  <Link to="/productivity-landing" className="text-white/50 hover:text-white/90 transition-colors">Productivity</Link>
                </li>
                <li>
                  <Link to="/tasks-landing" className="text-white/50 hover:text-white/90 transition-colors">Task Management</Link>
                </li>
                <li>
                  <Link to="/analytics-landing" className="text-white/50 hover:text-white/90 transition-colors">Analytics</Link>
                </li>
                <li>
                  <Link to="/mocktest-landing" className="text-white/50 hover:text-white/90 transition-colors">Mock Tests</Link>
                </li>
              </ul>
            </div>

            {/* Resources column */}
            <div className="md:col-span-2">
              <h3 className="text-white/90 font-semibold mb-4">Resources</h3>
              <ul className="space-y-2">
                <li>
                  <a href="https://isotopeai.featurebase.app/changelog" target="_blank" rel="noopener noreferrer" className="text-white/50 hover:text-white/90 transition-colors">Changelog</a>
                </li>
                <li>
                  <FeedbackWidget className="inline text-white/50 hover:text-white/90 transition-colors cursor-pointer" />
                </li>
                <li>
                  <a href="https://www.reddit.com/r/Isotope/" target="_blank" rel="noopener noreferrer" className="text-white/50 hover:text-white/90 transition-colors">Community</a>
                </li>
              </ul>
            </div>

            {/* Legal column */}
            <div className="md:col-span-2">
              <h3 className="text-white/90 font-semibold mb-4">Legal</h3>
              <ul className="space-y-2">
                <li>
                  <Link to="/privacy-policy" className="text-white/50 hover:text-white/90 transition-colors">Privacy Policy</Link>
                </li>
                <li>
                  <Link to="/terms-of-service" className="text-white/50 hover:text-white/90 transition-colors">Terms of Service</Link>
                </li>
                <li>
                  <Link to="/about-us" className="text-white/50 hover:text-white/90 transition-colors">About Us</Link>
                </li>
              </ul>
            </div>

            {/* Contact column */}
            <div className="md:col-span-2">
              <h3 className="text-white/90 font-semibold mb-4">Contact</h3>
              <ul className="space-y-2">
                <li>
                  <Link to="/contact-us" className="text-white/50 hover:text-white/90 transition-colors">Contact Us</Link>
                </li>
                <li>
                  <a href="mailto:<EMAIL>" className="text-white/50 hover:text-white/90 transition-colors">Email Us</a>
                </li>
                <li>
                  <a href="https://www.instagram.com/isotope.ai/" target="_blank" rel="noopener noreferrer" className="text-white/50 hover:text-white/90 transition-colors">Instagram</a>
                </li>
                <li>
                  <a href="https://www.reddit.com/r/Isotope/" target="_blank" rel="noopener noreferrer" className="text-white/50 hover:text-white/90 transition-colors">Reddit</a>
                </li>
              </ul>
            </div>
          </div>

          <div className="mt-12 pt-6 border-t border-white/[0.08] flex flex-col md:flex-row justify-between items-center">
            <p className="text-white/40 text-sm">
              © {new Date().getFullYear()} IsotopeAI. All rights reserved.
            </p>
            <div className="flex items-center gap-4 mt-2 md:mt-0">
              <Link to="/about-us" className="text-white/40 text-sm hover:text-violet-300 transition-colors">
                About Us
              </Link>
              <span className="text-white/20">|</span>
              <Link to="/privacy-policy" className="text-white/40 text-sm hover:text-violet-300 transition-colors">
                Privacy Policy
              </Link>
              <span className="text-white/20">|</span>
              <Link to="/terms-of-service" className="text-white/40 text-sm hover:text-violet-300 transition-colors">
                Terms of Service
              </Link>
              <p className="text-white/40 text-sm ml-4">
                Built with <span className="text-red-500">❤️</span> by a fellow JEEtard
              </p>
            </div>
          </div>
        </div>
      </motion.footer>
    </div>
  );
};

function MobileMenu() {
  const [isOpen, setIsOpen] = useState(false);
  const { user } = useSupabaseAuth();
  const navigate = useNavigate();

  const handleGetStarted = () => {
    if (user) {
      navigate('/ai');
    }
    setIsOpen(false);
  };

  // Close menu when clicking outside
  useEffect(() => {
    const handleOutsideClick = (e: MouseEvent) => {
      if (isOpen && e.target instanceof Element && !e.target.closest('.mobile-menu-container')) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleOutsideClick);
    return () => {
      document.removeEventListener('mousedown', handleOutsideClick);
    };
  }, [isOpen]);

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  return (
    <>
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3, delay: 0.6 }}
        className="md:hidden z-50"
      >
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="md:hidden flex items-center justify-center w-10 h-10 rounded-full bg-gradient-to-br from-violet-500/10 to-indigo-500/10 border border-white/10 text-white/80 hover:bg-white/10 transition-all duration-300 hover:scale-105 hover:shadow-lg active:scale-95"
          aria-label={isOpen ? "Close menu" : "Open menu"}
        >
          <AnimatePresence mode="wait">
            {isOpen ? (
              <motion.div
                key="close"
                initial={{ rotate: -90, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                exit={{ rotate: 90, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <X className="w-5 h-5" />
              </motion.div>
            ) : (
              <motion.div
                key="menu"
                initial={{ rotate: 90, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                exit={{ rotate: -90, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Menu className="w-5 h-5" />
              </motion.div>
            )}
          </AnimatePresence>
        </button>
      </motion.div>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, height: 0 }}
            animate={{ opacity: 1, y: 0, height: 'auto' }}
            exit={{ opacity: 0, y: -10, height: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed top-[60px] right-4 left-4 bg-black/90 backdrop-blur-xl rounded-xl border border-white/10 shadow-xl overflow-hidden mobile-menu-container md:hidden z-[100]"
          >
            <motion.div
              className="flex flex-col p-4 space-y-2"
              initial="hidden"
              animate="visible"
              variants={{
                hidden: {},
                visible: {
                  transition: {
                    staggerChildren: 0.07
                  }
                }
              }}
            >
              {[
                { name: 'AI Assistant', path: '/ai-landing', icon: <Brain className="w-4 h-4" /> },
                { name: 'Study Groups', path: '/groups-landing', icon: <MessageSquare className="w-4 h-4" /> },
                { name: 'Productivity', path: '/productivity-landing', icon: <Clock className="w-4 h-4" /> },
                { name: 'Tasks', path: '/tasks-landing', icon: <ListTodo className="w-4 h-4" /> },
                { name: 'Analytics', path: '/analytics-landing', icon: <BarChart2 className="w-4 h-4" /> },
                { name: 'Mock Tests', path: '/mocktest-landing', icon: <BarChart2 className="w-4 h-4" /> },
                { name: 'About Us', path: '/about-us', icon: <User className="w-4 h-4" /> },
                { name: 'Contact', path: '/contact-us', icon: <Mail className="w-4 h-4" /> },
              ].map((item, index) => (
                <motion.div
                  key={item.name}
                  variants={{
                    hidden: { opacity: 0, x: -20 },
                    visible: { opacity: 1, x: 0, transition: { duration: 0.2 } }
                  }}
                >
                  <Link
                    to={item.path}
                    className="text-white/80 hover:text-white font-medium px-4 py-3 rounded-lg hover:bg-white/5 transition-colors flex items-center gap-3 group"
                    onClick={() => setIsOpen(false)}
                  >
                    <span className="text-violet-400/80 group-hover:text-violet-400 transition-colors">{item.icon}</span>
                    {item.name}
                  </Link>
                </motion.div>
              ))}
              <motion.div
                variants={{
                  hidden: { opacity: 0, x: -20 },
                  visible: { opacity: 1, x: 0, transition: { duration: 0.2 } }
                }}
              >
                <a
                  href="https://isotopeai.featurebase.app/changelog"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-white/80 hover:text-white font-medium px-4 py-3 rounded-lg hover:bg-white/5 transition-colors flex items-center gap-3 group"
                  onClick={() => setIsOpen(false)}
                >
                  <span className="text-violet-400/80 group-hover:text-violet-400 transition-colors">
                    <Sparkles className="w-4 h-4" />
                  </span>
                  Changelog
                </a>
              </motion.div>

              {/* Border separator */}
              <motion.div
                className="border-t border-white/10 my-1 pt-2"
                variants={{
                  hidden: { opacity: 0 },
                  visible: { opacity: 1, transition: { duration: 0.2, delay: 0.3 } }
                }}
              ></motion.div>

              <motion.div
                variants={{
                  hidden: { opacity: 0, y: 10 },
                  visible: { opacity: 1, y: 0, transition: { duration: 0.3, delay: 0.4 } }
                }}
              >

              </motion.div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}

function SupportersSection() {
  const supporters = [
    "Samarth M Rao",
    "Utsav Lal",
    "Samarth Mishra",
    "Arnav Singh",
  ];

  return (
    <div className="relative py-16 md:py-24 overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-white/[0.03] to-transparent" />

      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center mb-3 bg-white/5 px-3 py-1 rounded-full text-white/60 text-sm border border-white/[0.08]">
            <span className="mr-2">🙏</span> Our Supporters
          </div>
          <h2 className="text-3xl md:text-5xl font-bold text-white/90 mb-4">A Special Thanks</h2>
          <p className="text-white/60 max-w-2xl mx-auto text-lg">
            We are incredibly grateful for the support of these individuals who have helped make IsotopeAI free for 2000+ students.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-8 text-center">
          {supporters.map((name, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="p-6 rounded-xl bg-gradient-to-br from-white/5 to-transparent border border-white/[0.08] shadow-lg"
            >
              <User className="w-8 h-8 text-violet-400 mx-auto mb-3" />
              <p className="text-white/90 font-semibold text-lg">{name}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default Index;
