import React, { createContext, useContext, useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import {
  Eye,
  Contrast,
  Type,
  Palette,
  Settings,
  Sun,
  Moon,
  ZoomIn,
  ZoomOut,
} from 'lucide-react';

interface AccessibilitySettings {
  highContrast: boolean;
  reducedMotion: boolean;
  largeText: boolean;
  colorBlindFriendly: boolean;
  focusIndicators: boolean;
  textScale: number;
  theme: 'light' | 'dark' | 'auto';
}

interface VisualAccessibilityContextType {
  settings: AccessibilitySettings;
  updateSetting: <K extends keyof AccessibilitySettings>(
    key: K,
    value: AccessibilitySettings[K]
  ) => void;
  resetSettings: () => void;
}

const defaultSettings: AccessibilitySettings = {
  highContrast: false,
  reducedMotion: false,
  largeText: false,
  colorBlindFriendly: false,
  focusIndicators: true,
  textScale: 100,
  theme: 'auto',
};

const VisualAccessibilityContext = createContext<VisualAccessibilityContextType | null>(null);

export function useVisualAccessibility() {
  const context = useContext(VisualAccessibilityContext);
  if (!context) {
    throw new Error('useVisualAccessibility must be used within VisualAccessibilityProvider');
  }
  return context;
}

interface VisualAccessibilityProviderProps {
  children: React.ReactNode;
}

export function VisualAccessibilityProvider({ children }: VisualAccessibilityProviderProps) {
  const [settings, setSettings] = useState<AccessibilitySettings>(() => {
    const saved = localStorage.getItem('accessibility-settings');
    return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
  });

  const updateSetting = <K extends keyof AccessibilitySettings>(
    key: K,
    value: AccessibilitySettings[K]
  ) => {
    setSettings(prev => {
      const newSettings = { ...prev, [key]: value };
      localStorage.setItem('accessibility-settings', JSON.stringify(newSettings));
      return newSettings;
    });
  };

  const resetSettings = () => {
    setSettings(defaultSettings);
    localStorage.removeItem('accessibility-settings');
  };

  // Apply accessibility settings to document
  useEffect(() => {
    const root = document.documentElement;

    // High contrast mode
    if (settings.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }

    // Reduced motion
    if (settings.reducedMotion) {
      root.classList.add('reduce-motion');
    } else {
      root.classList.remove('reduce-motion');
    }

    // Large text
    if (settings.largeText) {
      root.classList.add('large-text');
    } else {
      root.classList.remove('large-text');
    }

    // Color blind friendly
    if (settings.colorBlindFriendly) {
      root.classList.add('colorblind-friendly');
    } else {
      root.classList.remove('colorblind-friendly');
    }

    // Enhanced focus indicators
    if (settings.focusIndicators) {
      root.classList.add('enhanced-focus');
    } else {
      root.classList.remove('enhanced-focus');
    }

    // Text scale
    root.style.setProperty('--text-scale', `${settings.textScale}%`);

    // Theme
    if (settings.theme === 'light') {
      root.classList.remove('dark');
    } else if (settings.theme === 'dark') {
      root.classList.add('dark');
    } else {
      // Auto theme based on system preference
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      if (mediaQuery.matches) {
        root.classList.add('dark');
      } else {
        root.classList.remove('dark');
      }
    }
  }, [settings]);

  // Listen for system theme changes when in auto mode
  useEffect(() => {
    if (settings.theme === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = (e: MediaQueryListEvent) => {
        const root = document.documentElement;
        if (e.matches) {
          root.classList.add('dark');
        } else {
          root.classList.remove('dark');
        }
      };

      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [settings.theme]);

  return (
    <VisualAccessibilityContext.Provider
      value={{ settings, updateSetting, resetSettings }}
    >
      {children}
    </VisualAccessibilityContext.Provider>
  );
}

// Accessibility settings panel
interface AccessibilityPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

export function AccessibilityPanel({ isOpen, onClose }: AccessibilityPanelProps) {
  const { settings, updateSetting, resetSettings } = useVisualAccessibility();

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-gray-900 border border-gray-700 rounded-lg p-6 max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Accessibility Settings
          </h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            ×
          </Button>
        </div>

        <div className="space-y-6">
          {/* Visual Settings */}
          <div>
            <h3 className="text-sm font-medium text-gray-300 mb-3 flex items-center gap-2">
              <Contrast className="h-4 w-4" />
              Visual
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <label className="text-sm text-gray-400">High Contrast</label>
                <Switch
                  checked={settings.highContrast}
                  onCheckedChange={(checked) => updateSetting('highContrast', checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm text-gray-400">Enhanced Focus Indicators</label>
                <Switch
                  checked={settings.focusIndicators}
                  onCheckedChange={(checked) => updateSetting('focusIndicators', checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm text-gray-400">Color Blind Friendly</label>
                <Switch
                  checked={settings.colorBlindFriendly}
                  onCheckedChange={(checked) => updateSetting('colorBlindFriendly', checked)}
                />
              </div>
            </div>
          </div>

          {/* Text Settings */}
          <div>
            <h3 className="text-sm font-medium text-gray-300 mb-3 flex items-center gap-2">
              <Type className="h-4 w-4" />
              Text
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <label className="text-sm text-gray-400">Large Text</label>
                <Switch
                  checked={settings.largeText}
                  onCheckedChange={(checked) => updateSetting('largeText', checked)}
                />
              </div>
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="text-sm text-gray-400">Text Scale</label>
                  <span className="text-sm text-white">{settings.textScale}%</span>
                </div>
                <Slider
                  value={[settings.textScale]}
                  onValueChange={([value]) => updateSetting('textScale', value)}
                  min={75}
                  max={150}
                  step={5}
                  className="w-full"
                />
              </div>
            </div>
          </div>

          {/* Motion Settings */}
          <div>
            <h3 className="text-sm font-medium text-gray-300 mb-3">Motion</h3>
            <div className="flex items-center justify-between">
              <label className="text-sm text-gray-400">Reduce Motion</label>
              <Switch
                checked={settings.reducedMotion}
                onCheckedChange={(checked) => updateSetting('reducedMotion', checked)}
              />
            </div>
          </div>

          {/* Theme Settings */}
          <div>
            <h3 className="text-sm font-medium text-gray-300 mb-3 flex items-center gap-2">
              <Palette className="h-4 w-4" />
              Theme
            </h3>
            <div className="grid grid-cols-3 gap-2">
              {(['light', 'dark', 'auto'] as const).map((theme) => (
                <Button
                  key={theme}
                  variant={settings.theme === theme ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => updateSetting('theme', theme)}
                  className="capitalize"
                >
                  {theme === 'light' && <Sun className="h-3 w-3 mr-1" />}
                  {theme === 'dark' && <Moon className="h-3 w-3 mr-1" />}
                  {theme === 'auto' && <Settings className="h-3 w-3 mr-1" />}
                  {theme}
                </Button>
              ))}
            </div>
          </div>

          {/* Reset Button */}
          <div className="pt-4 border-t border-gray-700">
            <Button
              variant="outline"
              onClick={resetSettings}
              className="w-full"
            >
              Reset to Defaults
            </Button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}

// Accessibility quick actions toolbar
export function AccessibilityToolbar() {
  const { settings, updateSetting } = useVisualAccessibility();
  const [showPanel, setShowPanel] = useState(false);

  return (
    <>
      <div className="fixed bottom-4 right-4 z-40 flex flex-col gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => updateSetting('textScale', Math.min(settings.textScale + 10, 150))}
          className="bg-gray-900/90 backdrop-blur-sm border-gray-700 text-white hover:bg-gray-800"
          title="Increase text size"
        >
          <ZoomIn className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => updateSetting('textScale', Math.max(settings.textScale - 10, 75))}
          className="bg-gray-900/90 backdrop-blur-sm border-gray-700 text-white hover:bg-gray-800"
          title="Decrease text size"
        >
          <ZoomOut className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => updateSetting('highContrast', !settings.highContrast)}
          className={`bg-gray-900/90 backdrop-blur-sm border-gray-700 text-white hover:bg-gray-800 ${
            settings.highContrast ? 'ring-2 ring-violet-500' : ''
          }`}
          title="Toggle high contrast"
        >
          <Contrast className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowPanel(true)}
          className="bg-gray-900/90 backdrop-blur-sm border-gray-700 text-white hover:bg-gray-800"
          title="Accessibility settings"
        >
          <Eye className="h-4 w-4" />
        </Button>
      </div>

      <AccessibilityPanel isOpen={showPanel} onClose={() => setShowPanel(false)} />
    </>
  );
}

// Color blind friendly color palette
export const colorBlindFriendlyColors = {
  primary: '#0066CC',    // Blue
  secondary: '#FF6600',  // Orange
  success: '#009900',    // Green
  warning: '#FFCC00',    // Yellow
  danger: '#CC0000',     // Red
  info: '#6600CC',       // Purple
};

// High contrast color palette
export const highContrastColors = {
  background: '#000000',
  surface: '#1a1a1a',
  text: '#ffffff',
  primary: '#ffff00',
  secondary: '#00ffff',
  border: '#ffffff',
};

// Utility function to get accessible color
export function getAccessibleColor(
  color: string,
  settings: AccessibilitySettings
): string {
  if (settings.highContrast) {
    // Map colors to high contrast equivalents
    const colorMap: Record<string, string> = {
      'text-violet-500': 'text-yellow-400',
      'text-blue-500': 'text-cyan-400',
      'text-green-500': 'text-green-400',
      'text-red-500': 'text-red-400',
      'text-orange-500': 'text-orange-400',
    };
    return colorMap[color] || color;
  }

  if (settings.colorBlindFriendly) {
    // Use color blind friendly alternatives
    const colorMap: Record<string, string> = {
      'text-red-500': 'text-orange-500',
      'text-green-500': 'text-blue-500',
    };
    return colorMap[color] || color;
  }

  return color;
}
