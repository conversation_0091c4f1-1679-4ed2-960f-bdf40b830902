import React from 'react';
import { motion } from 'framer-motion';
import { LoadingSkeleton } from '../ui/loading-skeleton';
import { EnhancedProgress } from '../ui/enhanced-progress';
import { cn } from '@/lib/utils';

interface AnalyticsLoadingScreenProps {
  className?: string;
  showProgress?: boolean;
}

export const AnalyticsLoadingScreen: React.FC<AnalyticsLoadingScreenProps> = ({
  className,
  showProgress = true,
}) => {
  const [progress, setProgress] = React.useState(0);

  React.useEffect(() => {
    const interval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 90) return prev;
        return prev + Math.random() * 15;
      });
    }, 200);

    return () => clearInterval(interval);
  }, []);

  return (
    <motion.div
      className={cn('space-y-8 p-6', className)}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      {/* Header */}
      <motion.div
        className="text-center space-y-4"
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.1 }}
      >
        <div className="flex items-center justify-center space-x-3">
          <motion.div
            className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center"
            animate={{ rotate: [0, 360] }}
            transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
          >
            <svg
              className="w-4 h-4 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
              />
            </svg>
          </motion.div>
          <h2 className="text-xl font-semibold text-foreground">
            Loading Analytics
          </h2>
        </div>
        
        {showProgress && (
          <div className="max-w-md mx-auto">
            <EnhancedProgress
              value={progress}
              variant="animated"
              label="Processing data"
              showPercentage
            />
          </div>
        )}
      </motion.div>

      {/* Stats Cards Skeleton */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-3 gap-6"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={i}
            className="bg-card border rounded-lg p-6 space-y-4"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.3 + i * 0.1 }}
          >
            <div className="flex items-center space-x-3">
              <motion.div
                className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center"
                animate={{
                  scale: [1, 1.1, 1],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: i * 0.5,
                }}
              >
                <div className="w-5 h-5 bg-primary/30 rounded" />
              </motion.div>
              <div className="space-y-2 flex-1">
                <div className="h-4 bg-muted rounded animate-pulse" />
                <div className="h-6 bg-muted rounded w-2/3 animate-pulse" />
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* Chart Skeleton */}
      <motion.div
        className="bg-card border rounded-lg p-6"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.4 }}
      >
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="h-6 bg-muted rounded w-48 animate-pulse" />
            <div className="h-8 bg-muted rounded w-24 animate-pulse" />
          </div>
          
          {/* Animated Chart Bars */}
          <div className="space-y-3">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <div className="h-4 bg-muted rounded w-16 animate-pulse" />
                <div className="flex-1 bg-secondary rounded-full h-3 overflow-hidden">
                  <motion.div
                    className="h-full bg-gradient-to-r from-primary to-primary/60 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${Math.random() * 80 + 20}%` }}
                    transition={{
                      duration: 1.5,
                      delay: 0.5 + i * 0.1,
                      ease: 'easeOut',
                    }}
                  />
                </div>
                <div className="h-4 bg-muted rounded w-12 animate-pulse" />
              </div>
            ))}
          </div>
        </div>
      </motion.div>

      {/* Additional Content Skeleton */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 gap-6"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.6 }}
      >
        <LoadingSkeleton variant="analytics" count={1} />
        <div className="space-y-4">
          <div className="h-6 bg-muted rounded w-32 animate-pulse" />
          {[...Array(4)].map((_, i) => (
            <motion.div
              key={i}
              className="flex items-center space-x-3 p-3 bg-card border rounded-lg"
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.7 + i * 0.1 }}
            >
              <div className="w-8 h-8 bg-primary/10 rounded-full animate-pulse" />
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-muted rounded w-3/4 animate-pulse" />
                <div className="h-3 bg-muted rounded w-1/2 animate-pulse" />
              </div>
              <div className="h-6 bg-muted rounded w-16 animate-pulse" />
            </motion.div>
          ))}
        </div>
      </motion.div>
    </motion.div>
  );
};