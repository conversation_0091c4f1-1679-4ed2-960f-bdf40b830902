import { useMemo } from "react";
import { MockTest, MockTestAnalytics } from "@/types/mockTest";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { BarChart3, TrendingUp, Activity } from "lucide-react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  RadialLinearScale,
  ArcElement,
  ScatterController,
} from 'chart.js';
import { Bar, Line, Radar } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  RadialLinearScale,
  ArcElement,
  ScatterController,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface MockTestChartsProps {
  analytics: MockTestAnalytics;
  mockTests: MockTest[];
}

export function MockTestCharts({ analytics, mockTests }: MockTestChartsProps) {
  // Create a map of subject colors from mock tests
  const subjectColors = useMemo(() => {
    const colors = new Map<string, string>();
    mockTests.forEach(test => {
      test.subjectMarks.forEach(subjectMark => {
        if (subjectMark.subjectColor) {
          colors.set(subjectMark.subject, subjectMark.subjectColor);
        }
      });
    });
    return colors;
  }, [mockTests]);

  // Prepare data for subject performance chart
  const subjectPerformanceData = useMemo(() => {
    return Object.entries(analytics.subjectPerformance).map(([subject, data]) => ({
      subject,
      averagePercentage: Number(data.averagePercentage.toFixed(2)),
      tests: data.totalTests,
      color: subjectColors.get(subject) || 'rgb(99, 102, 241)', // Default to indigo if no color found
    }));
  }, [analytics.subjectPerformance, subjectColors]);

  // Prepare data for performance trend chart (last 10 tests in chronological order)
  const performanceTrendData = useMemo(() => {
    return [...mockTests]
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .slice(-10)
      .map((test) => ({
        name: test.name,
        date: test.date,
        percentage: Number(((test.totalMarksObtained / test.totalMarks) * 100).toFixed(2)),
        subject: test.subjectMarks[0]?.subject || 'Unknown',
      }));
  }, [mockTests]);

  // Prepare data for subject radar chart
  const subjectRadarData = useMemo(() => {
    const subjects = Object.keys(analytics.subjectPerformance);
    const percentages = subjects.map(subject =>
      analytics.subjectPerformance[subject].averagePercentage
    );
    const colors = subjects.map(subject =>
      subjectColors.get(subject) || 'rgb(99, 102, 241)'
    );

    return {
      subjects,
      percentages,
      colors
    };
  }, [analytics.subjectPerformance, subjectColors]);



  if (mockTests.length === 0) {
    return (
      <div className="grid gap-4 md:grid-cols-2 mb-4">
        <Card>
          <CardHeader>
            <CardTitle>No Data Available</CardTitle>
          </CardHeader>
          <CardContent className="h-[300px] flex items-center justify-center">
            <p className="text-muted-foreground">
              Add mock tests to see your performance analytics
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Subject Performance Chart Configuration
  const subjectPerformanceConfig = {
    data: {
      labels: subjectPerformanceData.map(item => item.subject),
      datasets: [
        {
          label: 'Average Score (%)',
          data: subjectPerformanceData.map(item => item.averagePercentage),
          backgroundColor: subjectPerformanceData.map(item => `${item.color}80`), // 50% opacity
          borderColor: subjectPerformanceData.map(item => item.color),
          borderWidth: 2,
          borderRadius: 5,
          barThickness: 40,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top' as const,
        },
        title: {
          display: false,
        },
      },
      scales: {
        y: {
          beginAtZero: true,
          max: 100,
          title: {
            display: true,
            text: 'Percentage (%)',
          },
        },
      },
    },
  };

  // Performance Trend Chart Configuration
  const performanceTrendConfig = {
    data: {
      labels: performanceTrendData.map(item => item.name),
      datasets: [
        {
          label: 'Score (%)',
          data: performanceTrendData.map(item => item.percentage),
          borderColor: 'rgb(99, 102, 241)',
          backgroundColor: 'rgba(99, 102, 241, 0.1)',
          fill: true,
          tension: 0.4,
          pointRadius: 4,
          pointHoverRadius: 6,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top' as const,
        },
        title: {
          display: false,
        },
      },
      scales: {
        y: {
          beginAtZero: true,
          max: 100,
          title: {
            display: true,
            text: 'Percentage (%)',
          },
        },
      },
    },
  };

  // Subject Radar Chart Configuration
  const subjectRadarConfig = {
    data: {
      labels: subjectRadarData.subjects,
      datasets: [
        {
          label: 'Subject Performance',
          data: subjectRadarData.percentages,
          backgroundColor: 'rgba(99, 102, 241, 0.2)',
          borderColor: 'rgb(99, 102, 241)',
          borderWidth: 2,
          pointBackgroundColor: subjectRadarData.colors,
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: subjectRadarData.colors,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        r: {
          angleLines: {
            display: true,
            color: 'rgba(150, 150, 150, 0.2)',
          },
          suggestedMin: 0,
          suggestedMax: 100,
        },
      },
      plugins: {
        legend: {
          position: 'top' as const,
        },
        title: {
          display: false,
        },
      },
    },
  };



  return (
    <div className="space-y-8">
      <div className="grid gap-8 md:grid-cols-2">
        {/* Subject Performance Chart */}
        <Card className="overflow-hidden border border-border shadow-xl bg-card hover:shadow-2xl hover:-translate-y-1 transition-all duration-500 group relative">
          <div className="absolute inset-0 bg-muted/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
          <CardHeader className="bg-muted/30 border-b border-border relative z-10">
            <CardTitle className="flex items-center gap-3 text-xl font-bold">
              <div className="p-2 rounded-lg bg-gray-700 shadow-lg">
                <BarChart3 className="h-6 w-6 text-white" />
              </div>
              <span className="text-foreground">
                Subject Performance
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent className="h-[380px] p-6 relative z-10">
            <Bar {...subjectPerformanceConfig} />
          </CardContent>
        </Card>

        {/* Performance Trend Chart */}
        <Card className="overflow-hidden border border-border shadow-xl bg-card hover:shadow-2xl hover:-translate-y-1 transition-all duration-500 group relative">
          <div className="absolute inset-0 bg-muted/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
          <CardHeader className="bg-muted/30 border-b border-border relative z-10">
            <CardTitle className="flex items-center gap-3 text-xl font-bold">
              <div className="p-2 rounded-lg bg-emerald-600 shadow-lg">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <span className="text-foreground">
                Performance Trend
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent className="h-[380px] p-6 relative z-10">
            <Line {...performanceTrendConfig} />
          </CardContent>
        </Card>
      </div>

      {/* Subject Radar Chart */}
      <div className="grid gap-8">
        <Card className="overflow-hidden border border-border shadow-xl bg-card hover:shadow-2xl hover:-translate-y-1 transition-all duration-500 group relative">
          <div className="absolute inset-0 bg-muted/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
          <CardHeader className="bg-muted/30 border-b border-border relative z-10">
            <CardTitle className="flex items-center gap-3 text-xl font-bold">
              <div className="p-2 rounded-lg bg-violet-600 shadow-lg">
                <Activity className="h-6 w-6 text-white" />
              </div>
              <span className="text-foreground">
                Subject Strengths
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent className="h-[380px] p-6 relative z-10">
            <Radar {...subjectRadarConfig} />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}