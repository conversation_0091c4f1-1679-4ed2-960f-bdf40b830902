import React, { useEffect, useRef, createContext, useContext, useState } from 'react';
import { EnhancedTodoItem } from '@/types/todo';

// Screen reader announcement types
type AnnouncementPriority = 'polite' | 'assertive' | 'off';

interface Announcement {
  id: string;
  message: string;
  priority: AnnouncementPriority;
  timestamp: number;
}

interface ScreenReaderContextType {
  announce: (message: string, priority?: AnnouncementPriority) => void;
  announceTaskAction: (action: string, task: EnhancedTodoItem) => void;
  announceNavigation: (location: string, context?: string) => void;
  announceProgress: (current: number, total: number, context?: string) => void;
}

const ScreenReaderContext = createContext<ScreenReaderContextType | null>(null);

export function useScreenReader() {
  const context = useContext(ScreenReaderContext);
  if (!context) {
    throw new Error('useScreenReader must be used within ScreenReaderProvider');
  }
  return context;
}

interface ScreenReaderProviderProps {
  children: React.ReactNode;
}

export function ScreenReaderProvider({ children }: ScreenReaderProviderProps) {
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const politeRef = useRef<HTMLDivElement>(null);
  const assertiveRef = useRef<HTMLDivElement>(null);

  const announce = (message: string, priority: AnnouncementPriority = 'polite') => {
    const announcement: Announcement = {
      id: `announcement-${Date.now()}-${Math.random()}`,
      message,
      priority,
      timestamp: Date.now(),
    };

    setAnnouncements(prev => [...prev, announcement]);

    // Update the appropriate live region
    const targetRef = priority === 'assertive' ? assertiveRef : politeRef;
    if (targetRef.current) {
      targetRef.current.textContent = message;
      
      // Clear after a delay to allow for new announcements
      setTimeout(() => {
        if (targetRef.current) {
          targetRef.current.textContent = '';
        }
      }, 1000);
    }

    // Clean up old announcements
    setTimeout(() => {
      setAnnouncements(prev => prev.filter(a => a.id !== announcement.id));
    }, 5000);
  };

  const announceTaskAction = (action: string, task: EnhancedTodoItem) => {
    const taskDescription = `${task.title}${task.subjectId ? ` in ${task.subjectId}` : ''}`;
    const priority = task.priority ? `, ${task.priority} priority` : '';
    const dueDate = task.dueDate ? `, due ${new Date(task.dueDate).toLocaleDateString()}` : '';
    
    let message = '';
    
    switch (action) {
      case 'created':
        message = `Task created: ${taskDescription}${priority}${dueDate}`;
        break;
      case 'completed':
        message = `Task completed: ${taskDescription}`;
        break;
      case 'updated':
        message = `Task updated: ${taskDescription}`;
        break;
      case 'deleted':
        message = `Task deleted: ${taskDescription}`;
        break;
      case 'moved':
        message = `Task moved: ${taskDescription}`;
        break;
      case 'selected':
        message = `Task selected: ${taskDescription}${priority}${dueDate}`;
        break;
      default:
        message = `${action}: ${taskDescription}`;
    }
    
    announce(message, 'polite');
  };

  const announceNavigation = (location: string, context?: string) => {
    const message = context 
      ? `Navigated to ${location}, ${context}`
      : `Navigated to ${location}`;
    announce(message, 'polite');
  };

  const announceProgress = (current: number, total: number, context?: string) => {
    const percentage = Math.round((current / total) * 100);
    const message = context
      ? `${context}: ${current} of ${total} items, ${percentage}% complete`
      : `${current} of ${total} items, ${percentage}% complete`;
    announce(message, 'polite');
  };

  return (
    <ScreenReaderContext.Provider
      value={{
        announce,
        announceTaskAction,
        announceNavigation,
        announceProgress,
      }}
    >
      {children}
      
      {/* Live regions for screen reader announcements */}
      <div
        ref={politeRef}
        aria-live="polite"
        aria-atomic="true"
        className="sr-only"
      />
      <div
        ref={assertiveRef}
        aria-live="assertive"
        aria-atomic="true"
        className="sr-only"
      />
    </ScreenReaderContext.Provider>
  );
}

// ARIA helper components and hooks

interface AriaLabelledSectionProps {
  label: string;
  children: React.ReactNode;
  level?: 1 | 2 | 3 | 4 | 5 | 6;
  className?: string;
}

export function AriaLabelledSection({ 
  label, 
  children, 
  level = 2, 
  className = '' 
}: AriaLabelledSectionProps) {
  const headingId = `heading-${label.toLowerCase().replace(/\s+/g, '-')}`;
  const HeadingTag = `h${level}` as keyof JSX.IntrinsicElements;

  return (
    <section aria-labelledby={headingId} className={className}>
      <HeadingTag id={headingId} className="sr-only">
        {label}
      </HeadingTag>
      {children}
    </section>
  );
}

interface TaskCardAriaProps {
  task: EnhancedTodoItem;
  isSelected?: boolean;
  position?: { current: number; total: number };
  children: React.ReactNode;
  className?: string;
}

export function TaskCardAria({ 
  task, 
  isSelected = false, 
  position, 
  children, 
  className = '' 
}: TaskCardAriaProps) {
  const { announceTaskAction } = useScreenReader();

  const getAriaLabel = () => {
    const parts = [
      `Task: ${task.title}`,
      task.description ? `Description: ${task.description}` : '',
      task.priority ? `Priority: ${task.priority}` : '',
      task.dueDate ? `Due: ${new Date(task.dueDate).toLocaleDateString()}` : '',
      `Completion: ${task.completionPercentage}%`,
      position ? `${position.current} of ${position.total}` : '',
    ].filter(Boolean);

    return parts.join(', ');
  };

  const getAriaDescription = () => {
    const parts = [
      'Use arrow keys to navigate',
      'Press Space to toggle completion',
      'Press E to edit',
      'Press Delete to remove',
    ];
    return parts.join(', ');
  };

  const handleFocus = () => {
    announceTaskAction('selected', task);
  };

  return (
    <div
      role="listitem"
      tabIndex={0}
      aria-label={getAriaLabel()}
      aria-description={getAriaDescription()}
      aria-selected={isSelected}
      className={className}
      onFocus={handleFocus}
    >
      {children}
    </div>
  );
}

interface ProgressAriaProps {
  value: number;
  max?: number;
  label: string;
  description?: string;
  className?: string;
}

export function ProgressAria({ 
  value, 
  max = 100, 
  label, 
  description, 
  className = '' 
}: ProgressAriaProps) {
  const percentage = Math.round((value / max) * 100);
  
  return (
    <div
      role="progressbar"
      aria-valuenow={value}
      aria-valuemin={0}
      aria-valuemax={max}
      aria-label={`${label}: ${percentage}%`}
      aria-description={description}
      className={className}
    >
      <span className="sr-only">
        {label}: {value} of {max} ({percentage}%)
        {description && `. ${description}`}
      </span>
    </div>
  );
}

interface ButtonAriaProps {
  action: string;
  context?: string;
  isPressed?: boolean;
  isExpanded?: boolean;
  controls?: string;
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
}

export function ButtonAria({
  action,
  context,
  isPressed,
  isExpanded,
  controls,
  children,
  onClick,
  className = '',
}: ButtonAriaProps) {
  const getAriaLabel = () => {
    return context ? `${action} ${context}` : action;
  };

  return (
    <button
      type="button"
      aria-label={getAriaLabel()}
      aria-pressed={isPressed}
      aria-expanded={isExpanded}
      aria-controls={controls}
      onClick={onClick}
      className={className}
    >
      {children}
      <span className="sr-only">{getAriaLabel()}</span>
    </button>
  );
}

// Hook for managing focus and announcements
export function useTaskListAria(tasks: EnhancedTodoItem[]) {
  const { announceProgress, announceNavigation } = useScreenReader();
  const [currentIndex, setCurrentIndex] = useState(0);

  const announceListStatus = () => {
    const completed = tasks.filter(task => task.completionPercentage === 100).length;
    announceProgress(completed, tasks.length, 'Task completion');
  };

  const announceCurrentTask = () => {
    if (tasks[currentIndex]) {
      announceNavigation(
        `Task ${currentIndex + 1} of ${tasks.length}`,
        tasks[currentIndex].title
      );
    }
  };

  const moveToNext = () => {
    if (currentIndex < tasks.length - 1) {
      setCurrentIndex(prev => prev + 1);
      announceCurrentTask();
    }
  };

  const moveToPrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(prev => prev - 1);
      announceCurrentTask();
    }
  };

  const moveToFirst = () => {
    setCurrentIndex(0);
    announceCurrentTask();
  };

  const moveToLast = () => {
    setCurrentIndex(tasks.length - 1);
    announceCurrentTask();
  };

  return {
    currentIndex,
    currentTask: tasks[currentIndex],
    announceListStatus,
    announceCurrentTask,
    moveToNext,
    moveToPrevious,
    moveToFirst,
    moveToLast,
  };
}

// Screen reader only text component
interface ScreenReaderOnlyProps {
  children: React.ReactNode;
}

export function ScreenReaderOnly({ children }: ScreenReaderOnlyProps) {
  return <span className="sr-only">{children}</span>;
}

// Skip link component for keyboard navigation
interface SkipLinkProps {
  href: string;
  children: React.ReactNode;
}

export function SkipLink({ href, children }: SkipLinkProps) {
  return (
    <a
      href={href}
      className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-violet-600 focus:text-white focus:rounded-md focus:shadow-lg"
    >
      {children}
    </a>
  );
}
