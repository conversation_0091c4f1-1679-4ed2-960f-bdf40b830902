import { useEffect } from 'react';
import { Navigate } from 'react-router-dom';

// This file exists to handle legacy imports and redirect to the new StudentDashboard component
const Dashboard = () => {
  useEffect(() => {
    console.log('Dashboard component loaded - redirecting to StudentDashboard');
  }, []);

  // Redirect to the StudentDashboard component
  return <Navigate to="/dashboard" replace />;
};

export default Dashboard;