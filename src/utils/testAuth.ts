import { supabase } from '../integrations/supabase/client';
import { ensureAuthenticated } from './supabase';

export const testAuthentication = async () => {
  try {
    console.log('Testing authentication...');
    
    // Check current session
    const { data: { session }, error } = await supabase.auth.getSession();
    console.log('Current session:', session?.user?.id, 'Error:', error);
    
    if (session) {
      console.log('User is authenticated:', session.user.email);
      
      // Test database access
      const { data: todos, error: todosError } = await supabase
        .from('todos')
        .select('id, title, createdBy')
        .limit(5);
        
      console.log('Todos query result:', todos?.length || 0, 'items, Error:', todosError);
      
      // Test auth.uid() in database
      const { data: authTest, error: authError } = await supabase
        .rpc('auth_uid_test');
        
      console.log('Auth UID test:', authTest, 'Error:', authError);
    } else {
      console.log('User is not authenticated');
    }
    
    return { session, error };
  } catch (error) {
    console.error('Error testing authentication:', error);
    return { session: null, error };
  }
};

// Test function to be called from browser console
(window as any).testAuth = testAuthentication;
