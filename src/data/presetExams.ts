import { PresetExam, PresetExamType } from '@/types/todo';

export const PRESET_EXAMS: Record<PresetExamType, PresetExam> = {
  JEE_MAIN: {
    id: 'JEE_MAIN',
    name: 'JEE Main',
    fullName: 'Joint Entrance Examination - Main',
    description: 'National level engineering entrance exam for admission to NITs, IIITs, and other centrally funded technical institutions',
    subjects: ['Physics', 'Chemistry', 'Mathematics'],
    duration: 180, // 3 hours
    totalMarks: 300,
    category: 'Engineering',
    difficulty: 'hard',
  },
  JEE_ADVANCED: {
    id: 'JEE_ADVANCED',
    name: 'JEE Advanced',
    fullName: 'Joint Entrance Examination - Advanced',
    description: 'Advanced level engineering entrance exam for admission to IITs',
    subjects: ['Physics', 'Chemistry', 'Mathematics'],
    duration: 360, // 6 hours (2 papers)
    totalMarks: 372,
    category: 'Engineering',
    difficulty: 'hard',
  },
  NEET: {
    id: 'NEET',
    name: 'NEET',
    fullName: 'National Eligibility cum Entrance Test',
    description: 'National level medical entrance exam for admission to MBBS, BDS, and other medical courses',
    subjects: ['Physics', 'Chemistry', 'Biology'],
    duration: 180, // 3 hours
    totalMarks: 720,
    category: 'Medical',
    difficulty: 'hard',
  },
  BITSAT: {
    id: 'BITSAT',
    name: 'BITSAT',
    fullName: 'Birla Institute of Technology and Science Admission Test',
    description: 'Computer-based entrance exam for admission to BITS campuses',
    subjects: ['Physics', 'Chemistry', 'Mathematics', 'English', 'Logical Reasoning'],
    duration: 180, // 3 hours
    totalMarks: 450,
    category: 'Engineering',
    difficulty: 'hard',
  },
  WBJEE: {
    id: 'WBJEE',
    name: 'WBJEE',
    fullName: 'West Bengal Joint Entrance Examination',
    description: 'State level engineering entrance exam for West Bengal',
    subjects: ['Physics', 'Chemistry', 'Mathematics'],
    duration: 120, // 2 hours
    totalMarks: 200,
    category: 'Engineering',
    difficulty: 'medium',
  },
  MHT_CET: {
    id: 'MHT_CET',
    name: 'MHT CET',
    fullName: 'Maharashtra Common Entrance Test',
    description: 'State level engineering entrance exam for Maharashtra',
    subjects: ['Physics', 'Chemistry', 'Mathematics'],
    duration: 150, // 2.5 hours
    totalMarks: 200,
    category: 'Engineering',
    difficulty: 'medium',
  },
  KCET: {
    id: 'KCET',
    name: 'KCET',
    fullName: 'Karnataka Common Entrance Test',
    description: 'State level engineering entrance exam for Karnataka',
    subjects: ['Physics', 'Chemistry', 'Mathematics'],
    duration: 160, // 2 hours 40 minutes
    totalMarks: 180,
    category: 'Engineering',
    difficulty: 'medium',
  },
  COMEDK: {
    id: 'COMEDK',
    name: 'COMEDK',
    fullName: 'Consortium of Medical, Engineering and Dental Colleges of Karnataka',
    description: 'Entrance exam for private engineering colleges in Karnataka',
    subjects: ['Physics', 'Chemistry', 'Mathematics'],
    duration: 180, // 3 hours
    totalMarks: 180,
    category: 'Engineering',
    difficulty: 'medium',
  },
  VITEEE: {
    id: 'VITEEE',
    name: 'VITEEE',
    fullName: 'VIT Engineering Entrance Examination',
    description: 'Entrance exam for admission to VIT campuses',
    subjects: ['Physics', 'Chemistry', 'Mathematics', 'English'],
    duration: 150, // 2.5 hours
    totalMarks: 125,
    category: 'Engineering',
    difficulty: 'medium',
  },
  SRMJEEE: {
    id: 'SRMJEEE',
    name: 'SRMJEEE',
    fullName: 'SRM Joint Engineering Entrance Examination',
    description: 'Entrance exam for admission to SRM campuses',
    subjects: ['Physics', 'Chemistry', 'Mathematics'],
    duration: 150, // 2.5 hours
    totalMarks: 105,
    category: 'Engineering',
    difficulty: 'medium',
  },
  CBSE_12: {
    id: 'CBSE_12',
    name: 'CBSE 12th',
    fullName: 'Central Board of Secondary Education - Class 12',
    description: 'National board examination for Class 12 students',
    subjects: ['Physics', 'Chemistry', 'Mathematics', 'English', 'Computer Science'],
    duration: 180, // 3 hours per subject
    totalMarks: 500,
    category: 'Board',
    difficulty: 'medium',
  },
  ICSE_12: {
    id: 'ICSE_12',
    name: 'ICSE 12th',
    fullName: 'Indian Certificate of Secondary Education - Class 12',
    description: 'National board examination for Class 12 students',
    subjects: ['Physics', 'Chemistry', 'Mathematics', 'English', 'Computer Science'],
    duration: 180, // 3 hours per subject
    totalMarks: 500,
    category: 'Board',
    difficulty: 'medium',
  },
  STATE_BOARD: {
    id: 'STATE_BOARD',
    name: 'State Board',
    fullName: 'State Board Examination',
    description: 'State level board examination for Class 12 students',
    subjects: ['Physics', 'Chemistry', 'Mathematics', 'English'],
    duration: 180, // 3 hours per subject
    totalMarks: 500,
    category: 'Board',
    difficulty: 'easy',
  },
  SAT: {
    id: 'SAT',
    name: 'SAT',
    fullName: 'Scholastic Assessment Test',
    description: 'Standardized test for college admissions in the United States',
    subjects: ['Mathematics', 'Evidence-Based Reading and Writing'],
    duration: 180, // 3 hours
    totalMarks: 1600,
    category: 'International',
    difficulty: 'medium',
  },
  OTHER: {
    id: 'OTHER',
    name: 'Other',
    fullName: 'Other Examination',
    description: 'Custom or other examination not listed above',
    subjects: [],
    duration: 180,
    totalMarks: 100,
    category: 'Other',
    difficulty: 'medium',
  },
};

// Helper functions
export const getPresetExam = (examType: PresetExamType): PresetExam => {
  return PRESET_EXAMS[examType];
};

export const getExamsByCategory = (category: PresetExam['category']): PresetExam[] => {
  return Object.values(PRESET_EXAMS).filter(exam => exam.category === category);
};

export const getEngineeringExams = (): PresetExam[] => {
  return getExamsByCategory('Engineering');
};

export const getMedicalExams = (): PresetExam[] => {
  return getExamsByCategory('Medical');
};

export const getBoardExams = (): PresetExam[] => {
  return getExamsByCategory('Board');
};

export const getAllExamTypes = (): PresetExamType[] => {
  return Object.keys(PRESET_EXAMS) as PresetExamType[];
};

export const getExamOptions = () => {
  return Object.values(PRESET_EXAMS).map(exam => ({
    value: exam.id,
    label: exam.name,
    fullName: exam.fullName,
    category: exam.category,
    difficulty: exam.difficulty,
  }));
};

// Exam colors for UI
export const EXAM_CATEGORY_COLORS = {
  Engineering: '#3b82f6', // blue
  Medical: '#10b981', // emerald
  Board: '#8b5cf6', // violet
  International: '#f59e0b', // amber
  Other: '#6b7280', // gray
} as const;

export const getExamCategoryColor = (category: PresetExam['category']): string => {
  return EXAM_CATEGORY_COLORS[category];
};

export const getExamColor = (examType: PresetExamType): string => {
  const exam = getPresetExam(examType);
  return getExamCategoryColor(exam.category);
};
