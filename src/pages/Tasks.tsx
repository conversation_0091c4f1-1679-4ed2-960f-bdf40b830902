import { useEffect } from "react"
import { useNavigate } from "react-router-dom"
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext"
import { EnhancedTaskManagement } from "@/components/enhanced-tasks/EnhancedTaskManagement"
import { useBackgroundTheme } from "@/contexts/BackgroundThemeContext"
import { SmallDeviceWarning } from '@/components/ui/SmallDeviceWarning'
import { useDocumentTitle } from "@/hooks/useDocumentTitle"
import Header from "@/components/shared/Header"
import { motion } from "framer-motion"
import { CheckSquare, ListTodo } from "lucide-react"

export default function Tasks() {
  useDocumentTitle("Tasks - IsotopeAI");
  const { user, loading } = useSupabaseAuth()
  const navigate = useNavigate()
  const { getBackgroundStyle } = useBackgroundTheme()

  useEffect(() => {
    if (!loading && !user) {
      navigate('/login')
    }
  }, [user, loading, navigate])

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-[#0a0f2c] dark:to-[#1a1f3c]">
        <div className="relative w-16 h-16">
          <div className="absolute top-0 left-0 w-full h-full rounded-full border-4 border-primary/30 animate-pulse"></div>
          <div className="absolute top-0 left-0 w-full h-full rounded-full border-t-4 border-primary animate-spin"></div>
        </div>
        <p className="mt-4 text-muted-foreground animate-pulse">Loading your workspace...</p>
      </div>
    )
  }

  return (
    <div className="relative min-h-screen w-full bg-gradient-to-br from-slate-50 via-white to-slate-100 text-gray-900 dark:bg-gradient-to-br dark:from-slate-950 dark:via-slate-900 dark:to-slate-800 dark:text-white">
      {/* Fixed background that covers full page height */}
      <div className="fixed inset-0 w-full h-full bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:bg-gradient-to-br dark:from-slate-950 dark:via-slate-900 dark:to-slate-800 -z-10" />
      <SmallDeviceWarning />

      {/* Modern sophisticated background */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Base gradient overlay */}
        <div className={`absolute inset-0 ${getBackgroundStyle()} opacity-10 dark:opacity-20`} />

        {/* Animated mesh gradient */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 dark:from-blue-950/20 dark:via-purple-950/15 dark:to-pink-950/20"></div>
          <motion.div
            className="absolute inset-0 bg-gradient-to-tr from-emerald-50/20 via-transparent to-cyan-50/20 dark:from-emerald-950/15 dark:via-transparent dark:to-cyan-950/15"
            animate={{
              background: [
                "linear-gradient(45deg, rgba(16, 185, 129, 0.05) 0%, transparent 50%, rgba(6, 182, 212, 0.05) 100%)",
                "linear-gradient(135deg, rgba(6, 182, 212, 0.05) 0%, transparent 50%, rgba(139, 92, 246, 0.05) 100%)",
                "linear-gradient(225deg, rgba(139, 92, 246, 0.05) 0%, transparent 50%, rgba(16, 185, 129, 0.05) 100%)",
                "linear-gradient(315deg, rgba(16, 185, 129, 0.05) 0%, transparent 50%, rgba(6, 182, 212, 0.05) 100%)"
              ]
            }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: "linear"
            }}
          />
        </div>

        {/* Geometric grid pattern */}
        <div className="absolute inset-0 opacity-[0.02] dark:opacity-[0.05]">
          <div className="absolute inset-0" style={{
            backgroundImage: `
              linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px)
            `,
            backgroundSize: '60px 60px'
          }} />
        </div>

        {/* Modern floating geometric shapes */}
        <div className="hidden lg:block">
          {/* Large floating orbs with glass morphism */}
          <motion.div
            className="absolute top-20 right-20 w-96 h-96 rounded-full bg-gradient-to-br from-blue-400/10 to-purple-600/10 dark:from-blue-400/5 dark:to-purple-600/5 backdrop-blur-3xl"
            animate={{
              y: [0, -30, 0],
              x: [0, 15, 0],
              scale: [1, 1.05, 1],
            }}
            transition={{
              duration: 25,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />

          <motion.div
            className="absolute bottom-32 left-20 w-80 h-80 rounded-full bg-gradient-to-tr from-emerald-400/8 to-cyan-500/8 dark:from-emerald-400/4 dark:to-cyan-500/4 backdrop-blur-3xl"
            animate={{
              y: [0, 25, 0],
              x: [0, -20, 0],
              scale: [1, 0.95, 1],
            }}
            transition={{
              duration: 30,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 5
            }}
          />

          {/* Floating geometric shapes */}
          <motion.div
            className="absolute top-1/3 right-1/3 w-32 h-32 bg-gradient-to-br from-violet-500/10 to-pink-500/10 dark:from-violet-500/5 dark:to-pink-500/5 backdrop-blur-sm rounded-2xl rotate-12"
            animate={{
              y: [0, -20, 0],
              rotate: [12, 25, 12],
            }}
            transition={{
              duration: 15,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
          />

          <motion.div
            className="absolute bottom-1/3 right-1/4 w-24 h-24 bg-gradient-to-tl from-orange-400/10 to-red-500/10 dark:from-orange-400/5 dark:to-red-500/5 backdrop-blur-sm rounded-xl -rotate-12"
            animate={{
              y: [0, 15, 0],
              rotate: [-12, -25, -12],
            }}
            transition={{
              duration: 18,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 7
            }}
          />

          <motion.div
            className="absolute top-1/4 left-1/3 w-20 h-20 bg-gradient-to-br from-teal-400/10 to-blue-500/10 dark:from-teal-400/5 dark:to-blue-500/5 backdrop-blur-sm rounded-full"
            animate={{
              y: [0, -25, 0],
              x: [0, 10, 0],
              scale: [1, 1.1, 1],
            }}
            transition={{
              duration: 22,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 10
            }}
          />

          {/* Subtle task-related icons with modern styling */}
          <motion.div
            className="absolute top-1/2 right-20 text-slate-400/20 dark:text-slate-500/20"
            animate={{
              y: [0, -10, 0],
              rotate: [0, 3, 0],
              scale: [1, 1.05, 1],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1
            }}
          >
            <CheckSquare size={32} strokeWidth={1.5} />
          </motion.div>

          <motion.div
            className="absolute bottom-1/2 left-24 text-slate-400/15 dark:text-slate-500/15"
            animate={{
              y: [0, 12, 0],
              rotate: [0, -2, 0],
            }}
            transition={{
              duration: 16,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4
            }}
          >
            <ListTodo size={28} strokeWidth={1.5} />
          </motion.div>
        </div>

        {/* Subtle radial gradients for depth */}
        <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[800px] h-[400px] bg-gradient-radial from-blue-500/5 via-transparent to-transparent dark:from-blue-400/3 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-1/3 w-[600px] h-[300px] bg-gradient-radial from-purple-500/5 via-transparent to-transparent dark:from-purple-400/3 rounded-full blur-3xl"></div>
      </div>

      {/* Header */}
      <Header />

      {/* Main content with enhanced task management */}
      <motion.div
        className="relative z-10 flex flex-col min-h-screen pt-20 px-4 pb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
      >
        <div className="flex-1 w-full max-w-7xl mx-auto">
          {/* Enhanced Task Management Component */}
          <motion.div
            className="h-[calc(100vh-140px)]"
            initial={{ opacity: 0, scale: 0.98 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            <EnhancedTaskManagement />
          </motion.div>
        </div>
      </motion.div>
    </div>
  )
}
