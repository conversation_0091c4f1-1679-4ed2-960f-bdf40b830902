import {
  MockTest,
  UpcomingTest,
  StandardTestFormData
} from '@/types/mockTest';
import { testDataSyncService } from './testDataSyncService';

// Updated storage keys for unified data model
const UNIFIED_STORAGE_KEYS = {
  MOCK_TESTS: 'unified_mock_tests',
  UPCOMING_TESTS: 'unified_upcoming_tests',
  CATEGORIES: 'mocktest-categories', // Keep existing
  SYNC_STATUS: 'test_sync_status',
} as const;

// Helper functions for local storage
const getFromStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const item = localStorage.getItem(key);
    return item ? JSON.parse(item) : defaultValue;
  } catch (error) {
    console.error(`Error reading from localStorage key ${key}:`, error);
    return defaultValue;
  }
};

// saveToStorage function removed - using direct localStorage.setItem calls

// ============================================================================
// UNIFIED MOCK TEST STORAGE
// ============================================================================

export const unifiedMockTestStorage = {
  // Get storage key for user's mock tests
  getStorageKey: (userId: string) => `${UNIFIED_STORAGE_KEYS.MOCK_TESTS}_${userId}`,

  // Get all mock tests for a user
  getAll: (userId: string): MockTest[] => {
    try {
      const data = localStorage.getItem(unifiedMockTestStorage.getStorageKey(userId));
      const tests: MockTest[] = data ? JSON.parse(data) : [];
      
      // Ensure all tests have unified structure
      return tests.map(test => unifiedMockTestStorage.ensureUnifiedStructure(test));
    } catch (error) {
      console.error('Error loading mock tests:', error);
      return [];
    }
  },

  // Get a specific mock test by ID
  getById: (userId: string, testId: string): MockTest | null => {
    const tests = unifiedMockTestStorage.getAll(userId);
    return tests.find(test => test.id === testId) || null;
  },

  // Save a mock test
  save: (userId: string, mockTest: MockTest): MockTest => {
    const tests = unifiedMockTestStorage.getAll(userId);
    const existingIndex = tests.findIndex(t => t.id === mockTest.id);

    // Ensure unified structure
    const testToSave = unifiedMockTestStorage.ensureUnifiedStructure({
      ...mockTest,
      createdAt: mockTest.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });

    // Validate before saving
    const validation = testDataSyncService.validateTestData(testToSave);
    if (!validation.isValid) {
      throw new Error(`Invalid test data: ${validation.errors.join(', ')}`);
    }

    let updatedTests: MockTest[];
    if (existingIndex >= 0) {
      updatedTests = [...tests];
      updatedTests[existingIndex] = testToSave;
    } else {
      updatedTests = [...tests, testToSave];
    }

    // Save to local storage
    localStorage.setItem(unifiedMockTestStorage.getStorageKey(userId), JSON.stringify(updatedTests));

    // Trigger local sync (async, non-blocking)
    testDataSyncService.syncTestToLocalStorage(testToSave).catch(error => {
      console.warn('Failed to sync test locally:', error);
    });

    return testToSave;
  },

  // Delete a mock test
  delete: (userId: string, testId: string): boolean => {
    const tests = unifiedMockTestStorage.getAll(userId);
    const filteredTests = tests.filter(t => t.id !== testId);

    if (filteredTests.length === tests.length) return false;

    localStorage.setItem(unifiedMockTestStorage.getStorageKey(userId), JSON.stringify(filteredTests));
    return true;
  },

  // Ensure test has unified structure
  ensureUnifiedStructure: (test: Partial<MockTest>): MockTest => {
    // Convert legacy data to unified structure if needed
    const unifiedTest = testDataSyncService.migrateToUnifiedStructure(test);
    
    return {
      ...unifiedTest,
      // Ensure required MockTest fields
      testType: test.testType || 'mock',
      targetScore: test.targetScore || 0,
      subjectMarks: test.subjectMarks || [],
      totalMarksObtained: test.totalMarksObtained || 0,
      totalMarks: test.totalMarks || 0,
      difficulty: test.difficulty || 'medium',
      isReviewed: test.isReviewed || false,
      analysisCompleted: test.analysisCompleted || false,
      mistakesAnalyzed: test.mistakesAnalyzed || false,
      takeawaysRecorded: test.takeawaysRecorded || false,
      status: test.status || 'completed',
    } as MockTest;
  },

  // Migrate from legacy storage
  migrateFromLegacy: async (userId: string): Promise<void> => {
    try {
      // Import from old enhancedMockTestUtils if exists
      const legacyKey = `mock_tests_${userId}`;
      const legacyData = localStorage.getItem(legacyKey);
      
      if (legacyData) {
        const legacyTests = JSON.parse(legacyData);
        const migratedTests = legacyTests.map((test: any) => 
          unifiedMockTestStorage.ensureUnifiedStructure(test)
        );
        
        // Save migrated tests
        localStorage.setItem(unifiedMockTestStorage.getStorageKey(userId), JSON.stringify(migratedTests));
        
        // Remove legacy data
        localStorage.removeItem(legacyKey);
        
        console.log(`Migrated ${migratedTests.length} mock tests to unified structure`);
      }
    } catch (error) {
      console.error('Error migrating legacy mock tests:', error);
    }
  }
};

// ============================================================================
// UNIFIED UPCOMING TEST STORAGE
// ============================================================================

export const unifiedUpcomingTestStorage = {
  // Get storage key for user's upcoming tests
  getStorageKey: (userId: string) => `${UNIFIED_STORAGE_KEYS.UPCOMING_TESTS}_${userId}`,

  // Get all upcoming tests for a user
  getAll: (userId: string): UpcomingTest[] => {
    try {
      const data = localStorage.getItem(unifiedUpcomingTestStorage.getStorageKey(userId));
      const tests: UpcomingTest[] = data ? JSON.parse(data) : [];
      
      // Ensure all tests have unified structure
      return tests.map(test => unifiedUpcomingTestStorage.ensureUnifiedStructure(test));
    } catch (error) {
      console.error('Error loading upcoming tests:', error);
      return [];
    }
  },

  // Get a specific upcoming test by ID
  getById: (userId: string, testId: string): UpcomingTest | null => {
    const tests = unifiedUpcomingTestStorage.getAll(userId);
    return tests.find(test => test.id === testId) || null;
  },

  // Create a new upcoming test
  create: (userId: string, testData: Omit<UpcomingTest, 'id' | 'createdAt' | 'daysLeft'>): UpcomingTest => {
    const newTest: UpcomingTest = {
      ...testData,
      id: `upcoming-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      createdAt: new Date().toISOString(),
      daysLeft: unifiedUpcomingTestStorage.calculateDaysLeft(testData.date),
      userId,
    };

    return unifiedUpcomingTestStorage.save(userId, newTest);
  },

  // Save an upcoming test
  save: (userId: string, upcomingTest: UpcomingTest): UpcomingTest => {
    const tests = unifiedUpcomingTestStorage.getAll(userId);
    const existingIndex = tests.findIndex(t => t.id === upcomingTest.id);

    // Ensure unified structure
    const testToSave = unifiedUpcomingTestStorage.ensureUnifiedStructure({
      ...upcomingTest,
      createdAt: upcomingTest.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      daysLeft: unifiedUpcomingTestStorage.calculateDaysLeft(upcomingTest.date),
    });

    // Validate before saving
    const validation = testDataSyncService.validateTestData(testToSave);
    if (!validation.isValid) {
      throw new Error(`Invalid upcoming test data: ${validation.errors.join(', ')}`);
    }

    let updatedTests: UpcomingTest[];
    if (existingIndex >= 0) {
      updatedTests = [...tests];
      updatedTests[existingIndex] = testToSave;
    } else {
      updatedTests = [...tests, testToSave];
    }

    // Save to local storage
    localStorage.setItem(unifiedUpcomingTestStorage.getStorageKey(userId), JSON.stringify(updatedTests));

    return testToSave;
  },

  // Delete an upcoming test
  delete: (userId: string, testId: string): boolean => {
    const tests = unifiedUpcomingTestStorage.getAll(userId);
    const filteredTests = tests.filter(t => t.id !== testId);

    if (filteredTests.length === tests.length) return false;

    localStorage.setItem(unifiedUpcomingTestStorage.getStorageKey(userId), JSON.stringify(filteredTests));
    return true;
  },

  // Get upcoming tests within specified days
  getUpcoming: (userId: string, daysAhead: number = 30): UpcomingTest[] => {
    const tests = unifiedUpcomingTestStorage.getAll(userId);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to start of day to allow same-day tests
    const futureDate = new Date(today.getTime() + (daysAhead * 24 * 60 * 60 * 1000));

    return tests
      .filter(test => {
        const testDate = new Date(test.date);
        testDate.setHours(0, 0, 0, 0); // Set to start of day for comparison
        return testDate >= today && testDate <= futureDate;
      })
      .map(test => ({
        ...test,
        daysLeft: unifiedUpcomingTestStorage.calculateDaysLeft(test.date)
      }))
      .sort((a, b) => (a.daysLeft || 0) - (b.daysLeft || 0));
  },

  // Calculate days left until test
  calculateDaysLeft: (testDate: string): number => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to start of day
    const test = new Date(testDate);
    test.setHours(0, 0, 0, 0); // Set to start of day
    const diffTime = test.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  },

  // Ensure test has unified structure
  ensureUnifiedStructure: (test: Partial<UpcomingTest>): UpcomingTest => {
    // Convert legacy data to unified structure if needed
    const unifiedTest = testDataSyncService.migrateToUnifiedStructure(test);
    
    return {
      ...unifiedTest,
      // Ensure required UpcomingTest fields
      isNotificationEnabled: test.isNotificationEnabled || false,
      daysLeft: test.daysLeft || unifiedUpcomingTestStorage.calculateDaysLeft(test.date || ''),
      status: test.status || 'scheduled',
    } as UpcomingTest;
  },

  // Migrate from legacy storage
  migrateFromLegacy: async (userId: string): Promise<void> => {
    try {
      // Import from old upcomingTestStorage if exists
      const legacyData = getFromStorage<UpcomingTest[]>('mocktest-upcoming', []);
      const userLegacyTests = legacyData.filter(test => test.userId === userId);

      if (userLegacyTests.length > 0) {
        const migratedTests = userLegacyTests.map(test =>
          unifiedUpcomingTestStorage.ensureUnifiedStructure(test)
        );

        // Save migrated tests
        localStorage.setItem(unifiedUpcomingTestStorage.getStorageKey(userId), JSON.stringify(migratedTests));

        console.log(`Migrated ${migratedTests.length} upcoming tests to unified structure`);
      }
    } catch (error) {
      console.error('Error migrating legacy upcoming tests:', error);
    }
  }
};

// ============================================================================
// UNIFIED TEST TRANSITION SERVICE
// ============================================================================

export const unifiedTestTransitionService = {
  // Transition an upcoming test to completed status
  transitionToCompleted: async (
    userId: string,
    upcomingTestId: string,
    completionData: {
      subjectMarks: any[];
      totalMarksObtained: number;
      totalMarks: number;
      timeSpent?: number;
      actualDifficulty?: 'easier' | 'as_expected' | 'harder';
      timeManagement?: 'excellent' | 'good' | 'average' | 'poor';
      stressLevel?: 1 | 2 | 3 | 4 | 5;
      notes?: string;
    }
  ): Promise<MockTest> => {
    // Get the upcoming test
    const upcomingTest = unifiedUpcomingTestStorage.getById(userId, upcomingTestId);
    if (!upcomingTest) {
      throw new Error('Upcoming test not found');
    }

    // Create completed test with preserved data
    const completedTest: MockTest = {
      // Basic fields from upcoming test
      id: upcomingTest.id,
      name: upcomingTest.name,
      date: upcomingTest.date,
      time: upcomingTest.time,
      userId: upcomingTest.userId,
      createdAt: upcomingTest.createdAt,

      // Add completion-specific data
      ...completionData,

      // Ensure required fields for completed tests
      testType: upcomingTest.testType || 'mock',
      categoryId: upcomingTest.categoryId, // Preserve category
      description: upcomingTest.description, // Preserve description
      targetScore: upcomingTest.targetScore,
      syllabus: upcomingTest.syllabus,
      mistakes: upcomingTest.mistakes,
      takeaways: upcomingTest.takeaways,

      // Set completion status
      status: 'completed',
      completedAt: new Date().toISOString(),
      resultEnteredAt: new Date().toISOString(),
      isFromUpcoming: true,
      upcomingTestId: upcomingTest.id,

      // Analysis flags
      isReviewed: false,
      analysisCompleted: false,
      mistakesAnalyzed: false,
      takeawaysRecorded: false,

      // Update timestamps
      updatedAt: new Date().toISOString()
    };

    // Save completed test
    const savedTest = unifiedMockTestStorage.save(userId, completedTest);

    // Remove from upcoming tests
    unifiedUpcomingTestStorage.delete(userId, upcomingTestId);

    return savedTest;
  },

  // Mark an upcoming test as missed
  markAsMissed: async (userId: string, upcomingTestId: string): Promise<MockTest> => {
    const upcomingTest = unifiedUpcomingTestStorage.getById(userId, upcomingTestId);
    if (!upcomingTest) {
      throw new Error('Upcoming test not found');
    }

    // Create missed test record
    const missedTest: MockTest = {
      ...upcomingTest,

      // Set as missed with no performance data
      subjectMarks: [],
      totalMarksObtained: 0,
      totalMarks: 0,
      notes: (upcomingTest.notes || '') + '\n\nTest was missed.',

      // Required fields
      testType: upcomingTest.testType || 'mock',
      categoryId: upcomingTest.categoryId, // Preserve category
      description: upcomingTest.description, // Preserve description
      targetScore: upcomingTest.targetScore || 0,
      syllabus: upcomingTest.syllabus || { topics: [], chapters: [], overallProgress: 0 },
      mistakes: [],
      takeaways: [],
      difficulty: 'medium',

      // Set missed status
      status: 'missed',
      completedAt: new Date().toISOString(),
      isFromUpcoming: true,
      upcomingTestId: upcomingTest.id,

      // Analysis flags
      isReviewed: false,
      analysisCompleted: false,
      mistakesAnalyzed: false,
      takeawaysRecorded: false,

      // Update timestamps
      updatedAt: new Date().toISOString()
    };

    // Save missed test
    const savedTest = unifiedMockTestStorage.save(userId, missedTest);

    // Remove from upcoming tests
    unifiedUpcomingTestStorage.delete(userId, upcomingTestId);

    return savedTest;
  },

  // Get tests that should be automatically transitioned
  getTestsForAutoTransition: (userId: string): UpcomingTest[] => {
    const upcomingTests = unifiedUpcomingTestStorage.getAll(userId);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return upcomingTests.filter(test => {
      const testDate = new Date(test.date);
      testDate.setHours(0, 0, 0, 0);

      // Test date has passed and auto-transition is enabled
      return testDate < today && test.autoTransition;
    });
  }
};

// ============================================================================
// UNIFIED FORM DATA CONVERSION
// ============================================================================

export const unifiedFormDataConverter = {
  // Convert form data to upcoming test
  formDataToUpcomingTest: (formData: StandardTestFormData, userId: string): UpcomingTest => {
    const unifiedData = testDataSyncService.convertFormDataToUnified(formData, userId);

    return {
      ...unifiedData,
      // Upcoming test specific fields
      isNotificationEnabled: formData.isNotificationEnabled,
      daysLeft: unifiedUpcomingTestStorage.calculateDaysLeft(unifiedData.date),
      status: 'scheduled',
      enableMistakeTracking: formData.enableMistakeTracking,
      enableTakeawayCollection: formData.enableTakeawayCollection,
    } as UpcomingTest;
  },

  // Convert form data to mock test
  formDataToMockTest: (formData: StandardTestFormData, userId: string): MockTest => {
    const unifiedData = testDataSyncService.convertFormDataToUnified(formData, userId);

    // Calculate totals from subject inputs
    const totalMarksObtained = formData.subjectInputs.reduce((sum, input) =>
      sum + (parseFloat(input.marksObtained?.toString() || '0') || 0), 0);
    const totalMarks = formData.subjectInputs.reduce((sum, input) =>
      sum + (parseFloat(input.totalMarks?.toString() || '0') || 0), 0);

    return {
      ...unifiedData,
      // Mock test specific fields
      testType: formData.testType,
      targetScore: parseInt(formData.targetScore) || 0,
      subjectMarks: formData.subjectInputs.map(input => ({
        subject: input.subject || '',
        subjectColor: input.subjectColor || '#3b82f6',
        marksObtained: input.marksObtained || 0,
        totalMarks: input.totalMarks || 0
      })),
      totalMarksObtained,
      totalMarks,
      difficulty: formData.expectedDifficulty || 'medium',
      isReviewed: false,
      analysisCompleted: false,
      mistakesAnalyzed: false,
      takeawaysRecorded: false,
      status: 'completed',
      completedAt: new Date().toISOString(),
      resultEnteredAt: new Date().toISOString(),
    } as MockTest;
  },

  // Convert test to form data
  testToFormData: (test: MockTest | UpcomingTest): StandardTestFormData => {
    return testDataSyncService.convertUnifiedToFormData(test);
  }
};

// ============================================================================
// MIGRATION UTILITIES
// ============================================================================

export const unifiedMigrationUtils = {
  // Run complete migration from legacy storage
  migrateAllData: async (userId: string): Promise<void> => {
    console.log('Starting migration to unified data model...');

    try {
      await unifiedMockTestStorage.migrateFromLegacy(userId);
      await unifiedUpcomingTestStorage.migrateFromLegacy(userId);

      console.log('Migration to unified data model completed successfully');
    } catch (error) {
      console.error('Error during migration:', error);
      throw error;
    }
  },

  // Check if migration is needed
  isMigrationNeeded: (userId: string): boolean => {
    const hasLegacyMockTests = localStorage.getItem(`mock_tests_${userId}`) !== null;
    const hasLegacyUpcomingTests = getFromStorage<UpcomingTest[]>('mocktest-upcoming', [])
      .some(test => test.userId === userId);

    return hasLegacyMockTests || hasLegacyUpcomingTests;
  },

  // Get migration status
  getMigrationStatus: (userId: string): {
    needed: boolean;
    mockTestsCount: number;
    upcomingTestsCount: number;
  } => {
    const legacyMockTests = localStorage.getItem(`mock_tests_${userId}`);
    const legacyUpcomingTests = getFromStorage<UpcomingTest[]>('mocktest-upcoming', [])
      .filter(test => test.userId === userId);

    return {
      needed: unifiedMigrationUtils.isMigrationNeeded(userId),
      mockTestsCount: legacyMockTests ? JSON.parse(legacyMockTests).length : 0,
      upcomingTestsCount: legacyUpcomingTests.length
    };
  }
};
