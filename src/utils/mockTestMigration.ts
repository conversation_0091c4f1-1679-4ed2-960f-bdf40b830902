import { MockTest, SubjectMarks } from '@/types/mockTest';
import { getUserMockTestsFromSupabase } from './supabaseClient';
import { enhancedMockTestUtils } from './mockTestLocalStorage';

/**
 * Service for handling backward compatibility migration of mock tests from Supabase to local storage
 * This ensures users who had data before the local migration can access their historical data
 */
export class MockTestMigrationService {
  private static instance: MockTestMigrationService;
  private static readonly MIGRATION_FLAG_KEY = 'mocktest-migration-completed';
  private static readonly MIGRATION_VERSION = '1.0.0';
  private migrationInProgress = new Set<string>();

  static getInstance(): MockTestMigrationService {
    if (!MockTestMigrationService.instance) {
      MockTestMigrationService.instance = new MockTestMigrationService();
    }
    return MockTestMigrationService.instance;
  }

  /**
   * Check if migration has been completed for a user
   */
  private isMigrationCompleted(userId: string): boolean {
    try {
      const migrationData = localStorage.getItem(`${MockTestMigrationService.MIGRATION_FLAG_KEY}_${userId}`);
      if (!migrationData) return false;
      
      const parsed = JSON.parse(migrationData);
      return parsed.completed === true && parsed.version === MockTestMigrationService.MIGRATION_VERSION;
    } catch (error) {
      console.error('Error checking migration status:', error);
      return false;
    }
  }

  /**
   * Mark migration as completed for a user
   */
  private markMigrationComplete(userId: string, stats: { migrated: number; skipped: number; errors: number }): void {
    try {
      const migrationData = {
        completed: true,
        version: MockTestMigrationService.MIGRATION_VERSION,
        completedAt: new Date().toISOString(),
        stats
      };
      localStorage.setItem(`${MockTestMigrationService.MIGRATION_FLAG_KEY}_${userId}`, JSON.stringify(migrationData));
      console.log(`✅ Migration completed for user ${userId}:`, stats);
    } catch (error) {
      console.error('Error marking migration complete:', error);
    }
  }

  /**
   * Transform Supabase mock test data to unified local storage format
   */
  private transformSupabaseToLocal(supabaseTest: any): MockTest {
    try {
      // Parse subject marks if it's a string
      let subjectMarks: SubjectMarks[] = [];
      if (typeof supabaseTest.subject_marks === 'string') {
        subjectMarks = JSON.parse(supabaseTest.subject_marks);
      } else if (Array.isArray(supabaseTest.subject_marks)) {
        subjectMarks = supabaseTest.subject_marks;
      }

      // Ensure subject marks have the correct structure
      subjectMarks = subjectMarks.map(mark => ({
        subject: mark.subject || '',
        marksObtained: Number(mark.marksObtained) || 0,
        totalMarks: Number(mark.totalMarks) || 0,
        subjectColor: mark.subjectColor || '#6366f1'
      }));

      const mockTest: MockTest = {
        // Basic information from Supabase
        id: supabaseTest.id,
        name: supabaseTest.name,
        date: supabaseTest.test_date, // Convert date to string format
        userId: supabaseTest.user_id,
        createdAt: supabaseTest.created_at,
        updatedAt: supabaseTest.updated_at || supabaseTest.created_at,

        // Performance data
        subjectMarks,
        totalMarksObtained: Number(supabaseTest.total_marks_obtained) || 0,
        totalMarks: Number(supabaseTest.total_marks) || 0,
        notes: supabaseTest.notes || '',

        // Enhanced fields with defaults for unified structure
        testType: 'mock',
        status: 'completed',
        isReviewed: false,
        analysisCompleted: false,
        mistakesAnalyzed: false,
        takeawaysRecorded: false,
        mistakes: [],
        takeaways: [],
        difficulty: null,

        // Migration metadata
        isFromSupabase: true,
        migratedAt: new Date().toISOString()
      };

      return mockTest;
    } catch (error) {
      console.error('Error transforming Supabase test to local format:', error, supabaseTest);
      throw new Error(`Failed to transform test ${supabaseTest.id}: ${error.message}`);
    }
  }

  /**
   * Fetch mock tests from Supabase for a user
   */
  private async fetchSupabaseData(userId: string): Promise<any[]> {
    try {
      console.log(`📥 Fetching Supabase data for user: ${userId}`);
      const supabaseTests = await getUserMockTestsFromSupabase(userId);
      console.log(`📊 Found ${supabaseTests.length} tests in Supabase for user ${userId}`);
      return supabaseTests || [];
    } catch (error) {
      console.error('Error fetching Supabase data:', error);
      // Don't throw here - we want to continue with local data even if Supabase fails
      return [];
    }
  }

  /**
   * Merge Supabase data with existing local data, handling conflicts intelligently
   */
  private mergeWithLocalData(userId: string, supabaseTests: MockTest[]): { testsToSave: MockTest[]; conflicts: Array<{ id: string; reason: string }> } {
    try {
      // Get existing local tests
      const existingLocalTests = enhancedMockTestUtils.getAll(userId);
      console.log(`📊 Found ${existingLocalTests.length} existing local tests`);

      // Create a map of existing tests for quick lookup
      const existingTestsMap = new Map(existingLocalTests.map(test => [test.id, test]));

      const testsToSave: MockTest[] = [];
      const conflicts: Array<{ id: string; reason: string }> = [];

      // Process each Supabase test
      for (const supabaseTest of supabaseTests) {
        const existingLocal = existingTestsMap.get(supabaseTest.id);

        if (!existingLocal) {
          // No conflict - new test from Supabase
          testsToSave.push(supabaseTest);
          console.log(`✅ Adding new test from Supabase: ${supabaseTest.name}`);
        } else {
          // Conflict detected - same ID exists in both
          const conflict = this.resolveTestConflict(supabaseTest, existingLocal);
          testsToSave.push(conflict.resolvedTest);

          if (conflict.hadConflict) {
            conflicts.push({
              id: supabaseTest.id,
              reason: conflict.resolution
            });
            console.log(`⚠️ Resolved conflict for test ${supabaseTest.id}: ${conflict.resolution}`);
          }
        }
      }

      // Add any local tests that don't exist in Supabase
      for (const localTest of existingLocalTests) {
        if (!supabaseTests.find(st => st.id === localTest.id)) {
          testsToSave.push(localTest);
        }
      }

      console.log(`📊 Final merge result: ${testsToSave.length} tests total, ${conflicts.length} conflicts resolved`);

      return { testsToSave, conflicts };
    } catch (error) {
      console.error('Error merging data:', error);
      // Return existing local data if merge fails
      return {
        testsToSave: enhancedMockTestUtils.getAll(userId),
        conflicts: [{ id: 'merge-error', reason: 'Failed to merge data' }]
      };
    }
  }

  /**
   * Resolve conflicts between Supabase and local test data
   */
  private resolveTestConflict(supabaseTest: MockTest, localTest: MockTest): {
    resolvedTest: MockTest;
    hadConflict: boolean;
    resolution: string
  } {
    // Compare timestamps to determine which is newer
    const supabaseTime = new Date(supabaseTest.updatedAt || supabaseTest.createdAt).getTime();
    const localTime = new Date(localTest.updatedAt || localTest.createdAt).getTime();

    // Check if local test has enhanced data that Supabase doesn't have
    const localHasEnhancements =
      localTest.isReviewed ||
      localTest.analysisCompleted ||
      (localTest.mistakes && localTest.mistakes.length > 0) ||
      (localTest.takeaways && localTest.takeaways.length > 0) ||
      localTest.categoryId ||
      localTest.testPaperUrl ||
      localTest.difficulty;

    // Resolution strategy:
    // 1. If local has enhancements, prefer local data but update basic fields from Supabase if newer
    // 2. If no enhancements, prefer the newer version
    // 3. Always preserve enhanced fields from local

    if (localHasEnhancements) {
      // Merge: keep local enhancements but update basic data if Supabase is newer
      const resolvedTest: MockTest = {
        ...localTest, // Start with local (has enhancements)
        // Update basic fields from Supabase if it's newer
        ...(supabaseTime > localTime ? {
          name: supabaseTest.name,
          date: supabaseTest.date,
          subjectMarks: supabaseTest.subjectMarks,
          totalMarksObtained: supabaseTest.totalMarksObtained,
          totalMarks: supabaseTest.totalMarks,
          notes: supabaseTest.notes,
          updatedAt: supabaseTest.updatedAt
        } : {}),
        // Always preserve migration metadata
        isFromSupabase: true,
        migratedAt: new Date().toISOString()
      };

      return {
        resolvedTest,
        hadConflict: true,
        resolution: `Merged local enhancements with ${supabaseTime > localTime ? 'newer' : 'older'} Supabase data`
      };
    } else {
      // No local enhancements - use the newer version
      const useSupabase = supabaseTime >= localTime;
      const resolvedTest = useSupabase ? {
        ...supabaseTest,
        isFromSupabase: true,
        migratedAt: new Date().toISOString()
      } : localTest;

      return {
        resolvedTest,
        hadConflict: supabaseTime !== localTime,
        resolution: `Used ${useSupabase ? 'Supabase' : 'local'} version (${useSupabase ? 'newer' : 'same or newer'})`
      };
    }
  }

  /**
   * Main migration function - migrates user's mock tests from Supabase to local storage
   */
  async migrateUserData(userId: string): Promise<{ success: boolean; stats: { migrated: number; skipped: number; errors: number } }> {
    // Check if migration already completed
    if (this.isMigrationCompleted(userId)) {
      console.log(`✅ Migration already completed for user: ${userId}`);
      return { success: true, stats: { migrated: 0, skipped: 0, errors: 0 } };
    }

    // Check if migration is already in progress
    if (this.migrationInProgress.has(userId)) {
      console.log(`⏳ Migration already in progress for user: ${userId}`);
      return { success: false, stats: { migrated: 0, skipped: 0, errors: 1 } };
    }

    this.migrationInProgress.add(userId);
    console.log(`🚀 Starting migration for user: ${userId}`);

    const stats = { migrated: 0, skipped: 0, errors: 0 };

    try {
      // Fetch data from Supabase
      const supabaseData = await this.fetchSupabaseData(userId);
      
      if (supabaseData.length === 0) {
        console.log(`📭 No Supabase data found for user: ${userId}`);
        this.markMigrationComplete(userId, stats);
        return { success: true, stats };
      }

      // Transform Supabase data to local format
      const transformedTests: MockTest[] = [];
      for (const supabaseTest of supabaseData) {
        try {
          const localTest = this.transformSupabaseToLocal(supabaseTest);
          transformedTests.push(localTest);
          stats.migrated++;
        } catch (error) {
          console.error(`❌ Failed to transform test ${supabaseTest.id}:`, error);
          stats.errors++;
        }
      }

      // Merge with existing local data and resolve conflicts
      const mergeResult = this.mergeWithLocalData(userId, transformedTests);
      const { testsToSave, conflicts } = mergeResult;

      // Log conflict resolution results
      if (conflicts.length > 0) {
        console.log(`⚠️ Resolved ${conflicts.length} conflicts during migration:`, conflicts);
      }

      // Save only the new tests from Supabase (don't re-save existing local tests)
      for (const test of transformedTests) {
        try {
          enhancedMockTestUtils.save(userId, test);
        } catch (error) {
          console.error(`❌ Failed to save test ${test.id}:`, error);
          stats.errors++;
          stats.migrated--; // Decrement since save failed
        }
      }

      // Mark migration as completed
      this.markMigrationComplete(userId, stats);

      console.log(`✅ Migration completed successfully for user: ${userId}`, stats);
      return { success: true, stats };

    } catch (error) {
      console.error(`❌ Migration failed for user ${userId}:`, error);
      stats.errors++;
      return { success: false, stats };
    } finally {
      this.migrationInProgress.delete(userId);
    }
  }

  /**
   * Check if migration is needed and trigger it if necessary
   * This is the main entry point called by the enhanced mock test utils
   */
  async ensureMigrationCompleted(userId: string): Promise<boolean> {
    if (this.isMigrationCompleted(userId)) {
      return true;
    }

    console.log(`🔄 Migration needed for user: ${userId}`);
    const result = await this.migrateUserData(userId);
    return result.success;
  }

  /**
   * Get migration status for a user
   */
  getMigrationStatus(userId: string): { completed: boolean; stats?: any; completedAt?: string } {
    try {
      const migrationData = localStorage.getItem(`${MockTestMigrationService.MIGRATION_FLAG_KEY}_${userId}`);
      if (!migrationData) {
        return { completed: false };
      }
      
      const parsed = JSON.parse(migrationData);
      return {
        completed: parsed.completed === true,
        stats: parsed.stats,
        completedAt: parsed.completedAt
      };
    } catch (error) {
      console.error('Error getting migration status:', error);
      return { completed: false };
    }
  }

  /**
   * Reset migration status for a user (for testing purposes)
   */
  resetMigrationStatus(userId: string): void {
    try {
      localStorage.removeItem(`${MockTestMigrationService.MIGRATION_FLAG_KEY}_${userId}`);
      console.log(`🔄 Reset migration status for user: ${userId}`);
    } catch (error) {
      console.error('Error resetting migration status:', error);
    }
  }

  /**
   * Force re-migration for a user (for testing purposes)
   */
  async forceMigration(userId: string): Promise<{ success: boolean; stats: { migrated: number; skipped: number; errors: number } }> {
    console.log(`🔄 Forcing migration for user: ${userId}`);
    this.resetMigrationStatus(userId);
    return await this.migrateUserData(userId);
  }
}

// Export singleton instance
export const mockTestMigrationService = MockTestMigrationService.getInstance();
