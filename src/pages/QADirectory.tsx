import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
// TODO: Implement Supabase Q&A functions
import { PublicQnA } from '../types/qa';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '../components/ui/tabs';
import { Separator } from '../components/ui/separator';
import { useDocumentTitle } from '../hooks/useDocumentTitle';
import { LoadingSkeleton } from '@/components/loading';

const QADirectory: React.FC = () => {
  useDocumentTitle('Q&A Directory - IsotopeAI');
  const [recentQnAs, setRecentQnAs] = useState<PublicQnA[]>([]);
  const [popularQnAs, setPopularQnAs] = useState<PublicQnA[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadQnAs = async () => {
      setIsLoading(true);
      try {
        // TODO: Implement Supabase Q&A loading
        setRecentQnAs([]);
        setPopularQnAs([]);
      } catch (error) {
        console.error('Error loading Q&As:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadQnAs();
  }, []);

  // Filter Q&As based on search query
  const filteredRecent = searchQuery
    ? recentQnAs.filter(qna => 
        qna.questionText.toLowerCase().includes(searchQuery.toLowerCase()) ||
        qna.answerText.toLowerCase().includes(searchQuery.toLowerCase()))
    : recentQnAs;

  const filteredPopular = searchQuery
    ? popularQnAs.filter(qna => 
        qna.questionText.toLowerCase().includes(searchQuery.toLowerCase()) ||
        qna.answerText.toLowerCase().includes(searchQuery.toLowerCase()))
    : popularQnAs;

  // Format timestamp for display
  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Q&A Directory</h1>
      
      <div className="mb-8">
        <p className="text-lg mb-4">
          Browse public questions and answers from the IsotopeAI community. 
          Find solutions to common problems or discover new insights.
        </p>
        
        <div className="flex gap-4 mb-6">
          <Input
            type="text"
            placeholder="Search Q&As..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-md"
          />
          <Button variant="outline" onClick={() => setSearchQuery('')}>
            Clear
          </Button>
          <Link to="/ai">
            <Button>Ask a New Question</Button>
          </Link>
        </div>
      </div>
      
      <Tabs defaultValue="recent" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="recent">Recent Questions</TabsTrigger>
          <TabsTrigger value="popular">Popular Questions</TabsTrigger>
        </TabsList>
        
        <TabsContent value="recent">
          {isLoading ? (
            <div className="py-4">
              <LoadingSkeleton variant="list" count={5} />
            </div>
          ) : filteredRecent.length > 0 ? (
            <ul className="space-y-6">
              {filteredRecent.map(qna => (
                <li key={qna.id} className="border rounded-lg p-4 hover:bg-gray-50 transition">
                  <Link to={`/qa/${qna.slug}`} className="block">
                    <h2 className="text-xl font-semibold text-blue-600 hover:underline mb-2">
                      {qna.questionText}
                    </h2>
                    <p className="text-gray-600 mb-3 line-clamp-2">
                      {qna.answerText}
                    </p>
                    <div className="flex justify-between items-center text-sm text-gray-500">
                      <span>Asked on {formatDate(qna.createdAt)}</span>
                      <span>{qna.viewCount} views</span>
                    </div>
                  </Link>
                </li>
              ))}
            </ul>
          ) : (
            <div className="text-center py-8">
              {searchQuery ? 'No results found for your search.' : 'No recent questions available.'}
            </div>
          )}
        </TabsContent>
        
        <TabsContent value="popular">
          {isLoading ? (
            <div className="py-4">
              <LoadingSkeleton variant="list" count={5} />
            </div>
          ) : filteredPopular.length > 0 ? (
            <ul className="space-y-6">
              {filteredPopular.map(qna => (
                <li key={qna.id} className="border rounded-lg p-4 hover:bg-gray-50 transition">
                  <Link to={`/qa/${qna.slug}`} className="block">
                    <h2 className="text-xl font-semibold text-blue-600 hover:underline mb-2">
                      {qna.questionText}
                    </h2>
                    <p className="text-gray-600 mb-3 line-clamp-2">
                      {qna.answerText}
                    </p>
                    <div className="flex justify-between items-center text-sm text-gray-500">
                      <span>Asked on {formatDate(qna.createdAt)}</span>
                      <span>{qna.viewCount} views</span>
                    </div>
                  </Link>
                </li>
              ))}
            </ul>
          ) : (
            <div className="text-center py-8">
              {searchQuery ? 'No results found for your search.' : 'No popular questions available.'}
            </div>
          )}
        </TabsContent>
      </Tabs>
      
      <Separator className="my-10" />
      
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-4">Have a question?</h2>
        <p className="mb-6">Don't see what you're looking for? Ask our AI and get instant answers.</p>
        <Link to="/ai">
          <Button size="lg">Ask a New Question</Button>
        </Link>
      </div>
    </div>
  );
};

export default QADirectory; 