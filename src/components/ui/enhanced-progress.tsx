import React from 'react';
import { motion } from 'framer-motion';
import { Progress } from './progress';
import { cn } from '@/lib/utils';

interface EnhancedProgressProps {
  value?: number;
  variant?: 'default' | 'gradient' | 'animated' | 'stepped';
  size?: 'sm' | 'md' | 'lg';
  showPercentage?: boolean;
  label?: string;
  steps?: number;
  className?: string;
  color?: 'primary' | 'success' | 'warning' | 'error';
}

const sizeClasses = {
  sm: 'h-2',
  md: 'h-3',
  lg: 'h-4',
};

const colorClasses = {
  primary: 'bg-primary',
  success: 'bg-green-500',
  warning: 'bg-yellow-500',
  error: 'bg-red-500',
};

export const EnhancedProgress: React.FC<EnhancedProgressProps> = ({
  value = 0,
  variant = 'default',
  size = 'md',
  showPercentage = false,
  label,
  steps,
  className,
  color = 'primary',
}) => {
  const progressValue = Math.min(Math.max(value, 0), 100);

  if (variant === 'stepped' && steps) {
    const currentStep = Math.floor((progressValue / 100) * steps);
    
    return (
      <div className={cn('space-y-2', className)}>
        {label && (
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-foreground">{label}</span>
            <span className="text-sm text-muted-foreground">
              {currentStep} / {steps}
            </span>
          </div>
        )}
        <div className="flex space-x-1">
          {[...Array(steps)].map((_, index) => (
            <motion.div
              key={index}
              className={cn(
                'flex-1 rounded-full transition-colors duration-300',
                sizeClasses[size],
                index < currentStep
                  ? colorClasses[color]
                  : 'bg-secondary'
              )}
              initial={{ scale: 0.8, opacity: 0.5 }}
              animate={{
                scale: index < currentStep ? 1 : 0.8,
                opacity: index < currentStep ? 1 : 0.5,
              }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            />
          ))}
        </div>
      </div>
    );
  }

  if (variant === 'animated') {
    return (
      <div className={cn('space-y-2', className)}>
        {(label || showPercentage) && (
          <div className="flex justify-between items-center">
            {label && <span className="text-sm font-medium text-foreground">{label}</span>}
            {showPercentage && (
              <motion.span
                className="text-sm text-muted-foreground"
                key={progressValue}
                initial={{ scale: 1.2 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.2 }}
              >
                {Math.round(progressValue)}%
              </motion.span>
            )}
          </div>
        )}
        <div className={cn('relative overflow-hidden rounded-full bg-secondary', sizeClasses[size])}>
          <motion.div
            className={cn('h-full rounded-full', colorClasses[color])}
            initial={{ width: 0 }}
            animate={{ width: `${progressValue}%` }}
            transition={{ duration: 0.8, ease: 'easeOut' }}
          />
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
            animate={{ x: ['-100%', '100%'] }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          />
        </div>
      </div>
    );
  }

  if (variant === 'gradient') {
    return (
      <div className={cn('space-y-2', className)}>
        {(label || showPercentage) && (
          <div className="flex justify-between items-center">
            {label && <span className="text-sm font-medium text-foreground">{label}</span>}
            {showPercentage && (
              <span className="text-sm text-muted-foreground">
                {Math.round(progressValue)}%
              </span>
            )}
          </div>
        )}
        <div className={cn('relative overflow-hidden rounded-full bg-secondary', sizeClasses[size])}>
          <motion.div
            className="h-full rounded-full bg-gradient-to-r from-primary via-primary/80 to-primary"
            initial={{ width: 0 }}
            animate={{ width: `${progressValue}%` }}
            transition={{ duration: 0.6, ease: 'easeOut' }}
          />
        </div>
      </div>
    );
  }

  return (
    <div className={cn('space-y-2', className)}>
      {(label || showPercentage) && (
        <div className="flex justify-between items-center">
          {label && <span className="text-sm font-medium text-foreground">{label}</span>}
          {showPercentage && (
            <span className="text-sm text-muted-foreground">
              {Math.round(progressValue)}%
            </span>
          )}
        </div>
      )}
      <Progress value={progressValue} className={sizeClasses[size]} />
    </div>
  );
};