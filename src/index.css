@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Space+Grotesk:wght@300;400;500;600;700&display=swap');
@import './styles/loading-animations.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 142.1 76.2% 36.3%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 142.1 76.2% 36.3%;
    --radius: 0.75rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --primary-rgb: 59, 130, 246; /* Blue color in RGB format */
  }

  .dark {
    --background: 20 14.3% 4.1%;
    --foreground: 0 0% 95%;
    --card: 24 9.8% 10%;
    --card-foreground: 0 0% 95%;
    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 95%;
    --primary: 142.1 70.6% 45.3%;
    --primary-foreground: 144.9 80.4% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 15%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 12 6.5% 15.1%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 85.7% 97.3%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 142.4 71.8% 29.2%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
    box-sizing: border-box;
  }
  body {
    @apply bg-background text-foreground font-inter antialiased;
    overflow-x: hidden;
    max-width: 100vw;
  }
  #root {
    max-width: 100vw;
    overflow-x: hidden;
  }
}

.gradient-bg {
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(var(--secondary)) 100%);
}

.feature-card {
  @apply bg-card p-6 rounded-2xl shadow-lg transition-transform duration-300 hover:-translate-y-1;
}

.stat-card {
  @apply text-center p-4;
}

.search-container {
  @apply w-full max-w-2xl mx-auto mt-8 mb-12;
}

.recent-queries {
  @apply flex flex-wrap gap-2 justify-center mt-4;
}

/* Add table border styles */
.prose table {
  @apply border-collapse;
  width: 100%;
  display: block;
  overflow-x: auto;
  max-width: 100%;
}

.prose th,
.prose td {
  @apply border border-border p-1 sm:p-2;
  min-width: 80px;
}

.prose th {
  @apply font-bold;
}

/* Add text wrapping */
.prose {
    overflow-wrap: break-word;
}

/* Adjust layout for larger screens */
@media (min-width: 768px) {
    .search-container {
        @apply max-w-4xl;
    }
}

/* Adjust layout for smaller screens */
@media (max-width: 767px) {
    .search-container {
        @apply w-full px-4;
    }

    /* Improve mobile navigation links */
    .navigation-links {
        @apply flex-wrap justify-center;
    }

    /* Adjust greeting and quote text size */
    .greeting-text {
        @apply text-2xl;
    }

    .quote-text {
        @apply text-sm;
    }
}

/* Extra small screens */
@media (max-width: 480px) {
    .search-container {
        @apply px-2;
    }

    /* Further reduce text sizes */
    .greeting-text {
        @apply text-xl;
    }

    /* Adjust navigation buttons */
    .nav-button {
        @apply px-2 py-1 text-xs;
    }

    /* Adjust discussion section */
    .discussion-section {
        @apply px-2;
    }

    /* Leaderboard mobile optimizations */
    .xs\:hidden {
        display: none !important;
    }

    .xs\:inline {
        display: inline !important;
    }
}

.chat-interface {
    @apply w-full max-w-4xl mx-auto;
    perspective: 1000px;
}

.chat-interface > div {
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

@media (max-width: 767px) {
    .chat-interface {
        @apply w-full px-2;
    }

    /* Adjust chat interface height for mobile */
    .chat-interface-container {
        @apply h-[70vh] !important;
    }

    /* Improve message display on mobile */
    .message-bubble {
        @apply max-w-full overflow-x-auto;
    }
}

/* Drag and drop styles */
body.dragging-active {
  cursor: grabbing !important;
}

body.dragging-invalid {
  cursor: no-drop !important;
}

/* Add a subtle animation for drag operations */
@keyframes pulse-border {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0.4);
  }
  70% {
    box-shadow: 0 0 0 4px rgba(var(--primary-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0);
  }
}

.dragging-active .min-h-\[50px\] {
  transition: all 0.2s ease;
}

.dragging-active .min-h-\[50px\]:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
  border: 1px dashed rgba(var(--primary-rgb), 0.3);
}

/* Improve the appearance of the placeholder */
[data-rbd-placeholder-context-id] {
  opacity: 0.3 !important;
  background-color: rgba(var(--primary-rgb), 0.1) !important;
  border: 1px dashed rgba(var(--primary-rgb), 0.3) !important;
  border-radius: 0.375rem !important;
  margin-bottom: 0.5rem !important;
}

/* Add a subtle transition for task cards */
[data-rbd-draggable-id] {
  transition: transform 0.15s ease, box-shadow 0.15s ease !important;
}

/* Enhance the appearance of the task being dragged */
[data-rbd-draggable-id][data-rbd-dragging="true"] {
  transform: scale(1.02) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  z-index: 50 !important;
  cursor: grabbing !important;
}

/* Fix for cursor position during drag - ensure the card stays with the cursor */
[data-rbd-draggable-id][data-rbd-dragging="true"] > div {
  transform: none !important;
}

/* Improve drop area visualization */
[data-rbd-droppable-id] {
  min-height: 100px;
  transition: background-color 0.2s ease, border 0.2s ease !important;
}

/* Empty column drop area styling */
[data-rbd-droppable-id]:empty {
  background-color: rgba(var(--primary-rgb), 0.02);
  border: 1px dashed rgba(var(--primary-rgb), 0.1);
  border-radius: 0.375rem;
}

/* Highlight the entire column when dragging over */
[data-rbd-droppable-id][data-rbd-droppable-context-id].bg-primary\/10 {
  animation: pulse-border 1.5s infinite;
  border-radius: 0.375rem;
}

/* Make the placeholder more visible */
[data-rbd-placeholder-context-id] {
  opacity: 0.5 !important;
  background-color: rgba(var(--primary-rgb), 0.15) !important;
  border: 1px dashed rgba(var(--primary-rgb), 0.4) !important;
  border-radius: 0.375rem !important;
  margin-bottom: 0.5rem !important;
  min-height: 80px !important;
  transition: all 0.2s ease !important;
}

/* Style for the empty space at the bottom of columns */
.flex-grow.min-h-\[50px\] {
  transition: background-color 0.2s ease;
  border-radius: 0.375rem;
}

/* Highlight the empty space when dragging over the column */
[data-rbd-droppable-id][data-rbd-droppable-context-id].bg-primary\/10 .flex-grow.min-h-\[50px\] {
  background-color: rgba(var(--primary-rgb), 0.05);
  border: 1px dashed rgba(var(--primary-rgb), 0.2);
}

/* Improve the drag handle cursor */
[data-rbd-draggable-id] {
  cursor: grab !important;
}

[data-rbd-draggable-id]:active {
  cursor: grabbing !important;
}

/* Additional styles for the task being dragged */
.task-being-dragged {
  pointer-events: auto !important;
}

/* Fix for the react-beautiful-dnd positioning issue */
.react-beautiful-dnd-draggable {
  transform: translate(0, 0) !important;
}

/* Ensure the dragged item stays with the cursor */
[data-rbd-draggable-id][data-rbd-dragging="true"] {
  pointer-events: none;
  position: fixed !important;
  top: auto !important;
  left: auto !important;
  margin: 0 !important;
  transform: none !important;
  width: auto !important;
}

/* Ensure the card doesn't get distorted during drag */
[data-rbd-draggable-id][data-rbd-dragging="true"] > div {
  width: 100% !important;
  height: 100% !important;
  transform: none !important;
}

/* Improve the appearance of the card during drag */
[data-rbd-draggable-id][data-rbd-dragging="true"] .shadow-sm {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
  border: 2px solid rgba(var(--primary-rgb), 0.5) !important;
}

/* KaTeX styling has been moved to @layer components section */

/* Chat interface animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out forwards;
}

.animate-slide-in {
  animation: slide-in 0.3s ease-out forwards;
}

.animate-pulse-once {
  animation: pulse 0.5s ease-out;
}

/* Message bubble styles */
.message-bubble {
  @apply relative overflow-hidden;
}

.message-bubble::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-br from-transparent to-white/5 pointer-events-none opacity-0 transition-opacity duration-300;
}

.message-bubble:hover::before {
  @apply opacity-100;
}

/* Improved scrollbar styles */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  @apply w-2;
}

.scrollbar-thin::-webkit-scrollbar-track {
  @apply bg-transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  @apply bg-primary/10 rounded-full transition-colors duration-200;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  @apply bg-primary/20;
}

/* Loading indicator animation */
@keyframes thinking-dots {
  0%, 20% {
    content: '.';
  }
  40%, 60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

.thinking-dots::after {
  content: '';
  animation: thinking-dots 1.5s infinite;
}

@layer components {
  /* Heading styles for AI output */
  .prose h1 {
    @apply text-4xl font-bold mt-10 mb-6;
  }

  .prose h2 {
    @apply text-3xl font-bold mt-8 mb-4;
  }

  .prose h3 {
    @apply text-2xl font-semibold mt-6 mb-3;
  }

  /* Fix heading symbol display with proper font rendering */
  .prose h1, .prose h2, .prose h3 {
    @apply font-spaceGrotesk;
    color: hsl(var(--foreground));
  }

  /* List styles with proper bullet alignment */
  .prose ul {
    @apply list-disc list-outside pl-5 my-2 space-y-2;
  }

  .prose ol {
    @apply list-decimal list-outside pl-6 my-2 space-y-2;
    counter-reset: list-item;
  }

  .prose ul li {
    @apply my-1.5 leading-normal;
    display: list-item;
    position: relative;
  }

  .prose ol li {
    @apply my-1.5 leading-normal;
    position: relative;
    padding-left: 0.25rem;
    counter-increment: list-item;
  }

  .prose ol li::before {
    content: counter(list-item) ".";
    position: absolute;
    left: -1.5rem;
    color: var(--tw-prose-counters);
    font-weight: 500;
  }

  /* Fix list item content alignment - ensure text is next to marker, not below */
  .prose li > * {
    display: inline;
    vertical-align: top;
  }

  /* Fix bullet vertical alignment */
  .prose ul li::marker {
    @apply text-primary/70;
    vertical-align: middle;
  }

  /* Fix for nested list alignment */
  .prose li > ul,
  .prose li > ol {
    @apply block my-2 ml-4;
  }

  /* Ensure consistent line height across list items */
  .prose li p {
    @apply my-1 leading-normal;
    display: inline;
  }

  /* KaTeX Math Styling */
  .katex {
    font-size: 1.15em !important;
  }

  .katex-display {
    overflow-x: auto;
    overflow-y: hidden;
    padding: 0.5rem 0;
    margin: 1.5rem auto !important;
    text-align: center;
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .katex-display > .katex {
    display: flex !important;
    justify-content: center;
    font-size: 1.3em !important;
    margin: 0 auto;
    text-align: center;
  }

  .math-display .katex-display {
    background-color: rgba(var(--secondary), 0.5);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin: 2rem auto !important;
    max-width: 100%;
    display: flex;
    justify-content: center;
    width: 100%;
  }

  /* Style for inline math */
  .math-inline {
    padding: 0 0.15rem;
  }

  /* Handle overflow for long equations */
  .math-display .katex-html {
    max-width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    padding-bottom: 0.5rem;
    display: flex;
    justify-content: center;
    width: 100%;
  }

  /* Ensure display mode equations are centered */
  .katex-display .katex-html .base {
    margin: 0 auto;
  }

  /* Enhanced styling for display mode equations */
  .katex-display {
    position: relative;
    border-radius: 0.5rem;
  }

  /* Add subtle highlight effect on hover */
  .katex-display:hover {
    background-color: rgba(var(--secondary), 0.2);
    transition: background-color 0.3s ease;
  }

  /* Custom container for display math */
  .math-display-container {
    margin: 1.5rem 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .math-display-container > div {
    background-color: rgba(var(--secondary), 0.1);
    border-radius: 0.5rem;
    padding: 1rem;
    transition: background-color 0.3s ease;
    max-width: 100%;
    overflow-x: auto;
  }

  .math-display-container > div:hover {
    background-color: rgba(var(--secondary), 0.2);
  }

  /* Wrapper for KaTeX display mode equations */
  .katex-display-wrapper {
    font-size: 1.2em;
    text-align: center;
  }

  /* Ensure equations don't overflow on mobile */
  @media (max-width: 640px) {
    .math-display-container {
      margin: 1rem 0;
    }

    .math-display-container > div {
      padding: 0.75rem;
      width: 100%;
    }

    .katex-display-wrapper {
      font-size: 1.1em;
    }
  }

  /* Make sure MathML is accessible */
  .katex-mathml {
    position: absolute;
    clip: rect(1px, 1px, 1px, 1px);
    padding: 0;
    border: 0;
    height: 1px;
    width: 1px;
    overflow: hidden;
  }

  /* Improve semantic elements */
  .mrow {
    display: inline-block;
  }

  /* Add spacing for semantics groups */
  semantics {
    margin: 0.25rem 0;
  }

  /* Fix for numbered points with text on next line */
  .prose ol li {
    display: block;
    margin-bottom: 0.75rem;
  }

  .prose ol li p,
  .prose ol li div {
    display: inline;
    margin: 0;
  }
}

.shared-view-gradient {
  background:
    radial-gradient(circle at top right,
      rgba(var(--primary-rgb), 0.18),
      rgba(var(--primary-rgb), 0.08) 25%,
      rgba(var(--primary-rgb), 0.03) 50%,
      transparent 80%),
    radial-gradient(circle at 20% 80%,
      rgba(var(--primary-rgb), 0.12),
      rgba(var(--primary-rgb), 0.04) 30%,
      rgba(var(--primary-rgb), 0.01) 70%,
      transparent 90%),
    radial-gradient(circle at 80% 50%,
      rgba(255, 89, 100, 0.1),
      rgba(255, 89, 100, 0.05) 30%,
      rgba(255, 89, 100, 0.02) 60%,
      transparent 80%);
  background-attachment: fixed;
}

/* Add new styles for the shared view */
.blur-card {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.shared-message-appear {
  animation: message-appear 0.5s ease forwards;
}

@keyframes message-appear {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Glass morphism effects */
.glass-panel {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
}

.glass-panel:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

/* Light beam effect */
.light-beam {
  position: absolute;
  width: 150%;
  height: 300px;
  background: linear-gradient(90deg,
    transparent,
    rgba(var(--primary-rgb), 0.05),
    rgba(var(--primary-rgb), 0.1),
    rgba(var(--primary-rgb), 0.05),
    transparent);
  transform: rotate(-45deg) translateX(-50%);
  animation: beam-move 8s infinite linear;
  pointer-events: none;
  z-index: -1;
}

@keyframes beam-move {
  0% {
    transform: rotate(-45deg) translateX(-100%);
  }
  100% {
    transform: rotate(-45deg) translateX(100%);
  }
}

/* Enhanced shared view animations */
@keyframes floating {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}

.float-animation {
  animation: floating 6s ease infinite;
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(var(--primary-rgb), 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(var(--primary-rgb), 0.6);
  }
}

.glow-animation {
  animation: glow 4s ease-in-out infinite;
}

/* Shared message hover effect */
.shared-message:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Typing indicator animation */
@keyframes typing {
  0% {
    width: 0;
  }
  50% {
    width: 100%;
  }
  100% {
    width: 0;
  }
}

.typing-animation::after {
  content: "";
  display: block;
  width: 0;
  height: 2px;
  background: rgba(var(--primary-rgb), 0.6);
  animation: typing 2.5s ease-in-out infinite;
  border-radius: 2px;
}

/* Enhanced light beam effects */
.light-beam-enhanced {
  position: absolute;
  width: 250%;
  height: 500px;
  background: linear-gradient(90deg,
    transparent,
    rgba(var(--primary-rgb), 0.01),
    rgba(var(--primary-rgb), 0.03),
    rgba(var(--primary-rgb), 0.01),
    transparent);
  transform: rotate(-35deg) translateX(-50%);
  animation: beam-move 12s infinite linear;
  pointer-events: none;
  z-index: -1;
  filter: blur(10px);
}

/* Particle effect for shared view */
.particle {
  position: absolute;
  width: 6px;
  height: 6px;
  background-color: rgba(var(--primary-rgb), 0.5);
  border-radius: 50%;
  pointer-events: none;
  z-index: -1;
  opacity: 0.4;
  filter: blur(1px);
  animation: particle-float 15s linear infinite;
}

@keyframes particle-float {
  0% {
    transform: translateY(0) translateX(0);
    opacity: 0;
  }
  20% {
    opacity: 0.4;
  }
  80% {
    opacity: 0.4;
  }
  100% {
    transform: translateY(-100vh) translateX(20px);
    opacity: 0;
  }
}

/* Apply frosted glass effect to shared chat buttons */
.glass-button {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  transition: all 0.2s ease;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
}

/* Fullscreen chat header responsive styles */
@media (max-width: 480px) {
  .fullscreen-chat-header {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .fullscreen-chat-header h1 {
    font-size: 1.25rem;
  }

  .fullscreen-chat-header img {
    width: 1.5rem;
    height: 1.5rem;
  }

  .fullscreen-chat-header .button-container {
    gap: 0.25rem;
  }

  /* Improve mobile chat experience */
  .chat-message {
    @apply px-2 py-3;
  }

  .chat-message-content {
    @apply text-sm;
  }

  /* Adjust search bar on mobile */
  .search-input {
    @apply py-4 text-sm;
  }

  /* Fix code blocks on mobile */
  pre {
    @apply max-w-full overflow-x-auto text-xs;
    max-width: calc(100vw - 3rem) !important;
    white-space: pre-wrap !important;
  }

  /* Ensure code doesn't overflow */
  code {
    white-space: pre-wrap !important;
    word-break: break-word !important;
  }

  /* Adjust tools dropdown on mobile */
  .tools-dropdown {
    @apply right-0 left-auto;
  }
}

/* Improve header transitions */
.fullscreen-chat-header {
  transition: all 0.3s ease-in-out;
}

.fullscreen-chat-header img,
.fullscreen-chat-header h1 {
  transition: all 0.3s ease-in-out;
}

/* Gradient text animation for shared page titles */
.animated-gradient-text {
  background: linear-gradient(to right, hsl(var(--primary)), hsl(var(--primary)/0.8), hsl(var(--primary)));
  background-size: 200% auto;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  animation: gradient-shift 5s linear infinite;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Transition effects */
.shared-transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* New content-focused styles to replace glassmorphism */
.content-panel {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  border-radius: var(--radius);
}

.content-panel:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 2px 4px rgba(0, 0, 0, 0.05);
}

.content-button {
  background: hsl(var(--secondary));
  border: 1px solid hsl(var(--border));
  transition: all 0.2s ease;
}

.content-button:hover {
  background: hsl(var(--secondary)/0.7);
  transform: translateY(-1px);
}

.subtle-gradient {
  background: linear-gradient(to bottom, hsl(var(--background)), hsl(var(--background)/0.97));
  background-attachment: fixed;
}

/* Modified text styles */
.clean-gradient-text {
  color: hsl(var(--primary));
  font-weight: 700;
}

/* Replace floating animations with subtle transitions */
.subtle-transition {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.subtle-transition:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

/* Refined message styles */
.clean-message {
  border-radius: var(--radius);
  border: 1px solid hsl(var(--border));
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.clean-message:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

/* Shared pages common styles */
.shared-page-container {
  position: relative;
  overflow: hidden;
}

/* Animated background for shared pages */
.shared-bg-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
  opacity: 0.5;
}

/* Enhanced chat message styling */
.chat-message {
  position: relative;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  isolation: isolate;
}

.chat-message::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(to right,
    hsl(var(--primary)/0.1),
    hsl(var(--primary)/0.05) 30%,
    hsl(var(--secondary)/0.05) 70%,
    hsl(var(--secondary)/0.1)
  );
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chat-message:hover::before {
  opacity: 1;
}

/* Shimmer effect animations */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-shimmer {
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 3s infinite linear;
}

/* First message in shared chat highlight */
.first-message {
  position: relative;
}

.first-message::after {
  content: '';
  position: absolute;
  top: -8px;
  left: 16px;
  right: 16px;
  height: 1px;
  background: linear-gradient(to right,
    transparent,
    hsl(var(--primary)/0.3),
    transparent
  );
}

/* Enhance cards in shared directory */
@keyframes card-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0);
  }
  70% {
    box-shadow: 0 0 0 4px rgba(var(--primary-rgb), 0.1);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0);
  }
}

.card-hover-effect {
  transition: all 0.3s ease;
}

.card-hover-effect:hover {
  transform: translateY(-5px);
  animation: card-pulse 1.5s infinite;
}

/* Enhancing text aesthetics */
.gradient-heading {
  background-image: linear-gradient(to right, hsl(var(--foreground)), hsl(var(--foreground)/0.7));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  font-family: 'Space Grotesk', sans-serif;
}

/* Button animations */
.button-glow {
  position: relative;
  overflow: hidden;
}

.button-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: all 0.6s;
}

.button-glow:hover::before {
  left: 100%;
}

/* Enhancing scrollbar for shared pages */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--primary)/0.3) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: hsl(var(--primary)/0.3);
  border-radius: 20px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--primary)/0.5);
}

/* Blurred background elements */
.blur-circle {
  position: absolute;
  border-radius: 100%;
  filter: blur(45px);
  opacity: 0.4;
  pointer-events: none;
  z-index: -1;
  background-image: radial-gradient(
    circle at center,
    hsla(var(--primary), 0.2) 0%,
    hsla(var(--primary), 0.1) 50%,
    transparent 80%
  );
}

.blur-circle-1 {
  top: 10%;
  right: 10%;
  width: 40vw;
  height: 40vw;
  max-width: 400px;
  max-height: 400px;
}

.blur-circle-2 {
  bottom: 5%;
  left: 5%;
  width: 50vw;
  height: 50vw;
  max-width: 500px;
  max-height: 500px;
  background-image: radial-gradient(
    circle at center,
    hsla(var(--secondary), 0.15) 0%,
    hsla(var(--secondary), 0.05) 50%,
    transparent 80%
  );
}

/* Enhanced badge styles */
.enhanced-badge {
  position: relative;
  overflow: hidden;
  isolation: isolate;
}

.enhanced-badge::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.enhanced-badge:hover::before {
  transform: translateX(100%);
}

/* Improved typography for shared pages */
.shared-page-typography h1,
.shared-page-typography h2 {
  letter-spacing: -0.03em;
}

.shared-page-typography p {
  line-height: 1.7;
}

/* Floating animation for elements */
@keyframes floating {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.floating-animation {
  animation: floating 7s ease-in-out infinite;
}

.floating-animation-delayed {
  animation: floating 8s ease-in-out 1s infinite;
}

/* Enhanced code blocks in shared messages */
.chat-message pre {
  position: relative;
  overflow-x: auto;
  transition: all 0.3s ease;
}

.chat-message pre:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.chat-message pre::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  width: 20px;
  background: linear-gradient(to right, transparent, hsl(var(--background)));
  z-index: 10;
  pointer-events: none;
}

/* Customize shared page tables */
.shared-page-table th {
  background-color: hsl(var(--primary)/0.1);
  font-weight: 600;
}

.shared-page-table tr:nth-child(even) {
  background-color: hsl(var(--background)/0.5);
}

.shared-page-table tr:hover {
  background-color: hsl(var(--primary)/0.05);
}

/* Enhanced loading state for shared pages */
.shared-loading {
  position: relative;
}

.shared-loading::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(
    to right,
    transparent,
    hsl(var(--primary)),
    transparent
  );
  animation: shimmer 2s infinite;
  background-size: 200% 100%;
}

@layer utilities {
  /* Hide scrollbar but keep scroll functionality */
  .no-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  
  .no-scrollbar::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }
}

