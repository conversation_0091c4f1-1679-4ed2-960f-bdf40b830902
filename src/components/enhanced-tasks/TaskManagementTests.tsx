import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { EnhancedTodoItem } from '@/types/todo';
import {
  TestTube,
  CheckCircle2,
  XCircle,
  AlertTriangle,
  Clock,
  Database,
  Zap,
  Target,
  Users,
  Smartphone,
  Monitor,
  Activity,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface TestResult {
  id: string;
  name: string;
  description: string;
  status: 'passed' | 'failed' | 'warning' | 'running';
  duration: number;
  details?: string;
  category: 'functionality' | 'performance' | 'ui' | 'integration';
}

interface TaskManagementTestsProps {
  isOpen: boolean;
  onClose: () => void;
}

export function TaskManagementTests({ isOpen, onClose }: TaskManagementTestsProps) {
  const {
    addTask,
    updateTask,
    deleteTask,
    getFilteredTasks,
    board,
    subjects,
    presetExams,
  } = useEnhancedTodoStore();

  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<string>('');

  // Test suite definitions
  const testSuite = [
    // Functionality Tests
    {
      id: 'create-task',
      name: 'Create Task',
      description: 'Test task creation with all fields',
      category: 'functionality' as const,
      test: async () => {
        const testTask = {
          title: 'Test Task',
          description: 'Test Description',
          priority: 'high' as const,
          difficultyLevel: 'medium' as const,
          timeEstimate: 60,
          tags: ['test'],
          chapterTags: ['chapter1'],
        };
        
        await addTask(testTask);
        return 'Task created successfully';
      }
    },
    {
      id: 'update-task',
      name: 'Update Task',
      description: 'Test task updates and state changes',
      category: 'functionality' as const,
      test: async () => {
        const tasks = getFilteredTasks();
        if (tasks.length === 0) throw new Error('No tasks to update');
        
        const task = tasks[0];
        await updateTask(task.id, { completionPercentage: 50 });
        
        const updatedTasks = getFilteredTasks();
        const updatedTask = updatedTasks.find(t => t.id === task.id);
        
        return updatedTask?.completionPercentage === 50 
          ? 'Task updated successfully' 
          : 'Task update failed';
      }
    },
    {
      id: 'delete-task',
      name: 'Delete Task',
      description: 'Test task deletion',
      category: 'functionality' as const,
      test: async () => {
        const tasks = getFilteredTasks();
        if (tasks.length === 0) throw new Error('No tasks to delete');
        
        const taskId = tasks[tasks.length - 1].id;
        await deleteTask(taskId);
        
        const remainingTasks = getFilteredTasks();
        const deletedTask = remainingTasks.find(t => t.id === taskId);
        
        return !deletedTask ? 'Task deleted successfully' : 'Task deletion failed';
      }
    },
    {
      id: 'filter-tasks',
      name: 'Filter Tasks',
      description: 'Test task filtering functionality',
      category: 'functionality' as const,
      test: async () => {
        const allTasks = getFilteredTasks();
        const highPriorityTasks = allTasks.filter(t => t.priority === 'high');
        
        return `Found ${highPriorityTasks.length} high priority tasks out of ${allTasks.length} total`;
      }
    },

    // Performance Tests
    {
      id: 'render-performance',
      name: 'Render Performance',
      description: 'Test component render times',
      category: 'performance' as const,
      test: async () => {
        const start = performance.now();
        
        // Simulate heavy rendering
        await new Promise(resolve => {
          requestAnimationFrame(() => {
            requestAnimationFrame(resolve);
          });
        });
        
        const duration = performance.now() - start;
        
        if (duration > 16) {
          throw new Error(`Render time ${duration.toFixed(2)}ms exceeds 16ms target`);
        }
        
        return `Render completed in ${duration.toFixed(2)}ms`;
      }
    },
    {
      id: 'memory-usage',
      name: 'Memory Usage',
      description: 'Test memory consumption',
      category: 'performance' as const,
      test: async () => {
        if ('memory' in performance) {
          const memory = (performance as any).memory;
          const usedMB = memory.usedJSHeapSize / 1024 / 1024;
          
          if (usedMB > 100) {
            throw new Error(`Memory usage ${usedMB.toFixed(2)}MB exceeds 100MB limit`);
          }
          
          return `Memory usage: ${usedMB.toFixed(2)}MB`;
        }
        
        return 'Memory API not available';
      }
    },
    {
      id: 'large-dataset',
      name: 'Large Dataset Performance',
      description: 'Test performance with many tasks',
      category: 'performance' as const,
      test: async () => {
        const taskCount = getFilteredTasks().length;
        
        if (taskCount < 10) {
          return 'Warning: Test requires more tasks for meaningful results';
        }
        
        const start = performance.now();
        
        // Simulate filtering large dataset
        for (let i = 0; i < 100; i++) {
          getFilteredTasks();
        }
        
        const duration = performance.now() - start;
        const avgTime = duration / 100;
        
        if (avgTime > 5) {
          throw new Error(`Average filter time ${avgTime.toFixed(2)}ms too slow`);
        }
        
        return `Average filter time: ${avgTime.toFixed(2)}ms`;
      }
    },

    // UI Tests
    {
      id: 'responsive-design',
      name: 'Responsive Design',
      description: 'Test mobile responsiveness',
      category: 'ui' as const,
      test: async () => {
        const isMobile = window.innerWidth < 768;
        const hasTouch = 'ontouchstart' in window;
        
        return `Device: ${isMobile ? 'Mobile' : 'Desktop'}, Touch: ${hasTouch ? 'Yes' : 'No'}`;
      }
    },
    {
      id: 'accessibility',
      name: 'Accessibility',
      description: 'Test accessibility features',
      category: 'ui' as const,
      test: async () => {
        const buttons = document.querySelectorAll('button');
        const buttonsWithAriaLabel = Array.from(buttons).filter(btn => 
          btn.getAttribute('aria-label') || btn.textContent?.trim()
        );
        
        const accessibilityScore = (buttonsWithAriaLabel.length / buttons.length) * 100;
        
        if (accessibilityScore < 80) {
          throw new Error(`Accessibility score ${accessibilityScore.toFixed(1)}% below 80% target`);
        }
        
        return `Accessibility score: ${accessibilityScore.toFixed(1)}%`;
      }
    },

    // Integration Tests
    {
      id: 'data-persistence',
      name: 'Data Persistence',
      description: 'Test data saving and loading',
      category: 'integration' as const,
      test: async () => {
        const tasks = getFilteredTasks();
        const subjectCount = Object.keys(subjects).length;
        
        return `${tasks.length} tasks, ${subjectCount} subjects loaded from store`;
      }
    },
    {
      id: 'real-time-sync',
      name: 'Real-time Sync',
      description: 'Test real-time updates',
      category: 'integration' as const,
      test: async () => {
        // Simulate real-time update
        const timestamp = Date.now();
        
        return `Real-time sync simulated at ${new Date(timestamp).toLocaleTimeString()}`;
      }
    },
  ];

  // Run individual test
  const runTest = useCallback(async (test: typeof testSuite[0]): Promise<TestResult> => {
    const start = performance.now();
    
    try {
      const result = await test.test();
      const duration = performance.now() - start;
      
      return {
        id: test.id,
        name: test.name,
        description: test.description,
        status: result.includes('Warning') ? 'warning' : 'passed',
        duration,
        details: result,
        category: test.category,
      };
    } catch (error) {
      const duration = performance.now() - start;
      
      return {
        id: test.id,
        name: test.name,
        description: test.description,
        status: 'failed',
        duration,
        details: (error as Error).message,
        category: test.category,
      };
    }
  }, [getFilteredTasks, addTask, updateTask, deleteTask, subjects]);

  // Run all tests
  const runAllTests = useCallback(async () => {
    setIsRunning(true);
    setTestResults([]);
    
    const results: TestResult[] = [];
    
    for (const test of testSuite) {
      setCurrentTest(test.name);
      
      const result = await runTest(test);
      results.push(result);
      setTestResults([...results]);
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    setIsRunning(false);
    setCurrentTest('');
  }, [runTest]);

  // Get test statistics
  const stats = {
    total: testResults.length,
    passed: testResults.filter(r => r.status === 'passed').length,
    failed: testResults.filter(r => r.status === 'failed').length,
    warnings: testResults.filter(r => r.status === 'warning').length,
    avgDuration: testResults.length > 0 
      ? testResults.reduce((sum, r) => sum + r.duration, 0) / testResults.length 
      : 0,
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'passed': return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'failed': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'running': return <Clock className="h-4 w-4 text-blue-500 animate-spin" />;
    }
  };

  const getCategoryIcon = (category: TestResult['category']) => {
    switch (category) {
      case 'functionality': return <Target className="h-4 w-4" />;
      case 'performance': return <Zap className="h-4 w-4" />;
      case 'ui': return <Monitor className="h-4 w-4" />;
      case 'integration': return <Database className="h-4 w-4" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            Task Management Test Suite
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Test Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold">{stats.total}</div>
                <div className="text-sm text-muted-foreground">Total Tests</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">{stats.passed}</div>
                <div className="text-sm text-muted-foreground">Passed</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
                <div className="text-sm text-muted-foreground">Failed</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-yellow-600">{stats.warnings}</div>
                <div className="text-sm text-muted-foreground">Warnings</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold">{stats.avgDuration.toFixed(1)}ms</div>
                <div className="text-sm text-muted-foreground">Avg Duration</div>
              </CardContent>
            </Card>
          </div>

          {/* Test Controls */}
          <div className="flex items-center justify-between">
            <Button
              onClick={runAllTests}
              disabled={isRunning}
              className="flex items-center gap-2"
            >
              {isRunning ? (
                <>
                  <Activity className="h-4 w-4 animate-spin" />
                  Running Tests...
                </>
              ) : (
                <>
                  <TestTube className="h-4 w-4" />
                  Run All Tests
                </>
              )}
            </Button>

            {isRunning && (
              <div className="text-sm text-muted-foreground">
                Current: {currentTest}
              </div>
            )}

            <div className="text-sm text-muted-foreground">
              {testSuite.length} tests available
            </div>
          </div>

          {/* Test Results */}
          <div className="space-y-2 max-h-[400px] overflow-y-auto">
            {testResults.map((result, index) => (
              <motion.div
                key={result.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
              >
                <Card className={cn(
                  "transition-colors",
                  result.status === 'passed' && "border-green-200 bg-green-50",
                  result.status === 'failed' && "border-red-200 bg-red-50",
                  result.status === 'warning' && "border-yellow-200 bg-yellow-50"
                )}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(result.status)}
                        {getCategoryIcon(result.category)}
                        <div>
                          <div className="font-medium">{result.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {result.description}
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <Badge variant="outline" className="mb-1">
                          {result.category}
                        </Badge>
                        <div className="text-xs text-muted-foreground">
                          {result.duration.toFixed(1)}ms
                        </div>
                      </div>
                    </div>
                    
                    {result.details && (
                      <div className="mt-2 text-sm text-muted-foreground">
                        {result.details}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {testResults.length === 0 && !isRunning && (
            <div className="text-center py-12 text-muted-foreground">
              <TestTube className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No tests run yet</p>
              <p className="text-sm">Click "Run All Tests" to start testing</p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
