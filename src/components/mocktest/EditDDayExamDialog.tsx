import { useState, useEffect } from "react";
import { Calendar as CalendarIcon, Clock, Target, Save, X, Check, ChevronsUpDown } from "lucide-react";
import { format } from "date-fns";
import { DDayExam, dDayStorage } from "@/utils/mockTestLocalStorage";
import { TestCategory, ChapterProgress } from "@/types/mockTest";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogHeader,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import { ChapterProgressEditor } from "./ChapterProgressEditor";

interface EditDDayExamDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  exam: DDayExam | null;
  categories: TestCategory[];
  onSave: () => void;
  userId: string;
}

export function EditDDayExamDialog({
  open,
  onOpenChange,
  exam,
  categories,
  onSave,
  userId,
}: EditDDayExamDialogProps) {
  const [formData, setFormData] = useState({
    name: "",
    date: new Date(),
    time: "09:00",
    category: "",
    categories: [] as string[],
    description: "",
    priority: "medium" as DDayExam['priority'],
    reminderEnabled: true,
    chapters: [] as ChapterProgress[],
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form data when exam changes
  useEffect(() => {
    if (exam) {
      // Handle categories properly - prioritize categories array, fallback to single category
      const examCategories = exam.categories && exam.categories.length > 0
        ? exam.categories
        : (exam.category ? [exam.category] : []);

      setFormData({
        name: exam.name,
        date: new Date(exam.date),
        time: exam.time,
        category: exam.category || "no-category",
        categories: examCategories,
        description: exam.description || "",
        priority: exam.priority,
        reminderEnabled: exam.reminderSettings.enabled,
        chapters: [...(exam.preparationData.chapters || [])],
      });
    } else {
      // Reset form for new exam
      setFormData({
        name: "",
        date: new Date(),
        time: "09:00",
        category: "no-category",
        categories: [],
        description: "",
        priority: "medium",
        reminderEnabled: true,
        chapters: [],
      });
    }
  }, [exam]);

  const handleSave = async () => {
    if (!formData.name.trim()) {
      toast({
        title: "Error",
        description: "Exam name is required",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const updatedExam: DDayExam = {
        ...exam!,
        name: formData.name.trim(),
        date: format(formData.date, "yyyy-MM-dd"),
        time: formData.time,
        category: formData.category === 'no-category' ? undefined : formData.category,
        categories: formData.categories.length > 0 ? formData.categories : undefined,
        description: formData.description || undefined,
        priority: formData.priority,
        reminderSettings: {
          ...exam!.reminderSettings,
          enabled: formData.reminderEnabled,
        },
        preparationData: {
          chapters: formData.chapters,
          totalTopics: formData.chapters.map(ch => ch.chapterName), // Keep for backward compatibility
        },
        updatedAt: new Date().toISOString(),
      };

      const updated = dDayStorage.update(userId, exam!.id, updatedExam);
      
      if (updated) {
        toast({
          title: "Success",
          description: "Exam updated successfully",
        });
        onSave();
        onOpenChange(false);
      } else {
        throw new Error("Failed to find and update the exam");
      }
    } catch (error) {
      console.error('Error updating exam:', error);
      toast({
        title: "Error",
        description: "Failed to update exam",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!exam) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="h-5 w-5 text-violet-600" />
            Edit Exam
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column - Basic Info */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Exam Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter exam name"
              />
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label>Date *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.date && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.date ? format(formData.date, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.date}
                      onSelect={(date) => date && setFormData(prev => ({ ...prev, date }))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label htmlFor="time">Time</Label>
                <Input
                  id="time"
                  type="time"
                  value={formData.time}
                  onChange={(e) => setFormData(prev => ({ ...prev, time: e.target.value }))}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label>Categories</Label>
                <div className="space-y-2">
                  <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                    {categories.map((category) => (
                      <div key={category.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`category-${category.id}`}
                          checked={formData.categories.includes(category.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setFormData(prev => ({
                                ...prev,
                                categories: [...prev.categories, category.id]
                              }));
                            } else {
                              setFormData(prev => ({
                                ...prev,
                                categories: prev.categories.filter(id => id !== category.id)
                              }));
                            }
                          }}
                        />
                        <Label
                          htmlFor={`category-${category.id}`}
                          className="text-sm font-normal cursor-pointer"
                        >
                          {category.name}
                        </Label>
                      </div>
                    ))}
                  </div>
                  {formData.categories.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {formData.categories.map((categoryId) => {
                        const category = categories.find(c => c.id === categoryId);
                        return category ? (
                          <Badge key={categoryId} variant="secondary" className="text-xs">
                            {category.name}
                            <X
                              className="h-3 w-3 ml-1 cursor-pointer"
                              onClick={() => setFormData(prev => ({
                                ...prev,
                                categories: prev.categories.filter(id => id !== categoryId)
                              }))}
                            />
                          </Badge>
                        ) : null;
                      })}
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label>Priority</Label>
                <Select
                  value={formData.priority}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value as DDayExam['priority'] }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Add exam description..."
                rows={3}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="reminders"
                checked={formData.reminderEnabled}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, reminderEnabled: checked }))}
              />
              <Label htmlFor="reminders">Enable Reminders</Label>
            </div>
          </div>

          {/* Right Column - Chapter Progress */}
          <div>
            <ChapterProgressEditor
              chapters={formData.chapters}
              onChaptersChange={(chapters) => setFormData(prev => ({ ...prev, chapters }))}
              className="h-full"
            />
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSubmitting}
            className="bg-gradient-to-r from-violet-600 to-purple-600 hover:from-violet-700 hover:to-purple-700"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSubmitting ? "Saving..." : "Save Changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
