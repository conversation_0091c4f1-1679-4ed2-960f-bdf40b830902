import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import { <PERSON><PERSON><PERSON>, Bar } from 'react-chartjs-2';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card.tsx';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { MockTest, TestCategory } from '@/types/mockTest';
import { PieChart, BarChart3, Layers } from 'lucide-react';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface CategoryAnalyticsChartProps {
  tests: MockTest[];
  categories: TestCategory[];
}

export function CategoryAnalyticsChart({ tests, categories }: CategoryAnalyticsChartProps) {
  // Calculate category-wise performance
  const categoryData = React.useMemo(() => {
    const categoryMap = new Map<string, { total: number; obtained: number; count: number; name: string }>();
    
    // Initialize with categories
    categories.forEach(category => {
      categoryMap.set(category.id, {
        total: 0,
        obtained: 0,
        count: 0,
        name: category.name
      });
    });

    // Add uncategorized
    categoryMap.set('uncategorized', {
      total: 0,
      obtained: 0,
      count: 0,
      name: 'Uncategorized'
    });

    tests.forEach(test => {
      const categoryId = test.categoryId || 'uncategorized';
      const existing = categoryMap.get(categoryId);
      if (existing) {
        categoryMap.set(categoryId, {
          ...existing,
          total: existing.total + (test.totalMarks || 0),
          obtained: existing.obtained + (test.marksObtained || 0),
          count: existing.count + 1
        });
      }
    });

    return Array.from(categoryMap.entries())
      .filter(([_, data]) => data.count > 0)
      .map(([id, data]) => ({
        id,
        name: data.name,
        percentage: data.total > 0 ? (data.obtained / data.total) * 100 : 0,
        totalTests: data.count,
        totalMarks: data.total,
        obtainedMarks: data.obtained
      }));
  }, [tests, categories]);

  const colors = [
    '#8B5CF6', '#06B6D4', '#10B981', '#F59E0B', '#EF4444', 
    '#8B5A2B', '#6366F1', '#EC4899', '#84CC16', '#F97316'
  ];

  const doughnutData = {
    labels: categoryData.map(item => item.name),
    datasets: [
      {
        label: 'Tests by Category',
        data: categoryData.map(item => item.totalTests),
        backgroundColor: colors.slice(0, categoryData.length),
        borderColor: colors.slice(0, categoryData.length),
        borderWidth: 2,
      }
    ]
  };

  const barData = {
    labels: categoryData.map(item => item.name),
    datasets: [
      {
        label: 'Average Performance (%)',
        data: categoryData.map(item => item.percentage),
        backgroundColor: colors.slice(0, categoryData.length).map(color => `${color}80`),
        borderColor: colors.slice(0, categoryData.length),
        borderWidth: 2,
        borderRadius: 8,
        borderSkipped: false,
      }
    ]
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = ((context.parsed / total) * 100).toFixed(1);
            return `${context.label}: ${context.parsed} tests (${percentage}%)`;
          }
        }
      }
    }
  };

  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        ticks: {
          callback: function(value: any) {
            return value + '%';
          }
        }
      }
    }
  };

  if (categoryData.length === 0) {
    return (
      <Card className="border border-border bg-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <PieChart className="h-5 w-5 text-rose-600 dark:text-rose-400" />
            <span className="text-foreground">Category Analytics</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <p className="text-muted-foreground">No test data available for analysis</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden border border-border bg-card hover:shadow-md transition-shadow duration-300">
      <CardHeader className="border-b border-border">
        <CardTitle className="flex items-center gap-3 text-xl">
          <div className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800">
            <PieChart className="h-6 w-6 text-rose-600 dark:text-rose-400" />
          </div>
          <span className="text-foreground">
            Category Analytics
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <Tabs defaultValue="distribution" className="w-full">
          <TabsList className="grid w-full grid-cols-2 bg-muted/30 border border-border">
            <TabsTrigger
              value="distribution"
              className="gap-2 data-[state=active]:bg-card data-[state=active]:text-rose-600 data-[state=active]:shadow-sm transition-colors duration-200"
            >
              <PieChart className="h-4 w-4" />
              Distribution
            </TabsTrigger>
            <TabsTrigger
              value="performance"
              className="gap-2 data-[state=active]:bg-card data-[state=active]:text-rose-600 data-[state=active]:shadow-sm transition-colors duration-200"
            >
              <BarChart3 className="h-4 w-4" />
              Performance
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="distribution" className="mt-6">
            <div className="h-[350px] relative">
              <Doughnut data={doughnutData} options={doughnutOptions} />
            </div>
            <div className="mt-6 space-y-2">
              {categoryData.map((category, index) => (
                <div key={category.id} className="flex items-center justify-between p-3 rounded-lg bg-gray-50 border border-gray-100 hover:bg-gray-100 transition-colors duration-200 dark:bg-gray-800/50 dark:border-gray-700 dark:hover:bg-gray-700/50">
                  <div className="flex items-center gap-3">
                    <div 
                      className="w-3 h-3 rounded-full flex-shrink-0" 
                      style={{ backgroundColor: colors[index % colors.length] }}
                    />
                    <span className="font-medium text-gray-800 dark:text-gray-200">{category.name}</span>
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {category.totalTests} test{category.totalTests !== 1 ? 's' : ''} • {category.percentage.toFixed(1)}%
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="performance" className="mt-6">
            <div className="h-96 p-4 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
              <Bar data={barData} options={barOptions} />
            </div>
          </TabsContent>
        </Tabs>

        <div className="mt-8">
          <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100 flex items-center gap-2">
            <Layers className="h-5 w-5 text-rose-600 dark:text-rose-400" />
            Category Summary
          </h3>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {categoryData.map((category, index) => (
              <div
                key={category.id}
                className="p-5 rounded-xl border-0 shadow-lg bg-white dark:bg-gray-800 hover:shadow-xl hover:-translate-y-1 transition-all duration-300 group relative dark:border-gray-700"
              >
                <div
                  className="absolute inset-0 bg-white dark:bg-gray-800 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"
                />
                <div className="relative z-10">
                  <div className="flex items-center gap-3 mb-3">
                    <div
                      className="w-5 h-5 rounded-full shadow-md border-2 border-white dark:border-gray-800"
                      style={{ backgroundColor: colors[index] }}
                    />
                    <h4 className="font-semibold text-gray-900 dark:text-gray-100">{category.name}</h4>
                  </div>
                  <div className="space-y-2">
                    <p className="text-3xl font-bold" style={{ color: colors[index] }}>
                      {category.percentage.toFixed(1)}%
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400 font-medium">
                      {category.totalTests} test{category.totalTests !== 1 ? 's' : ''} completed
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {category.obtainedMarks}/{category.totalMarks} marks
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
