import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from '../ui/card';
import { <PERSON>, Lightbulb, TrendingUp, <PERSON><PERSON>Triangle, Shield } from 'lucide-react';

export const SWOTAnalysisWidget: React.FC = () => {
  return (
    <Card className="bg-white dark:bg-gray-900/50 border-l-4 border-l-violet-500 hover:shadow-lg transition-all duration-300 h-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-3 text-xl">
          <Brain className="h-6 w-6 text-violet-500" />
          <span className="text-gray-800 dark:text-gray-200">
            SWOT Analysis
          </span>
        </CardTitle>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          AI-powered self-assessment and study pattern analysis
        </p>
      </CardHeader>
      <CardContent className="pb-6">
        {/* SWOT Grid Placeholder */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          {/* Strengths */}
          <div className="p-4 rounded-lg border-2 border-dashed border-green-300 bg-green-50 dark:bg-green-900/10 hover:border-green-400 transition-colors duration-200">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-700 dark:text-green-400">Strengths</span>
            </div>
            <p className="text-xs text-green-600 dark:text-green-500">
              What are you good at in your studies?
            </p>
          </div>

          {/* Weaknesses */}
          <div className="p-4 rounded-lg border-2 border-dashed border-red-300 bg-red-50 dark:bg-red-900/10 hover:border-red-400 transition-colors duration-200">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <span className="text-sm font-medium text-red-700 dark:text-red-400">Weaknesses</span>
            </div>
            <p className="text-xs text-red-600 dark:text-red-500">
              What areas need improvement?
            </p>
          </div>

          {/* Opportunities */}
          <div className="p-4 rounded-lg border-2 border-dashed border-blue-300 bg-blue-50 dark:bg-blue-900/10 hover:border-blue-400 transition-colors duration-200">
            <div className="flex items-center gap-2 mb-2">
              <Lightbulb className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-700 dark:text-blue-400">Opportunities</span>
            </div>
            <p className="text-xs text-blue-600 dark:text-blue-500">
              What opportunities can you leverage?
            </p>
          </div>

          {/* Threats */}
          <div className="p-4 rounded-lg border-2 border-dashed border-amber-300 bg-amber-50 dark:bg-amber-900/10 hover:border-amber-400 transition-colors duration-200">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="h-4 w-4 text-amber-600" />
              <span className="text-sm font-medium text-amber-700 dark:text-amber-400">Threats</span>
            </div>
            <p className="text-xs text-amber-600 dark:text-amber-500">
              What challenges might you face?
            </p>
          </div>
        </div>

        {/* AI Analysis Button */}
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Click quadrants to add your analysis
          </div>
          <button className="px-4 py-2 bg-violet-600 hover:bg-violet-700 text-white text-sm rounded-lg transition-colors duration-200 flex items-center gap-2">
            <Brain className="h-4 w-4" />
            AI Analyze
          </button>
        </div>

        {/* Placeholder for AI Insights */}
        <div className="mt-4 p-4 bg-violet-50 dark:bg-violet-900/10 rounded-lg border border-violet-200 dark:border-violet-800">
          <div className="flex items-center gap-2 mb-2">
            <Lightbulb className="h-4 w-4 text-violet-600" />
            <span className="text-sm font-medium text-violet-700 dark:text-violet-400">AI Insights</span>
          </div>
          <p className="text-xs text-violet-600 dark:text-violet-500">
            Complete your SWOT analysis to receive AI-powered insights about your study methods and GRIT assessment.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
