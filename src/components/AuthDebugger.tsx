import { useEffect, useState } from 'react';
import { useSupabaseAuth } from '../contexts/SupabaseAuthContext';
import { supabase } from '../integrations/supabase/client';
import { testAuthentication } from '../utils/testAuth';

export const AuthDebugger = () => {
  const { user, loading } = useSupabaseAuth();
  const [sessionInfo, setSessionInfo] = useState<any>(null);
  const [testResult, setTestResult] = useState<any>(null);

  useEffect(() => {
    const checkSession = async () => {
      const { data: { session }, error } = await supabase.auth.getSession();
      setSessionInfo({ session: session?.user?.id, error: error?.message });
    };
    
    checkSession();
  }, [user]);

  const runTest = async () => {
    const result = await testAuthentication();
    setTestResult(result);
  };

  if (import.meta.env.PROD) {
    return null; // Don't show in production
  }

  return (
    <div style={{
      position: 'fixed',
      top: 10,
      right: 10,
      background: 'rgba(0,0,0,0.8)',
      color: 'white',
      padding: '10px',
      borderRadius: '5px',
      fontSize: '12px',
      zIndex: 9999,
      maxWidth: '300px'
    }}>
      <h4>Auth Debug</h4>
      <div>Loading: {loading ? 'Yes' : 'No'}</div>
      <div>User ID: {user?.id || 'None'}</div>
      <div>User Email: {user?.email || 'None'}</div>
      <div>Session ID: {sessionInfo?.session || 'None'}</div>
      {sessionInfo?.error && <div style={{color: 'red'}}>Session Error: {sessionInfo.error}</div>}
      
      <button 
        onClick={runTest}
        style={{
          marginTop: '5px',
          padding: '2px 5px',
          fontSize: '10px'
        }}
      >
        Test Auth
      </button>
      
      {testResult && (
        <div style={{marginTop: '5px', fontSize: '10px'}}>
          <div>Test Session: {testResult.session?.user?.id || 'None'}</div>
          {testResult.error && <div style={{color: 'red'}}>Test Error: {testResult.error.message}</div>}
        </div>
      )}
    </div>
  );
};
