import { useState, useEffect, useMemo } from "react";
import { motion } from "framer-motion";
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  LineChart, 
  Target,
  Calendar,
  Award,
  Filter,
  Download
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js';
import { Line, Bar } from 'react-chartjs-2';
import { MockTest, TestCategory } from "@/types/mockTest";
import { categoryStorage } from "@/utils/mockTestLocalStorage";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { cn } from "@/lib/utils";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface PerformanceTrendAnalyticsProps {
  tests: MockTest[];
}

interface TrendData {
  date: string;
  score: number;
  totalMarks: number;
  percentage: number;
  testName: string;
  category?: string;
}

interface CategoryPerformance {
  categoryId: string;
  categoryName: string;
  tests: MockTest[];
  averageScore: number;
  averagePercentage: number;
  trend: 'up' | 'down' | 'stable';
  improvement: number;
}

export function PerformanceTrendAnalytics({ tests }: PerformanceTrendAnalyticsProps) {
  const { user } = useSupabaseAuth();
  const [categories, setCategories] = useState<TestCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [timeRange, setTimeRange] = useState<string>("all");
  const [chartType, setChartType] = useState<"line" | "bar">("line");

  // Load categories
  useEffect(() => {
    if (user) {
      const cats = categoryStorage.getAll(user.id);
      setCategories(cats);
    }
  }, [user]);

  // Filter tests based on selected filters
  const filteredTests = useMemo(() => {
    let filtered = tests.filter(test => test.marksObtained !== undefined && test.totalMarks !== undefined);

    // Filter by category
    if (selectedCategory !== "all") {
      if (selectedCategory === "uncategorized") {
        filtered = filtered.filter(test => !test.categoryId);
      } else {
        filtered = filtered.filter(test => test.categoryId === selectedCategory);
      }
    }

    // Filter by time range
    if (timeRange !== "all") {
      const now = new Date();
      const cutoffDate = new Date();
      
      switch (timeRange) {
        case "7d":
          cutoffDate.setDate(now.getDate() - 7);
          break;
        case "30d":
          cutoffDate.setDate(now.getDate() - 30);
          break;
        case "90d":
          cutoffDate.setDate(now.getDate() - 90);
          break;
        case "1y":
          cutoffDate.setFullYear(now.getFullYear() - 1);
          break;
      }
      
      filtered = filtered.filter(test => new Date(test.date) >= cutoffDate);
    }

    // Sort by date
    return filtered.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }, [tests, selectedCategory, timeRange]);

  // Prepare trend data
  const trendData: TrendData[] = useMemo(() => {
    return filteredTests.map(test => ({
      date: test.date,
      score: test.marksObtained!,
      totalMarks: test.totalMarks!,
      percentage: (test.marksObtained! / test.totalMarks!) * 100,
      testName: test.name,
      category: test.categoryId ? categories.find(cat => cat.id === test.categoryId)?.name : undefined,
    }));
  }, [filteredTests, categories]);

  // Calculate category-wise performance
  const categoryPerformance: CategoryPerformance[] = useMemo(() => {
    const categoryMap = new Map<string, MockTest[]>();
    
    // Group tests by category
    filteredTests.forEach(test => {
      const categoryId = test.categoryId || "uncategorized";
      if (!categoryMap.has(categoryId)) {
        categoryMap.set(categoryId, []);
      }
      categoryMap.get(categoryId)!.push(test);
    });

    return Array.from(categoryMap.entries()).map(([categoryId, categoryTests]) => {
      const categoryName = categoryId === "uncategorized" 
        ? "Uncategorized" 
        : categories.find(cat => cat.id === categoryId)?.name || "Unknown";

      const scores = categoryTests.map(test => (test.marksObtained! / test.totalMarks!) * 100);
      const averagePercentage = scores.reduce((sum, score) => sum + score, 0) / scores.length;
      const averageScore = categoryTests.reduce((sum, test) => sum + test.marksObtained!, 0) / categoryTests.length;

      // Calculate trend (compare first half vs second half)
      let trend: 'up' | 'down' | 'stable' = 'stable';
      let improvement = 0;
      
      if (scores.length >= 4) {
        const midPoint = Math.floor(scores.length / 2);
        const firstHalf = scores.slice(0, midPoint);
        const secondHalf = scores.slice(midPoint);
        
        const firstHalfAvg = firstHalf.reduce((sum, score) => sum + score, 0) / firstHalf.length;
        const secondHalfAvg = secondHalf.reduce((sum, score) => sum + score, 0) / secondHalf.length;
        
        improvement = secondHalfAvg - firstHalfAvg;
        
        if (improvement > 2) trend = 'up';
        else if (improvement < -2) trend = 'down';
        else trend = 'stable';
      }

      return {
        categoryId,
        categoryName,
        tests: categoryTests,
        averageScore,
        averagePercentage,
        trend,
        improvement,
      };
    }).sort((a, b) => b.averagePercentage - a.averagePercentage);
  }, [filteredTests, categories]);

  // Chart data for line/bar chart
  const chartData = useMemo(() => {
    const labels = trendData.map((data, index) => `Test ${index + 1}`);
    const percentages = trendData.map(data => data.percentage);
    const scores = trendData.map(data => data.score);

    return {
      labels,
      datasets: [
        {
          label: 'Percentage Score',
          data: percentages,
          borderColor: 'rgb(139, 92, 246)',
          backgroundColor: chartType === 'line' ? 'rgba(139, 92, 246, 0.1)' : 'rgba(139, 92, 246, 0.8)',
          fill: chartType === 'line',
          tension: 0.4,
        },
      ],
    };
  }, [trendData, chartType]);

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Performance Trend Over Time',
      },
      tooltip: {
        callbacks: {
          title: (context: any) => {
            const index = context[0].dataIndex;
            return trendData[index]?.testName || `Test ${index + 1}`;
          },
          label: (context: any) => {
            const index = context.dataIndex;
            const data = trendData[index];
            return [
              `Score: ${data.score}/${data.totalMarks}`,
              `Percentage: ${data.percentage.toFixed(1)}%`,
              `Date: ${new Date(data.date).toLocaleDateString()}`,
              data.category ? `Category: ${data.category}` : '',
            ].filter(Boolean);
          },
        },
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        title: {
          display: true,
          text: 'Percentage Score',
        },
      },
      x: {
        title: {
          display: true,
          text: 'Tests (Chronological Order)',
        },
      },
    },
  };

  // Calculate overall statistics
  const overallStats = useMemo(() => {
    if (trendData.length === 0) return null;

    const percentages = trendData.map(data => data.percentage);
    const average = percentages.reduce((sum, p) => sum + p, 0) / percentages.length;
    const highest = Math.max(...percentages);
    const lowest = Math.min(...percentages);
    
    // Calculate trend
    let overallTrend: 'up' | 'down' | 'stable' = 'stable';
    if (percentages.length >= 2) {
      const recent = percentages.slice(-3).reduce((sum, p) => sum + p, 0) / Math.min(3, percentages.length);
      const earlier = percentages.slice(0, 3).reduce((sum, p) => sum + p, 0) / Math.min(3, percentages.length);
      
      if (recent > earlier + 2) overallTrend = 'up';
      else if (recent < earlier - 2) overallTrend = 'down';
    }

    return { average, highest, lowest, trend: overallTrend };
  }, [trendData]);

  if (!user) {
    return <div>Please log in to view performance analytics.</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header with filters */}
      <Card className="border border-border shadow-lg bg-card">
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <CardTitle className="flex items-center gap-2 text-xl font-bold">
              <div className="p-2 rounded-lg bg-violet-600 shadow-lg">
                <BarChart3 className="h-5 w-5 text-white" />
              </div>
              <span className="text-foreground">
                Performance Trend Analytics
              </span>
            </CardTitle>
            <div className="flex flex-wrap gap-2">
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="uncategorized">Uncategorized</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Time Range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 3 months</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>

              <Select value={chartType} onValueChange={(value: "line" | "bar") => setChartType(value)}>
                <SelectTrigger className="w-24">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="line">Line</SelectItem>
                  <SelectItem value="bar">Bar</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Overall Statistics */}
      {overallStats && (
        <div className="grid gap-4 md:grid-cols-4">
          <Card className="border border-border shadow-lg bg-card hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-violet-600 dark:text-violet-400 font-medium">Average Score</p>
                  <p className="text-2xl font-bold text-foreground">{overallStats.average.toFixed(1)}%</p>
                </div>
                <div className="p-2 rounded-full bg-violet-100 dark:bg-violet-900/30">
                  <Target className="h-6 w-6 text-violet-600 dark:text-violet-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border border-border shadow-lg bg-card hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-emerald-600 dark:text-emerald-400 font-medium">Highest Score</p>
                  <p className="text-2xl font-bold text-foreground">{overallStats.highest.toFixed(1)}%</p>
                </div>
                <div className="p-2 rounded-full bg-emerald-100 dark:bg-emerald-900/30">
                  <Award className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border border-border shadow-lg bg-card hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-rose-600 dark:text-rose-400 font-medium">Lowest Score</p>
                  <p className="text-2xl font-bold text-foreground">{overallStats.lowest.toFixed(1)}%</p>
                </div>
                <div className="p-2 rounded-full bg-rose-100 dark:bg-rose-900/30">
                  <TrendingDown className="h-6 w-6 text-rose-600 dark:text-rose-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border border-border shadow-lg bg-card hover:shadow-xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600/70 font-medium dark:text-gray-300/70">Overall Trend</p>
                  <div className="flex items-center gap-2">
                    {overallStats.trend === 'up' && (
                      <>
                        <TrendingUp className="h-5 w-5 text-emerald-500 dark:text-emerald-400" />
                        <span className="text-emerald-600 font-semibold dark:text-emerald-400">Improving</span>
                      </>
                    )}
                    {overallStats.trend === 'down' && (
                      <>
                        <TrendingDown className="h-5 w-5 text-rose-500 dark:text-rose-400" />
                        <span className="text-rose-600 font-semibold dark:text-rose-400">Declining</span>
                      </>
                    )}
                    {overallStats.trend === 'stable' && (
                      <>
                        <LineChart className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                        <span className="text-gray-600 font-semibold dark:text-gray-400">Stable</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Chart */}
      {trendData.length > 0 ? (
        <Card className="border border-border shadow-xl bg-card">
          <CardHeader className="border-b border-border">
            <CardTitle className="text-xl font-bold text-foreground">Performance Over Time</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="h-80">
              {chartType === 'line' ? (
                <Line data={chartData} options={chartOptions} />
              ) : (
                <Bar data={chartData} options={chartOptions} />
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card className="border border-border shadow-lg bg-card">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="p-4 rounded-full bg-violet-100 dark:bg-violet-900/30 mb-4">
              <BarChart3 className="h-12 w-12 text-violet-600 dark:text-violet-400" />
            </div>
            <h3 className="text-lg font-semibold mb-2 text-foreground">No test data available</h3>
            <p className="text-muted-foreground text-center">
              Complete some mock tests to see your performance trends
            </p>
          </CardContent>
        </Card>
      )}

      {/* Category-wise Performance */}
      {categoryPerformance.length > 0 && (
        <Card className="border border-border shadow-xl bg-card">
          <CardHeader className="border-b border-border">
            <CardTitle className="text-xl font-bold text-foreground">Category-wise Performance</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              {categoryPerformance.map((category, index) => (
                <motion.div
                  key={category.categoryId}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between p-4 rounded-lg border border-border bg-muted/30 shadow-md hover:shadow-lg transition-all duration-300"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="font-semibold text-foreground">{category.categoryName}</h4>
                      <Badge variant="outline">{category.tests.length} tests</Badge>
                      {category.trend === 'up' && (
                        <Badge className="bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300">
                          <TrendingUp className="h-3 w-3 mr-1" />
                          Improving
                        </Badge>
                      )}
                      {category.trend === 'down' && (
                        <Badge className="bg-rose-100 text-rose-800 dark:bg-rose-900/30 dark:text-rose-300">
                          <TrendingDown className="h-3 w-3 mr-1" />
                          Declining
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-6 text-sm text-muted-foreground">
                      <span>Average: {category.averagePercentage.toFixed(1)}%</span>
                      {category.improvement !== 0 && (
                        <span className={cn(
                          category.improvement > 0 ? "text-emerald-600 dark:text-emerald-400" : "text-rose-600 dark:text-rose-400"
                        )}>
                          {category.improvement > 0 ? "+" : ""}{category.improvement.toFixed(1)}% change
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                      {category.averagePercentage.toFixed(1)}%
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
