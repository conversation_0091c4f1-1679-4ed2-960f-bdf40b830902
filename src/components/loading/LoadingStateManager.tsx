import React from 'react';
import { AnimatePresence } from 'framer-motion';
import { LoadingScreen } from '../ui/loading-screen';
import { AILoadingScreen } from './AILoadingScreen';
import { AnalyticsLoadingScreen } from './AnalyticsLoadingScreen';
import { AuthLoadingScreen } from './AuthLoadingScreen';
import { GroupsLoadingScreen } from './GroupsLoadingScreen';

export type LoadingType = 
  | 'default'
  | 'ai'
  | 'analytics' 
  | 'auth'
  | 'groups'
  | 'tasks'
  | 'profile'
  | 'chat';

interface LoadingStateManagerProps {
  isLoading: boolean;
  type?: LoadingType;
  message?: string;
  variant?: string;
  className?: string;
}

export const LoadingStateManager: React.FC<LoadingStateManagerProps> = ({
  isLoading,
  type = 'default',
  message,
  variant,
  className,
}) => {
  const renderLoadingComponent = () => {
    switch (type) {
      case 'ai':
        return (
          <AILoadingScreen
            message={message}
            variant={variant as any}
            className={className}
          />
        );
      
      case 'analytics':
        return (
          <AnalyticsLoadingScreen
            className={className}
            showProgress={variant !== 'minimal'}
          />
        );
      
      case 'auth':
        return (
          <AuthLoadingScreen
            message={message}
            variant={variant as any}
            className={className}
          />
        );
      
      case 'groups':
        return (
          <GroupsLoadingScreen
            variant={variant as any}
            className={className}
          />
        );
      
      case 'tasks':
        return (
          <LoadingScreen
            title={message || 'Loading your workspace...'}
            subtitle="Organizing your tasks and productivity tools"
            variant="gradient"
            className={className}
          />
        );
      
      case 'profile':
        return (
          <LoadingScreen
            title={message || 'Loading profile...'}
            subtitle="Setting up your personalized experience"
            variant="branded"
            className={className}
          />
        );
      
      case 'chat':
        return (
          <LoadingScreen
            title={message || 'Loading chats...'}
            subtitle="Retrieving your conversation history"
            variant="minimal"
            className={className}
          />
        );
      
      default:
        return (
          <LoadingScreen
            title={message || 'Loading...'}
            variant={variant as any}
            className={className}
          />
        );
    }
  };

  return (
    <AnimatePresence mode="wait">
      {isLoading && renderLoadingComponent()}
    </AnimatePresence>
  );
};

// Hook for easy loading state management
export const useLoadingState = (initialType: LoadingType = 'default') => {
  const [isLoading, setIsLoading] = React.useState(false);
  const [loadingType, setLoadingType] = React.useState<LoadingType>(initialType);
  const [message, setMessage] = React.useState<string>();
  const [variant, setVariant] = React.useState<string>();

  const startLoading = (
    type?: LoadingType,
    loadingMessage?: string,
    loadingVariant?: string
  ) => {
    if (type) setLoadingType(type);
    if (loadingMessage) setMessage(loadingMessage);
    if (loadingVariant) setVariant(loadingVariant);
    setIsLoading(true);
  };

  const stopLoading = () => {
    setIsLoading(false);
  };

  const LoadingComponent = () => (
    <LoadingStateManager
      isLoading={isLoading}
      type={loadingType}
      message={message}
      variant={variant}
    />
  );

  return {
    isLoading,
    startLoading,
    stopLoading,
    LoadingComponent,
  };
};