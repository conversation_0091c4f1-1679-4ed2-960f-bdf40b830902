import React from 'react';
import { TodaysTasksWidget } from './TodaysTasksWidget';
import { UpcomingExamsWidget } from './UpcomingExamsWidget';
import { SWOTAnalysisWidget } from './SWOTAnalysisWidget';
import { PrepInsightBoardWidget } from './PrepInsightBoardWidget';
import { AnalyticsOverviewWidget } from './AnalyticsOverviewWidget';
import { DDayCountdownWidget } from './DDayCountdownWidget';

interface DashboardGridProps {
  userId: string;
}

export const DashboardGrid: React.FC<DashboardGridProps> = ({ userId }) => {
  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="mb-12">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-3">
          Welcome to your Dashboard
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Here's an overview of your academic progress and activities
        </p>
      </div>

      {/* 2x3 Dashboard Grid Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Row 1: Top Row */}
        {/* Today's Tasks Widget - Top Left */}
        <div className="w-full">
          <TodaysTasksWidget />
        </div>

        {/* Upcoming Exams Widget - Top Right */}
        <div className="w-full">
          <UpcomingExamsWidget />
        </div>

        {/* Row 2: Middle Row */}
        {/* SWOT Analysis Widget - Middle Left */}
        <div className="w-full">
          <SWOTAnalysisWidget />
        </div>

        {/* Prep Insight Board Widget - Middle Right */}
        <div className="w-full">
          <PrepInsightBoardWidget />
        </div>

        {/* Row 3: Bottom Row */}
        {/* Analytics Overview Widget - Bottom Left */}
        <div className="w-full">
          <AnalyticsOverviewWidget />
        </div>

        {/* Final D-Day Countdown Timer - Bottom Right */}
        <div className="w-full">
          <DDayCountdownWidget />
        </div>
      </div>
    </div>
  );
};