import { useState, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  Calendar, 
  ChevronLeft, 
  ChevronRight, 
  Clock,
  Target,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { DDayExam, chapterProgressUtils } from "@/utils/mockTestLocalStorage";
import { cn } from "@/lib/utils";
import { 
  format, 
  startOfMonth, 
  endOfMonth, 
  eachDayOfInterval, 
  isSameMonth, 
  isSameDay, 
  addMonths, 
  subMonths,
  isToday,
  getDay
} from "date-fns";

interface ExamCalendarViewProps {
  exams: DDayExam[];
  onExamClick?: (exam: DDayExam) => void;
}

export function ExamCalendarView({ exams, onExamClick }: ExamCalendarViewProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);

  // Get calendar data
  const calendarData = useMemo(() => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const calendarStart = new Date(monthStart);
    const calendarEnd = new Date(monthEnd);
    
    // Adjust to show full weeks
    calendarStart.setDate(calendarStart.getDate() - getDay(calendarStart));
    calendarEnd.setDate(calendarEnd.getDate() + (6 - getDay(calendarEnd)));
    
    const days = eachDayOfInterval({ start: calendarStart, end: calendarEnd });
    
    // Group exams by date
    const examsByDate = new Map<string, DDayExam[]>();
    exams.forEach(exam => {
      const dateKey = exam.date;
      if (!examsByDate.has(dateKey)) {
        examsByDate.set(dateKey, []);
      }
      examsByDate.get(dateKey)!.push(exam);
    });
    
    return { days, examsByDate };
  }, [currentDate, exams]);

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => direction === 'prev' ? subMonths(prev, 1) : addMonths(prev, 1));
  };

  const getExamsForDate = (date: Date) => {
    const dateKey = format(date, 'yyyy-MM-dd');
    return calendarData.examsByDate.get(dateKey) || [];
  };

  const getDayClasses = (date: Date, exams: DDayExam[]) => {
    const baseClasses = "relative p-2 h-24 border border-border/50 transition-all duration-200 cursor-pointer";
    
    if (!isSameMonth(date, currentDate)) {
      return cn(baseClasses, "bg-muted/30 text-muted-foreground");
    }
    
    if (isToday(date)) {
      return cn(baseClasses, "bg-violet-50 dark:bg-violet-950/30 border-violet-200 dark:border-violet-800");
    }
    
    if (selectedDate && isSameDay(date, selectedDate)) {
      return cn(baseClasses, "bg-violet-100 dark:bg-violet-900/50 border-violet-300 dark:border-violet-700");
    }
    
    if (exams.length > 0) {
      const hasUrgent = exams.some(exam => {
        const daysUntil = Math.ceil((new Date(exam.date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
        return daysUntil <= 3 || exam.priority === 'critical';
      });
      
      if (hasUrgent) {
        return cn(baseClasses, "bg-red-50 dark:bg-red-950/30 border-red-200 dark:border-red-800 hover:bg-red-100 dark:hover:bg-red-900/50");
      }
      
      return cn(baseClasses, "bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-800 hover:bg-blue-100 dark:hover:bg-blue-900/50");
    }
    
    return cn(baseClasses, "hover:bg-muted/50");
  };

  const getPriorityColor = (priority: DDayExam['priority']) => {
    switch (priority) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const selectedDateExams = selectedDate ? getExamsForDate(selectedDate) : [];

  return (
    <div className="space-y-6">
      {/* Calendar Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-violet-600" />
              Exam Calendar
            </CardTitle>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateMonth('prev')}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              
              <div className="min-w-[140px] text-center">
                <h3 className="text-lg font-semibold">
                  {format(currentDate, 'MMMM yyyy')}
                </h3>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateMonth('next')}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Calendar Grid */}
          <div className="space-y-4">
            {/* Day Headers */}
            <div className="grid grid-cols-7 gap-0">
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                <div key={day} className="p-2 text-center text-sm font-medium text-muted-foreground">
                  {day}
                </div>
              ))}
            </div>
            
            {/* Calendar Days */}
            <div className="grid grid-cols-7 gap-0 border border-border rounded-lg overflow-hidden">
              {calendarData.days.map((date, index) => {
                const dayExams = getExamsForDate(date);
                
                return (
                  <motion.div
                    key={date.toISOString()}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: index * 0.01 }}
                    className={getDayClasses(date, dayExams)}
                    onClick={() => setSelectedDate(date)}
                  >
                    <div className="flex items-start justify-between h-full">
                      <span className={cn(
                        "text-sm font-medium",
                        isToday(date) && "text-violet-600 font-bold"
                      )}>
                        {format(date, 'd')}
                      </span>
                      
                      {dayExams.length > 0 && (
                        <div className="flex flex-col gap-1">
                          {dayExams.slice(0, 2).map((exam, examIndex) => (
                            <motion.div
                              key={exam.id}
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ delay: (index * 0.01) + (examIndex * 0.05) }}
                              className={cn(
                                "w-2 h-2 rounded-full",
                                getPriorityColor(exam.priority)
                              )}
                            />
                          ))}
                          {dayExams.length > 2 && (
                            <span className="text-xs text-muted-foreground">
                              +{dayExams.length - 2}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                    
                    {dayExams.length > 0 && (
                      <div className="absolute bottom-1 left-1 right-1">
                        <div className="text-xs truncate">
                          {dayExams[0].name}
                        </div>
                      </div>
                    )}
                  </motion.div>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Selected Date Details */}
      <AnimatePresence>
        {selectedDate && selectedDateExams.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-violet-600" />
                  Exams on {format(selectedDate, 'EEEE, MMMM d, yyyy')}
                </CardTitle>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-3">
                  {selectedDateExams.map((exam, index) => {
                    const daysUntil = Math.ceil((new Date(exam.date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
                    const completionRate = chapterProgressUtils.calculateOverallProgress(exam.preparationData.chapters) / 100;
                    
                    return (
                      <motion.div
                        key={exam.id}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="p-4 rounded-lg border bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-950/30 dark:to-purple-950/30 cursor-pointer hover:shadow-md transition-all duration-200"
                        onClick={() => onExamClick?.(exam)}
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1">
                            <h4 className="font-semibold text-lg">{exam.name}</h4>
                            <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <Clock className="h-4 w-4" />
                                {exam.time}
                              </div>
                              <div className="flex items-center gap-1">
                                {daysUntil === 0 ? (
                                  <>
                                    <AlertTriangle className="h-4 w-4 text-red-500" />
                                    Today!
                                  </>
                                ) : daysUntil < 0 ? (
                                  <>
                                    <CheckCircle className="h-4 w-4 text-green-500" />
                                    Past
                                  </>
                                ) : (
                                  <>
                                    <Target className="h-4 w-4" />
                                    {daysUntil} days left
                                  </>
                                )}
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Badge 
                              className={cn(
                                "text-white",
                                exam.priority === 'critical' && "bg-red-500",
                                exam.priority === 'high' && "bg-orange-500",
                                exam.priority === 'medium' && "bg-yellow-500",
                                exam.priority === 'low' && "bg-green-500"
                              )}
                            >
                              {exam.priority}
                            </Badge>
                          </div>
                        </div>
                        
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span>Preparation Progress</span>
                            <span>{Math.round(completionRate * 100)}%</span>
                          </div>
                          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <motion.div
                              initial={{ width: 0 }}
                              animate={{ width: `${completionRate * 100}%` }}
                              transition={{ duration: 0.5, delay: index * 0.1 }}
                              className="bg-violet-600 h-2 rounded-full"
                            />
                          </div>
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
