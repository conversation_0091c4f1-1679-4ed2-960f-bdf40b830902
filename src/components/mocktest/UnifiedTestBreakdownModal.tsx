import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  X,
  Calendar,
  Clock,
  Target,
  FileText,
  ExternalLink,
  Edit2,
  Save,
  Plus,
  Trash2,
  CheckCircle2,
  AlertCircle,
  Award,
  TrendingUp,
  BarChart3,
  BookOpen,
  AlertTriangle,
  Lightbulb
} from "lucide-react";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Tabs,
  Ta<PERSON>Content,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@/components/ui/tabs";
import { 
  MockTest, 
  UnifiedMistake, 
  UnifiedTakeaway, 
  UnifiedSyllabus,
  DifficultyLevel,
  ConfidenceLevel
} from "@/types/mockTest";
import { unifiedMockTestStorage } from "@/utils/unifiedTestStorage";
import { testDataValidator } from "@/utils/testDataValidation";
import { testDataSyncService } from "@/utils/testDataSyncService";
import { toast } from "@/components/ui/use-toast";
import { cn } from "@/lib/utils";
import { format } from "date-fns";

interface UnifiedTestBreakdownModalProps {
  test: MockTest | null;
  isOpen: boolean;
  onClose: () => void;
  onTestUpdate?: (updatedTest: MockTest) => void;
}

export function UnifiedTestBreakdownModal({ 
  test, 
  isOpen, 
  onClose, 
  onTestUpdate 
}: UnifiedTestBreakdownModalProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  
  // Form states for editing
  const [editForm, setEditForm] = useState<{
    totalMarksObtained: number;
    totalMarks: number;
    timeSpent: number;
    difficulty: DifficultyLevel;
    targetScore: number;
    isReviewed: boolean;
    actualDifficulty?: 'easier' | 'as_expected' | 'harder';
    timeManagement?: 'excellent' | 'good' | 'average' | 'poor';
    stressLevel?: ConfidenceLevel;
    notes: string;
  }>({
    totalMarksObtained: 0,
    totalMarks: 0,
    timeSpent: 0,
    difficulty: 'medium',
    targetScore: 0,
    isReviewed: false,
    notes: '',
  });

  // Mistake form
  const [mistakeForm, setMistakeForm] = useState<Partial<UnifiedMistake>>({
    category: 'other',
    subject: '',
    topic: '',
    description: '',
    solution: '',
    preventionStrategy: '',
    severity: 'medium',
  });

  // Takeaway form
  const [takeawayForm, setTakeawayForm] = useState<Partial<UnifiedTakeaway>>({
    category: 'other',
    subject: '',
    topic: '',
    description: '',
    actionPlan: '',
    priority: 'medium',
  });

  const [isAddingMistake, setIsAddingMistake] = useState(false);
  const [isAddingTakeaway, setIsAddingTakeaway] = useState(false);

  // Load data when test changes
  useEffect(() => {
    if (test) {
      setEditForm({
        totalMarksObtained: test.totalMarksObtained || 0,
        totalMarks: test.totalMarks || 0,
        timeSpent: test.timeSpent || 0,
        difficulty: test.difficulty || 'medium',
        targetScore: test.targetScore || 0,
        isReviewed: test.isReviewed || false,
        actualDifficulty: test.actualDifficulty,
        timeManagement: test.timeManagement,
        stressLevel: test.stressLevel,
        notes: test.notes || '',
      });
    }
  }, [test]);

  // ============================================================================
  // HANDLERS
  // ============================================================================

  const handleSaveEdit = async () => {
    if (!test) return;

    try {
      // Validate the updated data
      const updatedTest = { ...test, ...editForm };
      const validation = testDataValidator.validateMockTest(updatedTest, {
        mode: 'edit',
        testType: 'completed',
        userId: test.userId
      });

      if (!validation.isValid) {
        toast({
          title: "Validation Error",
          description: validation.errors.join(', '),
          variant: "destructive",
        });
        return;
      }

      // Save the updated test
      const savedTest = unifiedMockTestStorage.save(test.userId, updatedTest);
      onTestUpdate?.(savedTest);
      setIsEditing(false);

      toast({
        title: "Success",
        description: "Test details updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update test",
        variant: "destructive",
      });
    }
  };

  const handleAddMistake = () => {
    if (!test || !mistakeForm.description?.trim()) {
      toast({
        title: "Error",
        description: "Please enter a mistake description",
        variant: "destructive",
      });
      return;
    }

    const newMistake: UnifiedMistake = {
      id: `${test.id}-mistake-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      category: mistakeForm.category || 'other',
      subject: mistakeForm.subject,
      topic: mistakeForm.topic,
      description: mistakeForm.description,
      solution: mistakeForm.solution,
      preventionStrategy: mistakeForm.preventionStrategy,
      severity: mistakeForm.severity || 'medium',
      createdAt: new Date().toISOString(),
      resolved: false,
    };

    const updatedTest = {
      ...test,
      mistakes: [...(test.mistakes || []), newMistake],
      mistakesAnalyzed: true,
      analysisCompleted: true,
    };

    try {
      const savedTest = unifiedMockTestStorage.save(test.userId, updatedTest);
      onTestUpdate?.(savedTest);
      
      // Reset form
      setMistakeForm({
        category: 'other',
        subject: '',
        topic: '',
        description: '',
        solution: '',
        preventionStrategy: '',
        severity: 'medium',
      });
      setIsAddingMistake(false);

      toast({
        title: "Success",
        description: "Mistake added successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add mistake",
        variant: "destructive",
      });
    }
  };

  const handleAddTakeaway = () => {
    if (!test || !takeawayForm.description?.trim()) {
      toast({
        title: "Error",
        description: "Please enter a takeaway description",
        variant: "destructive",
      });
      return;
    }

    const newTakeaway: UnifiedTakeaway = {
      id: `${test.id}-takeaway-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      category: takeawayForm.category || 'other',
      subject: takeawayForm.subject,
      topic: takeawayForm.topic,
      description: takeawayForm.description,
      actionPlan: takeawayForm.actionPlan,
      priority: takeawayForm.priority || 'medium',
      implemented: false,
      createdAt: new Date().toISOString(),
    };

    const updatedTest = {
      ...test,
      takeaways: [...(test.takeaways || []), newTakeaway],
      takeawaysRecorded: true,
      analysisCompleted: true,
    };

    try {
      const savedTest = unifiedMockTestStorage.save(test.userId, updatedTest);
      onTestUpdate?.(savedTest);
      
      // Reset form
      setTakeawayForm({
        category: 'other',
        subject: '',
        topic: '',
        description: '',
        actionPlan: '',
        priority: 'medium',
      });
      setIsAddingTakeaway(false);

      toast({
        title: "Success",
        description: "Takeaway added successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add takeaway",
        variant: "destructive",
      });
    }
  };

  const handleDeleteMistake = (mistakeId: string) => {
    if (!test) return;

    const updatedMistakes = test.mistakes?.filter(m => m.id !== mistakeId) || [];
    const updatedTest = {
      ...test,
      mistakes: updatedMistakes,
      mistakesAnalyzed: updatedMistakes.length > 0,
    };

    try {
      const savedTest = unifiedMockTestStorage.save(test.userId, updatedTest);
      onTestUpdate?.(savedTest);

      toast({
        title: "Success",
        description: "Mistake deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete mistake",
        variant: "destructive",
      });
    }
  };

  const handleDeleteTakeaway = (takeawayId: string) => {
    if (!test) return;

    const updatedTakeaways = test.takeaways?.filter(t => t.id !== takeawayId) || [];
    const updatedTest = {
      ...test,
      takeaways: updatedTakeaways,
      takeawaysRecorded: updatedTakeaways.length > 0,
    };

    try {
      const savedTest = unifiedMockTestStorage.save(test.userId, updatedTest);
      onTestUpdate?.(savedTest);

      toast({
        title: "Success",
        description: "Takeaway deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete takeaway",
        variant: "destructive",
      });
    }
  };

  const toggleMistakeResolved = (mistakeId: string) => {
    if (!test) return;

    const updatedMistakes = test.mistakes?.map(mistake => 
      mistake.id === mistakeId 
        ? { 
            ...mistake, 
            resolved: !mistake.resolved,
            resolvedAt: !mistake.resolved ? new Date().toISOString() : undefined
          }
        : mistake
    ) || [];

    const updatedTest = { ...test, mistakes: updatedMistakes };

    try {
      const savedTest = unifiedMockTestStorage.save(test.userId, updatedTest);
      onTestUpdate?.(savedTest);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update mistake status",
        variant: "destructive",
      });
    }
  };

  const toggleTakeawayImplemented = (takeawayId: string) => {
    if (!test) return;

    const updatedTakeaways = test.takeaways?.map(takeaway => 
      takeaway.id === takeawayId 
        ? { 
            ...takeaway, 
            implemented: !takeaway.implemented,
            implementedAt: !takeaway.implemented ? new Date().toISOString() : undefined
          }
        : takeaway
    ) || [];

    const updatedTest = { ...test, takeaways: updatedTakeaways };

    try {
      const savedTest = unifiedMockTestStorage.save(test.userId, updatedTest);
      onTestUpdate?.(savedTest);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update takeaway status",
        variant: "destructive",
      });
    }
  };

  // ============================================================================
  // RENDER METHODS
  // ============================================================================

  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Test Performance Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Performance Summary
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {isEditing ? (
                  <Input
                    type="number"
                    value={editForm.totalMarksObtained}
                    onChange={(e) => setEditForm(prev => ({ ...prev, totalMarksObtained: parseInt(e.target.value) || 0 }))}
                    className="text-center"
                  />
                ) : (
                  test.totalMarksObtained
                )}
              </div>
              <div className="text-sm text-muted-foreground">Marks Obtained</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                {isEditing ? (
                  <Input
                    type="number"
                    value={editForm.totalMarks}
                    onChange={(e) => setEditForm(prev => ({ ...prev, totalMarks: parseInt(e.target.value) || 0 }))}
                    className="text-center"
                  />
                ) : (
                  test.totalMarks
                )}
              </div>
              <div className="text-sm text-muted-foreground">Total Marks</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {test.totalMarks > 0 ? Math.round((test.totalMarksObtained / test.totalMarks) * 100) : 0}%
              </div>
              <div className="text-sm text-muted-foreground">Percentage</div>
            </div>
          </div>

          <Progress
            value={test.totalMarks > 0 ? (test.totalMarksObtained / test.totalMarks) * 100 : 0}
            className="h-3"
          />
        </CardContent>
      </Card>

      {/* Test Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Test Details
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Date</Label>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                {format(new Date(test.date), 'PPP')}
              </div>
            </div>

            <div className="space-y-2">
              <Label>Time Spent</Label>
              {isEditing ? (
                <Input
                  type="number"
                  value={editForm.timeSpent}
                  onChange={(e) => setEditForm(prev => ({ ...prev, timeSpent: parseInt(e.target.value) || 0 }))}
                  placeholder="Minutes"
                />
              ) : (
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  {test.timeSpent ? `${test.timeSpent} minutes` : 'Not recorded'}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label>Difficulty</Label>
              {isEditing ? (
                <Select
                  value={editForm.difficulty}
                  onValueChange={(value: DifficultyLevel) => setEditForm(prev => ({ ...prev, difficulty: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="easy">Easy</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="hard">Hard</SelectItem>
                    <SelectItem value="very_hard">Very Hard</SelectItem>
                  </SelectContent>
                </Select>
              ) : (
                <Badge variant="outline">
                  {test.difficulty?.charAt(0).toUpperCase() + test.difficulty?.slice(1)}
                </Badge>
              )}
            </div>

            <div className="space-y-2">
              <Label>Target Score</Label>
              {isEditing ? (
                <Input
                  type="number"
                  value={editForm.targetScore}
                  onChange={(e) => setEditForm(prev => ({ ...prev, targetScore: parseInt(e.target.value) || 0 }))}
                />
              ) : (
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  {test.targetScore || 'Not set'}
                </div>
              )}
            </div>
          </div>

          {isEditing && (
            <div className="space-y-2">
              <Label>Notes</Label>
              <Textarea
                value={editForm.notes}
                onChange={(e) => setEditForm(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Add notes about this test..."
                rows={3}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Analysis Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle2 className="h-5 w-5" />
            Analysis Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <div className={cn(
                "w-3 h-3 rounded-full",
                test.isReviewed ? "bg-green-500" : "bg-gray-300"
              )} />
              <span className="text-sm">
                {test.isReviewed ? "Reviewed" : "Not Reviewed"}
              </span>
            </div>

            <div className="flex items-center gap-2">
              <div className={cn(
                "w-3 h-3 rounded-full",
                test.mistakesAnalyzed ? "bg-green-500" : "bg-gray-300"
              )} />
              <span className="text-sm">
                Mistakes: {test.mistakes?.length || 0}
              </span>
            </div>

            <div className="flex items-center gap-2">
              <div className={cn(
                "w-3 h-3 rounded-full",
                test.takeawaysRecorded ? "bg-green-500" : "bg-gray-300"
              )} />
              <span className="text-sm">
                Takeaways: {test.takeaways?.length || 0}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderSyllabusTab = () => (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Syllabus Coverage
          </CardTitle>
        </CardHeader>
        <CardContent>
          {test.syllabus?.topics && test.syllabus.topics.length > 0 ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Overall Progress</span>
                <span className="text-sm text-muted-foreground">
                  {test.syllabus.overallProgress || 0}%
                </span>
              </div>
              <Progress value={test.syllabus.overallProgress || 0} className="h-2" />

              <div className="space-y-2">
                <Label>Topics Covered</Label>
                <div className="flex flex-wrap gap-2">
                  {test.syllabus.topics.map((topic, index) => (
                    <Badge key={index} variant="secondary">
                      {topic}
                    </Badge>
                  ))}
                </div>
              </div>

              {test.syllabus.chapters && test.syllabus.chapters.length > 0 && (
                <div className="space-y-2">
                  <Label>Chapter Details</Label>
                  <div className="space-y-2">
                    {test.syllabus.chapters.map((chapter, index) => (
                      <div key={index} className="flex items-center justify-between p-2 border rounded">
                        <span className="font-medium">{chapter.name}</span>
                        <div className="flex items-center gap-2">
                          <Badge variant={chapter.completed ? "default" : "outline"}>
                            {chapter.completed ? "Completed" : "Pending"}
                          </Badge>
                          <Badge variant="outline">
                            Confidence: {chapter.confidence}/5
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="text-center text-muted-foreground py-8">
              No syllabus data available
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );

  const renderMistakesTab = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Mistakes Analysis</h3>
        <Button onClick={() => setIsAddingMistake(true)} size="sm">
          <Plus className="h-4 w-4 mr-2" />
          Add Mistake
        </Button>
      </div>

      {isAddingMistake && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Mistake</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Category</Label>
                <Select
                  value={mistakeForm.category}
                  onValueChange={(value) => setMistakeForm(prev => ({ ...prev, category: value as any }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="conceptual">Conceptual</SelectItem>
                    <SelectItem value="calculation">Calculation</SelectItem>
                    <SelectItem value="silly">Silly Mistake</SelectItem>
                    <SelectItem value="time_management">Time Management</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Severity</Label>
                <Select
                  value={mistakeForm.severity}
                  onValueChange={(value) => setMistakeForm(prev => ({ ...prev, severity: value as any }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Subject</Label>
                <Input
                  value={mistakeForm.subject || ''}
                  onChange={(e) => setMistakeForm(prev => ({ ...prev, subject: e.target.value }))}
                  placeholder="Subject name"
                />
              </div>

              <div className="space-y-2">
                <Label>Topic</Label>
                <Input
                  value={mistakeForm.topic || ''}
                  onChange={(e) => setMistakeForm(prev => ({ ...prev, topic: e.target.value }))}
                  placeholder="Topic name"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Description *</Label>
              <Textarea
                value={mistakeForm.description || ''}
                onChange={(e) => setMistakeForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe the mistake..."
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label>Solution</Label>
              <Textarea
                value={mistakeForm.solution || ''}
                onChange={(e) => setMistakeForm(prev => ({ ...prev, solution: e.target.value }))}
                placeholder="How to solve this correctly..."
                rows={2}
              />
            </div>

            <div className="space-y-2">
              <Label>Prevention Strategy</Label>
              <Textarea
                value={mistakeForm.preventionStrategy || ''}
                onChange={(e) => setMistakeForm(prev => ({ ...prev, preventionStrategy: e.target.value }))}
                placeholder="How to prevent this mistake in future..."
                rows={2}
              />
            </div>

            <div className="flex gap-2">
              <Button onClick={handleAddMistake}>Add Mistake</Button>
              <Button variant="outline" onClick={() => setIsAddingMistake(false)}>Cancel</Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="space-y-3">
        {test.mistakes && test.mistakes.length > 0 ? (
          test.mistakes.map((mistake) => (
            <Card key={mistake.id}>
              <CardContent className="pt-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant={mistake.severity === 'high' ? 'destructive' : mistake.severity === 'medium' ? 'default' : 'secondary'}>
                        {mistake.severity}
                      </Badge>
                      <Badge variant="outline">{mistake.category}</Badge>
                      {mistake.subject && <Badge variant="outline">{mistake.subject}</Badge>}
                      {mistake.resolved && <Badge variant="default">Resolved</Badge>}
                    </div>

                    <p className="text-sm">{mistake.description}</p>

                    {mistake.solution && (
                      <div className="text-sm">
                        <span className="font-medium">Solution: </span>
                        {mistake.solution}
                      </div>
                    )}

                    {mistake.preventionStrategy && (
                      <div className="text-sm">
                        <span className="font-medium">Prevention: </span>
                        {mistake.preventionStrategy}
                      </div>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toggleMistakeResolved(mistake.id)}
                    >
                      {mistake.resolved ? 'Mark Unresolved' : 'Mark Resolved'}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteMistake(mistake.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="text-center text-muted-foreground py-8">
            No mistakes recorded yet
          </div>
        )}
      </div>
    </div>
  );

  const renderTakeawaysTab = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Key Takeaways</h3>
        <Button onClick={() => setIsAddingTakeaway(true)} size="sm">
          <Plus className="h-4 w-4 mr-2" />
          Add Takeaway
        </Button>
      </div>

      {isAddingTakeaway && (
        <Card>
          <CardHeader>
            <CardTitle>Add New Takeaway</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Category</Label>
                <Select
                  value={takeawayForm.category}
                  onValueChange={(value) => setTakeawayForm(prev => ({ ...prev, category: value as any }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="strength">Strength</SelectItem>
                    <SelectItem value="weakness">Weakness</SelectItem>
                    <SelectItem value="strategy">Strategy</SelectItem>
                    <SelectItem value="concept">Concept</SelectItem>
                    <SelectItem value="time_management">Time Management</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Priority</Label>
                <Select
                  value={takeawayForm.priority}
                  onValueChange={(value) => setTakeawayForm(prev => ({ ...prev, priority: value as any }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Subject</Label>
                <Input
                  value={takeawayForm.subject || ''}
                  onChange={(e) => setTakeawayForm(prev => ({ ...prev, subject: e.target.value }))}
                  placeholder="Subject name"
                />
              </div>

              <div className="space-y-2">
                <Label>Topic</Label>
                <Input
                  value={takeawayForm.topic || ''}
                  onChange={(e) => setTakeawayForm(prev => ({ ...prev, topic: e.target.value }))}
                  placeholder="Topic name"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Description *</Label>
              <Textarea
                value={takeawayForm.description || ''}
                onChange={(e) => setTakeawayForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Describe the takeaway..."
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label>Action Plan</Label>
              <Textarea
                value={takeawayForm.actionPlan || ''}
                onChange={(e) => setTakeawayForm(prev => ({ ...prev, actionPlan: e.target.value }))}
                placeholder="What actions will you take based on this takeaway..."
                rows={2}
              />
            </div>

            <div className="flex gap-2">
              <Button onClick={handleAddTakeaway}>Add Takeaway</Button>
              <Button variant="outline" onClick={() => setIsAddingTakeaway(false)}>Cancel</Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="space-y-3">
        {test.takeaways && test.takeaways.length > 0 ? (
          test.takeaways.map((takeaway) => (
            <Card key={takeaway.id}>
              <CardContent className="pt-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant={takeaway.priority === 'high' ? 'destructive' : takeaway.priority === 'medium' ? 'default' : 'secondary'}>
                        {takeaway.priority}
                      </Badge>
                      <Badge variant="outline">{takeaway.category}</Badge>
                      {takeaway.subject && <Badge variant="outline">{takeaway.subject}</Badge>}
                      {takeaway.implemented && <Badge variant="default">Implemented</Badge>}
                    </div>

                    <p className="text-sm">{takeaway.description}</p>

                    {takeaway.actionPlan && (
                      <div className="text-sm">
                        <span className="font-medium">Action Plan: </span>
                        {takeaway.actionPlan}
                      </div>
                    )}
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toggleTakeawayImplemented(takeaway.id)}
                    >
                      {takeaway.implemented ? 'Mark Pending' : 'Mark Implemented'}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteTakeaway(takeaway.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <div className="text-center text-muted-foreground py-8">
            No takeaways recorded yet
          </div>
        )}
      </div>
    </div>
  );

  if (!test) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>{test.name} - Test Breakdown</span>
            <div className="flex items-center gap-2">
              {!isEditing ? (
                <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
                  <Edit2 className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              ) : (
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={() => setIsEditing(false)}>
                    Cancel
                  </Button>
                  <Button size="sm" onClick={handleSaveEdit}>
                    <Save className="h-4 w-4 mr-2" />
                    Save
                  </Button>
                </div>
              )}
            </div>
          </DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="syllabus">Syllabus</TabsTrigger>
            <TabsTrigger value="mistakes">Mistakes</TabsTrigger>
            <TabsTrigger value="takeaways">Takeaways</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {renderOverviewTab()}
          </TabsContent>

          <TabsContent value="syllabus" className="space-y-4">
            {renderSyllabusTab()}
          </TabsContent>

          <TabsContent value="mistakes" className="space-y-4">
            {renderMistakesTab()}
          </TabsContent>

          <TabsContent value="takeaways" className="space-y-4">
            {renderTakeawaysTab()}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
