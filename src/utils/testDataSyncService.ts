import { 
  MockTest, 
  UpcomingTest, 
  UnifiedTestData, 
  UnifiedSyllabus, 
  UnifiedMistake, 
  UnifiedTakeaway,
  TestDataValidation,
  SyncStatus,
  TestSyncData,
  StandardTestFormData
} from '@/types/mockTest';
// Removed Supabase dependency - using local storage only

/**
 * Centralized service for managing bidirectional synchronization 
 * between local storage and Supabase for mock test data
 */
export class TestDataSyncService {
  private static instance: TestDataSyncService;
  private syncInProgress = new Set<string>();
  private syncCallbacks: ((testId: string, status: SyncStatus) => void)[] = [];

  static getInstance(): TestDataSyncService {
    if (!TestDataSyncService.instance) {
      TestDataSyncService.instance = new TestDataSyncService();
    }
    return TestDataSyncService.instance;
  }

  // ============================================================================
  // DATA VALIDATION
  // ============================================================================

  validateTestData(data: Partial<UnifiedTestData>): TestDataValidation {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields validation
    if (!data.name?.trim()) errors.push('Test name is required');
    if (!data.date) errors.push('Test date is required');
    if (!data.userId) errors.push('User ID is required');

    // Date format validation
    if (data.date && !/^\d{4}-\d{2}-\d{2}$/.test(data.date)) {
      errors.push('Invalid date format (YYYY-MM-DD required)');
    }

    // Time format validation
    if (data.time && !/^\d{2}:\d{2}$/.test(data.time)) {
      errors.push('Invalid time format (HH:MM required)');
    }

    // Syllabus validation
    if (data.syllabus) {
      if (!Array.isArray(data.syllabus.topics)) {
        errors.push('Syllabus topics must be an array');
      } else if (data.syllabus.topics.length === 0) {
        warnings.push('No syllabus topics specified');
      }
    }

    // Mistakes validation
    if (data.mistakes) {
      data.mistakes.forEach((mistake, index) => {
        if (!mistake.description?.trim()) {
          errors.push(`Mistake ${index + 1}: Description is required`);
        }
        if (!mistake.category) {
          errors.push(`Mistake ${index + 1}: Category is required`);
        }
      });
    }

    // Takeaways validation
    if (data.takeaways) {
      data.takeaways.forEach((takeaway, index) => {
        if (!takeaway.description?.trim()) {
          errors.push(`Takeaway ${index + 1}: Description is required`);
        }
        if (!takeaway.category) {
          errors.push(`Takeaway ${index + 1}: Category is required`);
        }
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  // ============================================================================
  // DATA CONVERSION AND MIGRATION
  // ============================================================================

  convertLegacySyllabus(legacySyllabus?: string[]): UnifiedSyllabus {
    return {
      topics: legacySyllabus || [],
      chapters: [],
      overallProgress: 0,
      lastUpdated: new Date().toISOString()
    };
  }

  convertLegacyMistakes(legacyMistakes?: any[]): UnifiedMistake[] {
    if (!legacyMistakes) return [];
    
    return legacyMistakes.map(mistake => ({
      id: mistake.id || `mistake-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      category: mistake.category || 'other',
      subject: mistake.subject,
      topic: mistake.topic,
      description: mistake.description || '',
      solution: mistake.solution,
      preventionStrategy: mistake.preventionStrategy,
      severity: mistake.severity || 'medium',
      createdAt: mistake.createdAt || new Date().toISOString(),
      resolved: mistake.resolved || false,
      resolvedAt: mistake.resolvedAt
    }));
  }

  convertLegacyTakeaways(legacyTakeaways?: any[]): UnifiedTakeaway[] {
    if (!legacyTakeaways) return [];
    
    return legacyTakeaways.map(takeaway => ({
      id: takeaway.id || `takeaway-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      category: takeaway.category || 'other',
      subject: takeaway.subject,
      topic: takeaway.topic,
      description: takeaway.description || '',
      actionPlan: takeaway.actionPlan,
      priority: takeaway.priority || 'medium',
      implemented: takeaway.implemented || false,
      implementedAt: takeaway.implementedAt,
      createdAt: takeaway.createdAt || new Date().toISOString()
    }));
  }

  migrateToUnifiedStructure(legacyTest: any): UnifiedTestData {
    return {
      id: legacyTest.id,
      name: legacyTest.name,
      date: legacyTest.date,
      time: legacyTest.time,
      userId: legacyTest.userId,
      createdAt: legacyTest.createdAt,
      updatedAt: legacyTest.updatedAt || new Date().toISOString(),
      
      // Test configuration
      testType: legacyTest.testType || 'mock',
      testEnvironment: legacyTest.testEnvironment,
      duration: legacyTest.duration,
      totalQuestions: legacyTest.totalQuestions,
      categoryId: legacyTest.categoryId,
      testPaperUrl: legacyTest.testPaperUrl,
      
      // Goals and preparation
      targetScore: legacyTest.targetScore,
      confidenceLevel: legacyTest.confidenceLevel,
      preparationTime: legacyTest.preparationTime,
      studyMaterials: legacyTest.studyMaterials || [],
      expectedDifficulty: legacyTest.expectedDifficulty || legacyTest.difficulty,
      
      // Convert legacy data to unified structure
      syllabus: this.convertLegacySyllabus(legacyTest.syllabus || legacyTest.syllabus_legacy),
      mistakes: this.convertLegacyMistakes(legacyTest.mistakes || legacyTest.mistakes_legacy),
      takeaways: this.convertLegacyTakeaways(legacyTest.takeaways || legacyTest.takeaways_legacy),
      
      // Notes and settings
      notes: legacyTest.notes,
      isNotificationEnabled: legacyTest.isNotificationEnabled,
      autoTransition: legacyTest.autoTransition,
      preparationReminders: legacyTest.preparationReminders,
      reminderIntervals: legacyTest.reminderIntervals
    };
  }

  // ============================================================================
  // FORM DATA STANDARDIZATION
  // ============================================================================

  convertFormDataToUnified(formData: StandardTestFormData, userId: string): UnifiedTestData {
    const testId = `test-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    
    return {
      id: testId,
      name: formData.name,
      date: formData.date.toISOString().split('T')[0],
      time: formData.time,
      userId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      
      testType: formData.testType,
      testEnvironment: formData.testEnvironment,
      duration: formData.duration ? parseInt(formData.duration) : undefined,
      totalQuestions: formData.totalQuestions ? parseInt(formData.totalQuestions) : undefined,
      categoryId: formData.categoryId,
      testPaperUrl: formData.testPaperUrl,
      
      targetScore: formData.targetScore ? parseInt(formData.targetScore) : undefined,
      confidenceLevel: formData.confidenceLevel,
      preparationTime: formData.preparationTime ? parseFloat(formData.preparationTime) : undefined,
      studyMaterials: formData.studyMaterials,
      expectedDifficulty: formData.expectedDifficulty,
      
      syllabus: {
        topics: formData.syllabusTopics,
        chapters: formData.syllabusChapters,
        overallProgress: 0,
        lastUpdated: new Date().toISOString()
      },
      
      mistakes: formData.mistakes,
      takeaways: formData.takeaways,
      
      notes: formData.notes,
      isNotificationEnabled: formData.isNotificationEnabled,
      autoTransition: false,
      preparationReminders: false,
      reminderIntervals: []
    };
  }

  convertUnifiedToFormData(unifiedData: UnifiedTestData): StandardTestFormData {
    return {
      name: unifiedData.name,
      date: new Date(unifiedData.date),
      time: unifiedData.time || '09:00',
      categoryId: unifiedData.categoryId,
      testType: unifiedData.testType || 'mock',
      
      testEnvironment: unifiedData.testEnvironment,
      duration: unifiedData.duration?.toString() || '',
      totalQuestions: unifiedData.totalQuestions?.toString() || '',
      testPaperUrl: unifiedData.testPaperUrl || '',
      
      targetScore: unifiedData.targetScore?.toString() || '',
      confidenceLevel: unifiedData.confidenceLevel || 3,
      preparationTime: unifiedData.preparationTime?.toString() || '',
      studyMaterials: unifiedData.studyMaterials || [],
      expectedDifficulty: unifiedData.expectedDifficulty || 'medium',
      
      subjectInputs: [], // Will be populated separately
      
      syllabusTopics: unifiedData.syllabus?.topics || [],
      syllabusChapters: unifiedData.syllabus?.chapters || [],
      
      mistakes: unifiedData.mistakes || [],
      takeaways: unifiedData.takeaways || [],
      
      enableMistakeTracking: true,
      enableTakeawayCollection: true,
      isNotificationEnabled: unifiedData.isNotificationEnabled || false,
      
      notes: unifiedData.notes || ''
    };
  }

  // ============================================================================
  // SYNCHRONIZATION STATUS MANAGEMENT
  // ============================================================================

  getSyncStatus(testId: string): SyncStatus {
    return {
      syncInProgress: this.syncInProgress.has(testId),
      syncErrors: [],
      localChanges: false,
      remoteChanges: false
    };
  }

  onSyncStatusChange(callback: (testId: string, status: SyncStatus) => void): void {
    this.syncCallbacks.push(callback);
  }

  private notifySyncStatusChange(testId: string, status: SyncStatus): void {
    this.syncCallbacks.forEach(callback => callback(testId, status));
  }

  // ============================================================================
  // LOCAL STORAGE SYNCHRONIZATION (No Supabase)
  // ============================================================================

  async syncTestToLocalStorage(test: MockTest | UpcomingTest): Promise<TestSyncData> {
    const testId = test.id;
    this.syncInProgress.add(testId);

    const syncStatus: SyncStatus = {
      syncInProgress: true,
      syncErrors: [],
      localChanges: false,
      remoteChanges: false
    };

    try {
      // Validate data before sync
      const validation = this.validateTestData(test);
      if (!validation.isValid) {
        syncStatus.syncErrors = validation.errors;
        return { test, syncStatus, validation };
      }

      // Convert to unified structure if needed
      const unifiedTest = this.migrateToUnifiedStructure(test);

      // Save to local storage (this will be handled by the storage utilities)
      syncStatus.lastSynced = new Date().toISOString();

      return { test, syncStatus, validation };

    } catch (error) {
      syncStatus.syncErrors.push(`Sync error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return { test, syncStatus, validation: { isValid: false, errors: syncStatus.syncErrors, warnings: [] } };
    } finally {
      this.syncInProgress.delete(testId);
      syncStatus.syncInProgress = false;
      this.notifySyncStatusChange(testId, syncStatus);
    }
  }

  // ============================================================================
  // TEST TRANSITION MANAGEMENT (Moved to testTransitionService)
  // ============================================================================

  // Test transition methods have been moved to testTransitionService.ts
  // for better separation of concerns and local storage only operations
}

// Export singleton instance
export const testDataSyncService = TestDataSyncService.getInstance();
