import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Target, TrendingUp, Medal, Layers } from "lucide-react";
import { MockTestAnalytics } from "@/types/mockTest";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface MockTestAnalyticsCardsProps {
  analytics: MockTestAnalytics;
}

export function MockTestAnalyticsCards({ analytics }: MockTestAnalyticsCardsProps) {
  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-500";
    if (score >= 75) return "text-green-400";
    if (score >= 60) return "text-yellow-500";
    if (score >= 40) return "text-orange-500";
    return "text-red-500";
  };

  const CardBaseClasses = "overflow-hidden border border-border bg-card hover:bg-muted/50 transition-all duration-300";

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card className={CardBaseClasses}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Total Tests
          </CardTitle>
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-800">
            <Layers className="h-5 w-5 text-gray-600 dark:text-gray-400" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-foreground">{analytics.totalTests}</div>
          <p className="text-xs text-muted-foreground">
            Tests recorded
          </p>
        </CardContent>
      </Card>

      <Card className={CardBaseClasses}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Average Score
          </CardTitle>
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-800">
            <TrendingUp className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
          </div>
        </CardHeader>
        <CardContent>
          <div className={cn("text-2xl font-bold", getScoreColor(analytics.averageScore))}>
            {analytics.averageScore.toFixed(1)}%
          </div>
          <p className="text-xs text-muted-foreground">
            Across all tests
          </p>
        </CardContent>
      </Card>

      <Card className={CardBaseClasses}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Highest Score
          </CardTitle>
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-800">
            <Medal className="h-5 w-5 text-violet-600 dark:text-violet-400" />
          </div>
        </CardHeader>
        <CardContent>
          <div className={cn("text-2xl font-bold", getScoreColor(analytics.highestScore.percentage))}>
            {analytics.highestScore.percentage.toFixed(1)}%
          </div>
          <p className="text-xs text-muted-foreground line-clamp-1">
            in {analytics.highestScore.testName || 'N/A'}
          </p>
        </CardContent>
      </Card>

      <Card className={CardBaseClasses}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-muted-foreground">
            Subjects Tested
          </CardTitle>
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gray-100 dark:bg-gray-800">
            <BookOpen className="h-5 w-5 text-rose-600 dark:text-rose-400" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-foreground">
            {Object.keys(analytics.subjectPerformance).length}
          </div>
          <p className="text-xs text-muted-foreground">
            Total unique subjects
          </p>
        </CardContent>
      </Card>
    </div>
  );
}