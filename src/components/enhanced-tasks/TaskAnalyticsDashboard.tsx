import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { EnhancedTodoItem } from '@/types/todo';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart,
} from 'recharts';
import {
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle2,
  Alert<PERSON>riangle,
  Target,
  Calendar,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Activity,
  Award,
  Zap,
  Filter,
} from 'lucide-react';
import { format, subDays, startOfDay, endOfDay, isWithinInterval } from 'date-fns';
import { cn } from '@/lib/utils';

interface TaskAnalyticsDashboardProps {
  isOpen: boolean;
  onClose: () => void;
}

interface AnalyticsData {
  totalTasks: number;
  completedTasks: number;
  overdueTasks: number;
  completionRate: number;
  averageCompletionTime: number;
  productivityScore: number;
  taskVelocity: number;
  subjectDistribution: Array<{ name: string; value: number; color: string }>;
  priorityDistribution: Array<{ name: string; value: number; color: string }>;
  difficultyDistribution: Array<{ name: string; value: number; color: string }>;
  weeklyProgress: Array<{ date: string; completed: number; created: number }>;
  monthlyTrends: Array<{ month: string; completion: number; velocity: number }>;
}

const COLORS = {
  primary: '#8b5cf6',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
  info: '#3b82f6',
  purple: '#a855f7',
  pink: '#ec4899',
  indigo: '#6366f1',
};

export function TaskAnalyticsDashboard({ isOpen, onClose }: TaskAnalyticsDashboardProps) {
  const { getFilteredTasks, subjects } = useEnhancedTodoStore();
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');
  const [selectedView, setSelectedView] = useState<'overview' | 'trends' | 'subjects' | 'performance'>('overview');

  const allTasks = getFilteredTasks();

  // Filter tasks by time range
  const filteredTasks = useMemo(() => {
    if (timeRange === 'all') return allTasks;

    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90;
    const startDate = startOfDay(subDays(new Date(), days));
    const endDate = endOfDay(new Date());

    return allTasks.filter(task => 
      isWithinInterval(new Date(task.createdAt), { start: startDate, end: endDate })
    );
  }, [allTasks, timeRange]);

  // Calculate analytics data
  const analyticsData = useMemo((): AnalyticsData => {
    const totalTasks = filteredTasks.length;
    const completedTasks = filteredTasks.filter(task => task.completionPercentage === 100).length;
    const overdueTasks = filteredTasks.filter(task => 
      task.dueDate && new Date(task.dueDate) < new Date() && task.completionPercentage < 100
    ).length;

    const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

    // Calculate average completion time (for completed tasks)
    const completedTasksWithTime = filteredTasks.filter(task => 
      task.completionPercentage === 100 && task.actualTimeSpent
    );
    const averageCompletionTime = completedTasksWithTime.length > 0
      ? completedTasksWithTime.reduce((sum, task) => sum + (task.actualTimeSpent || 0), 0) / completedTasksWithTime.length
      : 0;

    // Calculate productivity score (0-100)
    const productivityScore = Math.min(100, Math.round(
      (completionRate * 0.4) + 
      (Math.max(0, 100 - (overdueTasks / Math.max(totalTasks, 1)) * 100) * 0.3) +
      (Math.min(100, (completedTasks / Math.max(1, timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90)) * 10) * 0.3)
    ));

    // Calculate task velocity (tasks completed per day)
    const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : timeRange === '90d' ? 90 : Math.max(1, (Date.now() - Math.min(...filteredTasks.map(t => t.createdAt))) / (1000 * 60 * 60 * 24));
    const taskVelocity = completedTasks / Math.max(1, days);

    // Subject distribution
    const subjectCounts = filteredTasks.reduce((acc, task) => {
      const subjectName = task.subjectId && subjects[task.subjectId] 
        ? subjects[task.subjectId].name 
        : 'No Subject';
      const subjectColor = task.subjectId && subjects[task.subjectId]
        ? subjects[task.subjectId].color
        : '#6b7280';
      
      if (!acc[subjectName]) {
        acc[subjectName] = { count: 0, color: subjectColor };
      }
      acc[subjectName].count++;
      return acc;
    }, {} as Record<string, { count: number; color: string }>);

    const subjectDistribution = Object.entries(subjectCounts).map(([name, data]) => ({
      name,
      value: data.count,
      color: data.color,
    }));

    // Priority distribution
    const priorityDistribution = [
      { 
        name: 'High', 
        value: filteredTasks.filter(t => t.priority === 'high').length,
        color: COLORS.danger 
      },
      { 
        name: 'Medium', 
        value: filteredTasks.filter(t => t.priority === 'medium').length,
        color: COLORS.warning 
      },
      { 
        name: 'Low', 
        value: filteredTasks.filter(t => t.priority === 'low').length,
        color: COLORS.success 
      },
    ].filter(item => item.value > 0);

    // Difficulty distribution
    const difficultyDistribution = [
      { 
        name: 'Hard', 
        value: filteredTasks.filter(t => t.difficultyLevel === 'hard').length,
        color: COLORS.danger 
      },
      { 
        name: 'Medium', 
        value: filteredTasks.filter(t => t.difficultyLevel === 'medium').length,
        color: COLORS.warning 
      },
      { 
        name: 'Easy', 
        value: filteredTasks.filter(t => t.difficultyLevel === 'easy').length,
        color: COLORS.success 
      },
    ].filter(item => item.value > 0);

    // Weekly progress (last 7 days)
    const weeklyProgress = Array.from({ length: 7 }, (_, i) => {
      const date = subDays(new Date(), 6 - i);
      const dayStart = startOfDay(date);
      const dayEnd = endOfDay(date);
      
      const dayTasks = allTasks.filter(task => 
        isWithinInterval(new Date(task.createdAt), { start: dayStart, end: dayEnd })
      );
      
      const completedInDay = allTasks.filter(task => 
        task.completionPercentage === 100 &&
        task.updatedAt &&
        isWithinInterval(new Date(task.updatedAt), { start: dayStart, end: dayEnd })
      );

      return {
        date: format(date, 'MMM dd'),
        completed: completedInDay.length,
        created: dayTasks.length,
      };
    });

    // Monthly trends (placeholder - would need more historical data)
    const monthlyTrends = [
      { month: 'Jan', completion: 85, velocity: 2.3 },
      { month: 'Feb', completion: 78, velocity: 2.1 },
      { month: 'Mar', completion: 92, velocity: 2.8 },
      { month: 'Apr', completion: completionRate, velocity: taskVelocity },
    ];

    return {
      totalTasks,
      completedTasks,
      overdueTasks,
      completionRate,
      averageCompletionTime,
      productivityScore,
      taskVelocity,
      subjectDistribution,
      priorityDistribution,
      difficultyDistribution,
      weeklyProgress,
      monthlyTrends,
    };
  }, [filteredTasks, subjects, timeRange, allTasks]);

  const StatCard = ({ title, value, subtitle, icon: Icon, trend, color = 'primary' }: {
    title: string;
    value: string | number;
    subtitle?: string;
    icon: any;
    trend?: 'up' | 'down' | 'neutral';
    color?: keyof typeof COLORS;
  }) => (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            {subtitle && (
              <p className="text-xs text-muted-foreground mt-1">{subtitle}</p>
            )}
          </div>
          <div className={cn("p-3 rounded-full", `bg-${color}-100`)}>
            <Icon className={cn("h-6 w-6", `text-${color}-600`)} />
          </div>
        </div>
        {trend && (
          <div className="flex items-center mt-4">
            {trend === 'up' ? (
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
            ) : trend === 'down' ? (
              <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
            ) : null}
            <span className={cn(
              "text-sm",
              trend === 'up' ? 'text-green-600' : trend === 'down' ? 'text-red-600' : 'text-gray-600'
            )}>
              {trend === 'up' ? 'Improving' : trend === 'down' ? 'Declining' : 'Stable'}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="Total Tasks"
          value={analyticsData.totalTasks}
          icon={Target}
          color="primary"
        />
        <StatCard
          title="Completion Rate"
          value={`${Math.round(analyticsData.completionRate)}%`}
          subtitle={`${analyticsData.completedTasks} completed`}
          icon={CheckCircle2}
          trend={analyticsData.completionRate > 75 ? 'up' : analyticsData.completionRate > 50 ? 'neutral' : 'down'}
          color="success"
        />
        <StatCard
          title="Productivity Score"
          value={analyticsData.productivityScore}
          subtitle="Based on completion & timing"
          icon={Zap}
          trend={analyticsData.productivityScore > 75 ? 'up' : analyticsData.productivityScore > 50 ? 'neutral' : 'down'}
          color="warning"
        />
        <StatCard
          title="Task Velocity"
          value={analyticsData.taskVelocity.toFixed(1)}
          subtitle="tasks per day"
          icon={Activity}
          color="info"
        />
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Weekly Progress */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Weekly Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <BarChart data={analyticsData.weeklyProgress}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="completed" fill={COLORS.success} name="Completed" />
                <Bar dataKey="created" fill={COLORS.primary} name="Created" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Priority Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChartIcon className="h-5 w-5" />
              Priority Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <PieChart>
                <Pie
                  data={analyticsData.priorityDistribution}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}`}
                >
                  {analyticsData.priorityDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Alerts */}
      {analyticsData.overdueTasks > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="font-medium text-orange-800">
                  {analyticsData.overdueTasks} overdue task{analyticsData.overdueTasks > 1 ? 's' : ''}
                </p>
                <p className="text-sm text-orange-600">
                  Consider reviewing and updating deadlines or priorities
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  const renderTrends = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Monthly Trends</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={analyticsData.monthlyTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip />
              <Line 
                yAxisId="left"
                type="monotone" 
                dataKey="completion" 
                stroke={COLORS.success} 
                strokeWidth={2}
                name="Completion Rate (%)"
              />
              <Line 
                yAxisId="right"
                type="monotone" 
                dataKey="velocity" 
                stroke={COLORS.primary} 
                strokeWidth={2}
                name="Task Velocity"
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );

  const renderSubjects = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Subject Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={analyticsData.subjectDistribution}
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}`}
                >
                  {analyticsData.subjectDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
            
            <div className="space-y-3">
              {analyticsData.subjectDistribution.map((subject, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                  <div className="flex items-center gap-3">
                    <div 
                      className="w-4 h-4 rounded-full" 
                      style={{ backgroundColor: subject.color }}
                    />
                    <span className="font-medium">{subject.name}</span>
                  </div>
                  <Badge variant="secondary">{subject.value} tasks</Badge>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderPerformance = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Difficulty Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <BarChart data={analyticsData.difficultyDistribution}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="value" fill={COLORS.primary} />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Performance Insights</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">Productivity Score</span>
                <span className="text-sm font-medium">{analyticsData.productivityScore}/100</span>
              </div>
              <Progress value={analyticsData.productivityScore} className="h-2" />
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">Completion Rate</span>
                <span className="text-sm font-medium">{Math.round(analyticsData.completionRate)}%</span>
              </div>
              <Progress value={analyticsData.completionRate} className="h-2" />
            </div>

            {analyticsData.averageCompletionTime > 0 && (
              <div className="pt-4 border-t">
                <p className="text-sm text-muted-foreground">Average Completion Time</p>
                <p className="text-lg font-semibold">
                  {Math.floor(analyticsData.averageCompletionTime / 60)}h {analyticsData.averageCompletionTime % 60}m
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Task Analytics Dashboard
            </DialogTitle>
            <div className="flex items-center gap-2">
              <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="all">All time</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </DialogHeader>

        <div className="flex h-[75vh]">
          {/* Sidebar */}
          <div className="w-48 border-r pr-4">
            <div className="space-y-2">
              {[
                { id: 'overview', label: 'Overview', icon: BarChart3 },
                { id: 'trends', label: 'Trends', icon: TrendingUp },
                { id: 'subjects', label: 'Subjects', icon: Target },
                { id: 'performance', label: 'Performance', icon: Award },
              ].map(({ id, label, icon: Icon }) => (
                <Button
                  key={id}
                  variant={selectedView === id ? 'default' : 'ghost'}
                  className="w-full justify-start"
                  onClick={() => setSelectedView(id as any)}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {label}
                </Button>
              ))}
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 pl-4 overflow-y-auto">
            <motion.div
              key={selectedView}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {selectedView === 'overview' && renderOverview()}
              {selectedView === 'trends' && renderTrends()}
              {selectedView === 'subjects' && renderSubjects()}
              {selectedView === 'performance' && renderPerformance()}
            </motion.div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
