import React, { useState, useEffect } from 'react';
import { useSupabaseAuth } from '../../contexts/SupabaseAuthContext';
import { DashboardHeader } from './DashboardHeader';
import { CollapsibleSidebar } from './CollapsibleSidebar';
import { DashboardGrid } from './DashboardGrid';
import { DiscussionSidebar } from './DiscussionSidebar';
import { useDocumentTitle } from '../../hooks/useDocumentTitle';
import { Notification, NavigationItem } from '../../types/dashboard';
import {
  House,
  Brain,
  Users,
  CheckSquare,
  BarChart3,
  BookOpen,
  Settings,
  User,
  Timer,
  Info
} from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '../ui/alert';

interface StudentDashboardProps {
  className?: string;
}

export const StudentDashboard: React.FC<StudentDashboardProps> = ({ className }) => {
  const { user } = useSupabaseAuth();
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(true); // Default to collapsed
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [activeSection, setActiveSection] = useState('dashboard');
  const [isDiscussionOpen, setIsDiscussionOpen] = useState(false);

  useDocumentTitle('Dashboard - IsotopeAI');

  // Mouse edge detection for auto-opening sidebars
  useEffect(() => {
    let sidebarTimeout: NodeJS.Timeout;
    let discussionTimeout: NodeJS.Timeout;

    const handleMouseMove = (e: MouseEvent) => {
      const { clientX, clientY } = e;
      const windowWidth = window.innerWidth;

      // Left edge detection for sidebar (within 10px of left edge)
      if (clientX <= 10 && clientY > 64) { // Avoid header area
        clearTimeout(sidebarTimeout);
        if (isSidebarCollapsed) {
          setIsSidebarCollapsed(false);
        }
      } else if (clientX > 280 && isSidebarCollapsed === false) { // Auto-collapse when mouse moves away
        sidebarTimeout = setTimeout(() => {
          setIsSidebarCollapsed(true);
        }, 2000); // 2 second delay
      }

      // Right edge detection for discussion sidebar (within 10px of right edge)
      if (clientX >= windowWidth - 10 && clientY > 64) { // Avoid header area
        clearTimeout(discussionTimeout);
        if (!isDiscussionOpen) {
          setIsDiscussionOpen(true);
        }
      } else if (clientX < windowWidth - 400 && isDiscussionOpen) { // Auto-close when mouse moves away
        discussionTimeout = setTimeout(() => {
          setIsDiscussionOpen(false);
        }, 2000); // 2 second delay
      }
    };

    document.addEventListener('mousemove', handleMouseMove);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      clearTimeout(sidebarTimeout);
      clearTimeout(discussionTimeout);
    };
  }, [isSidebarCollapsed, isDiscussionOpen]);

  // Mock notifications - will be replaced with real data
  useEffect(() => {
    const mockNotifications: Notification[] = [
      {
        id: '1',
        type: 'exam',
        title: 'Physics Exam Tomorrow',
        message: 'Your Physics exam is scheduled for tomorrow at 10:00 AM',
        timestamp: new Date(),
        isRead: false,
        actionUrl: '/mock-tests'
      },
      {
        id: '2',
        type: 'task',
        title: 'Assignment Due',
        message: 'Chemistry assignment is due in 2 hours',
        timestamp: new Date(Date.now() - 3600000),
        isRead: false,
        actionUrl: '/tasks'
      },
      {
        id: '3',
        type: 'achievement',
        title: 'Study Streak!',
        message: 'Congratulations! You have maintained a 7-day study streak',
        timestamp: new Date(Date.now() - 7200000),
        isRead: true
      }
    ];
    setNotifications(mockNotifications);
  }, []);

  const navigationItems: NavigationItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: House,
      path: '/dashboard',
      isActive: activeSection === 'dashboard'
    },
    {
      id: 'ai',
      label: 'AI Assistant',
      icon: Brain,
      path: '/ai',
      isActive: activeSection === 'ai'
    },
    {
      id: 'productivity',
      label: 'Productivity',
      icon: Timer,
      path: '/productivity',
      isActive: activeSection === 'productivity'
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: BarChart3,
      path: '/analytics',
      isActive: activeSection === 'analytics'
    },
    {
      id: 'groups',
      label: 'Study Groups',
      icon: Users,
      path: '/groups',
      isActive: activeSection === 'groups'
    },
    {
      id: 'tasks',
      label: 'Tasks',
      icon: CheckSquare,
      path: '/tasks',
      badge: 3,
      isActive: activeSection === 'tasks'
    },
    {
      id: 'mock-tests',
      label: 'Mock Tests',
      icon: BookOpen,
      path: '/mock-tests',
      isActive: activeSection === 'mock-tests'
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: User,
      path: '/profile',
      isActive: activeSection === 'profile'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Settings,
      path: '/settings',
      isActive: activeSection === 'settings'
    }
  ];

  const handleSidebarToggle = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  const handleDiscussionToggle = () => {
    setIsDiscussionOpen(!isDiscussionOpen);
  };

  const handleSearch = (query: string) => {
    // TODO: Implement global search functionality
    console.log('Search query:', query);
  };

  const handleThemeToggle = () => {
    // TODO: Implement theme toggle functionality
    console.log('Theme toggle clicked');
  };

  const handleNotificationClick = (notification: Notification) => {
    // Mark notification as read
    setNotifications(prev =>
      prev.map(n =>
        n.id === notification.id ? { ...n, isRead: true } : n
      )
    );

    // Navigate to action URL if provided
    if (notification.actionUrl) {
      window.location.href = notification.actionUrl;
    }
  };

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-2">
            Please sign in to access your dashboard
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            You need to be authenticated to view this page.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 ${className || ''}`}>
          {/* Dashboard Header */}
          <div className="relative z-30">
            <DashboardHeader
              user={user}
              notifications={notifications}
              onSearch={handleSearch}
              onThemeToggle={handleThemeToggle}
              onNotificationClick={handleNotificationClick}
              onDiscussionToggle={handleDiscussionToggle}
              isDiscussionOpen={isDiscussionOpen}
            />
          </div>

          <div className="flex relative z-20">
        {/* Collapsible Sidebar */}
        <CollapsibleSidebar
          isCollapsed={isSidebarCollapsed}
          onToggle={handleSidebarToggle}
          activeSection={activeSection}
          navigationItems={navigationItems}
          onNavigate={setActiveSection}
        />

        {/* Main Content Area */}
        <main
          className={`flex-1 transition-all duration-300 ${isSidebarCollapsed ? 'ml-16' : 'ml-64'
            } pt-16 relative z-10`}
        >
          <div className="p-6">
            <Alert className="mb-6">
              <Info className="h-4 w-4" />
              <AlertTitle>Heads up!</AlertTitle>
              <AlertDescription>
                Currently, all data displayed on the dashboard is mock data. Actual features and real-time data integration will be implemented soon.
              </AlertDescription>
            </Alert>
            <DashboardGrid userId={user.id} />
          </div>
        </main>
      </div>

      {/* Discussion Sidebar */}
      <div className="relative z-40">
        <DiscussionSidebar
          isOpen={isDiscussionOpen}
          onClose={handleDiscussionToggle}
        />
      </div>
    </div>
  );
};

export default StudentDashboard;
