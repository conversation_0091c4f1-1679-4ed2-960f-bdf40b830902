import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Activity,
  Zap,
  Clock,
  Database,
  Wifi,
  AlertTriangle,
  CheckCircle2,
  TrendingUp,
  TrendingDown,
  Monitor,
  Smartphone,
  Cpu,
  HardDrive,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface PerformanceMetrics {
  renderTime: number;
  memoryUsage: number;
  taskCount: number;
  loadTime: number;
  networkLatency: number;
  cacheHitRate: number;
  errorRate: number;
  fps: number;
  bundleSize: number;
}

interface PerformanceMonitorProps {
  isOpen: boolean;
  onClose: () => void;
}

export function PerformanceMonitor({ isOpen, onClose }: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderTime: 0,
    memoryUsage: 0,
    taskCount: 0,
    loadTime: 0,
    networkLatency: 0,
    cacheHitRate: 0,
    errorRate: 0,
    fps: 0,
    bundleSize: 0,
  });

  const [isMonitoring, setIsMonitoring] = useState(false);
  const [performanceScore, setPerformanceScore] = useState(0);

  // Performance monitoring functions
  const measureRenderTime = useCallback(() => {
    const start = performance.now();
    
    // Simulate component render measurement
    requestAnimationFrame(() => {
      const end = performance.now();
      setMetrics(prev => ({ ...prev, renderTime: end - start }));
    });
  }, []);

  const measureMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const usedMB = memory.usedJSHeapSize / 1024 / 1024;
      setMetrics(prev => ({ ...prev, memoryUsage: usedMB }));
    }
  }, []);

  const measureNetworkLatency = useCallback(async () => {
    const start = performance.now();
    try {
      await fetch('/api/ping', { method: 'HEAD' });
      const latency = performance.now() - start;
      setMetrics(prev => ({ ...prev, networkLatency: latency }));
    } catch (error) {
      console.warn('Network latency measurement failed:', error);
    }
  }, []);

  const measureFPS = useCallback(() => {
    let frames = 0;
    let lastTime = performance.now();

    const countFrames = () => {
      frames++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        setMetrics(prev => ({ ...prev, fps: frames }));
        frames = 0;
        lastTime = currentTime;
      }
      
      if (isMonitoring) {
        requestAnimationFrame(countFrames);
      }
    };

    if (isMonitoring) {
      requestAnimationFrame(countFrames);
    }
  }, [isMonitoring]);

  const calculatePerformanceScore = useCallback(() => {
    const {
      renderTime,
      memoryUsage,
      networkLatency,
      cacheHitRate,
      errorRate,
      fps,
    } = metrics;

    // Calculate score based on various metrics (0-100)
    let score = 100;

    // Render time penalty (target: <16ms for 60fps)
    if (renderTime > 16) score -= Math.min(30, (renderTime - 16) * 2);

    // Memory usage penalty (target: <50MB)
    if (memoryUsage > 50) score -= Math.min(20, (memoryUsage - 50) * 0.5);

    // Network latency penalty (target: <100ms)
    if (networkLatency > 100) score -= Math.min(20, (networkLatency - 100) * 0.1);

    // FPS penalty (target: 60fps)
    if (fps < 60) score -= Math.min(15, (60 - fps) * 0.5);

    // Cache hit rate bonus
    score += cacheHitRate * 0.1;

    // Error rate penalty
    score -= errorRate * 10;

    setPerformanceScore(Math.max(0, Math.round(score)));
  }, [metrics]);

  // Start/stop monitoring
  const toggleMonitoring = useCallback(() => {
    setIsMonitoring(prev => !prev);
  }, []);

  // Run performance tests
  const runPerformanceTest = useCallback(async () => {
    setIsMonitoring(true);
    
    // Measure various metrics
    measureRenderTime();
    measureMemoryUsage();
    await measureNetworkLatency();
    
    // Simulate task count from store
    const taskCount = document.querySelectorAll('[data-task-id]').length;
    setMetrics(prev => ({ ...prev, taskCount }));

    // Measure load time from navigation timing
    if (performance.navigation) {
      const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
      setMetrics(prev => ({ ...prev, loadTime: loadTime / 1000 }));
    }

    // Simulate cache hit rate (would be measured from actual cache)
    setMetrics(prev => ({ ...prev, cacheHitRate: Math.random() * 100 }));

    // Simulate error rate (would be measured from error tracking)
    setMetrics(prev => ({ ...prev, errorRate: Math.random() * 5 }));

    setTimeout(() => setIsMonitoring(false), 5000);
  }, [measureRenderTime, measureMemoryUsage, measureNetworkLatency]);

  // Effects
  useEffect(() => {
    if (isMonitoring) {
      measureFPS();
    }
  }, [isMonitoring, measureFPS]);

  useEffect(() => {
    calculatePerformanceScore();
  }, [metrics, calculatePerformanceScore]);

  // Auto-run test when dialog opens
  useEffect(() => {
    if (isOpen) {
      runPerformanceTest();
    }
  }, [isOpen, runPerformanceTest]);

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadge = (score: number) => {
    if (score >= 90) return { label: 'Excellent', variant: 'default' as const };
    if (score >= 70) return { label: 'Good', variant: 'secondary' as const };
    return { label: 'Needs Improvement', variant: 'destructive' as const };
  };

  const MetricCard = ({ 
    title, 
    value, 
    unit, 
    icon: Icon, 
    target, 
    status 
  }: {
    title: string;
    value: number;
    unit: string;
    icon: any;
    target?: number;
    status?: 'good' | 'warning' | 'error';
  }) => (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">
              {value.toFixed(1)}{unit}
            </p>
            {target && (
              <p className="text-xs text-muted-foreground">
                Target: {target}{unit}
              </p>
            )}
          </div>
          <div className={cn(
            "p-2 rounded-full",
            status === 'good' && "bg-green-100 text-green-600",
            status === 'warning' && "bg-yellow-100 text-yellow-600",
            status === 'error' && "bg-red-100 text-red-600",
            !status && "bg-gray-100 text-gray-600"
          )}>
            <Icon className="h-4 w-4" />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Performance Monitor
            </DialogTitle>
            <div className="flex items-center gap-2">
              <Badge {...getScoreBadge(performanceScore)}>
                {getScoreBadge(performanceScore).label}
              </Badge>
              <span className={cn("text-2xl font-bold", getScoreColor(performanceScore))}>
                {performanceScore}
              </span>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Performance Score */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Overall Performance Score
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span>Performance Score</span>
                  <span className={cn("font-bold", getScoreColor(performanceScore))}>
                    {performanceScore}/100
                  </span>
                </div>
                <Progress value={performanceScore} className="h-3" />
                <p className="text-sm text-muted-foreground">
                  Based on render time, memory usage, network latency, and FPS
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <MetricCard
              title="Render Time"
              value={metrics.renderTime}
              unit="ms"
              icon={Clock}
              target={16}
              status={metrics.renderTime <= 16 ? 'good' : metrics.renderTime <= 33 ? 'warning' : 'error'}
            />
            
            <MetricCard
              title="Memory Usage"
              value={metrics.memoryUsage}
              unit="MB"
              icon={HardDrive}
              target={50}
              status={metrics.memoryUsage <= 50 ? 'good' : metrics.memoryUsage <= 100 ? 'warning' : 'error'}
            />
            
            <MetricCard
              title="Network Latency"
              value={metrics.networkLatency}
              unit="ms"
              icon={Wifi}
              target={100}
              status={metrics.networkLatency <= 100 ? 'good' : metrics.networkLatency <= 300 ? 'warning' : 'error'}
            />
            
            <MetricCard
              title="Frame Rate"
              value={metrics.fps}
              unit="fps"
              icon={Monitor}
              target={60}
              status={metrics.fps >= 60 ? 'good' : metrics.fps >= 30 ? 'warning' : 'error'}
            />
          </div>

          {/* Additional Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <MetricCard
              title="Task Count"
              value={metrics.taskCount}
              unit=""
              icon={Database}
            />
            
            <MetricCard
              title="Cache Hit Rate"
              value={metrics.cacheHitRate}
              unit="%"
              icon={Zap}
              status={metrics.cacheHitRate >= 80 ? 'good' : metrics.cacheHitRate >= 60 ? 'warning' : 'error'}
            />
            
            <MetricCard
              title="Error Rate"
              value={metrics.errorRate}
              unit="%"
              icon={AlertTriangle}
              status={metrics.errorRate <= 1 ? 'good' : metrics.errorRate <= 5 ? 'warning' : 'error'}
            />
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                onClick={runPerformanceTest}
                disabled={isMonitoring}
                className="flex items-center gap-2"
              >
                {isMonitoring ? (
                  <>
                    <Activity className="h-4 w-4 animate-spin" />
                    Running Test...
                  </>
                ) : (
                  <>
                    <Activity className="h-4 w-4" />
                    Run Performance Test
                  </>
                )}
              </Button>
              
              <Button
                variant="outline"
                onClick={toggleMonitoring}
                className="flex items-center gap-2"
              >
                <Monitor className="h-4 w-4" />
                {isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}
              </Button>
            </div>

            <div className="text-sm text-muted-foreground">
              Last updated: {new Date().toLocaleTimeString()}
            </div>
          </div>

          {/* Performance Tips */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Performance Tips</CardTitle>
            </CardHeader>
            <CardContent className="text-sm space-y-2">
              <div className="flex items-start gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5" />
                <span>Use React.memo() for expensive components</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5" />
                <span>Implement virtual scrolling for large task lists</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5" />
                <span>Use debounced search and filtering</span>
              </div>
              <div className="flex items-start gap-2">
                <CheckCircle2 className="h-4 w-4 text-green-500 mt-0.5" />
                <span>Optimize images and use lazy loading</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}
