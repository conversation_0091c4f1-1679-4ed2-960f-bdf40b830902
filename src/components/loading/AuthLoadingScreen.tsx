import React from 'react';
import { motion } from 'framer-motion';
import { LoadingSpinner } from '../ui/loading-spinner';
import { cn } from '@/lib/utils';

interface AuthLoadingScreenProps {
  message?: string;
  variant?: 'login' | 'signup' | 'callback' | 'logout';
  className?: string;
}

const getAuthMessage = (variant: string) => {
  switch (variant) {
    case 'login':
      return 'Signing you in...';
    case 'signup':
      return 'Creating your account...';
    case 'callback':
      return 'Completing authentication...';
    case 'logout':
      return 'Signing you out...';
    default:
      return 'Authenticating...';
  }
};

const getAuthIcon = (variant: string) => {
  switch (variant) {
    case 'login':
      return (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"
        />
      );
    case 'signup':
      return (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"
        />
      );
    case 'logout':
      return (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
        />
      );
    default:
      return (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
        />
      );
  }
};

export const AuthLoadingScreen: React.FC<AuthLoadingScreenProps> = ({
  message,
  variant = 'login',
  className,
}) => {
  const displayMessage = message || getAuthMessage(variant);

  return (
    <motion.div
      className={cn(
        'flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-background via-background to-primary/5',
        className
      )}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5" />
      
      {/* Security Shield Animation */}
      <motion.div
        className="relative mb-8"
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        {/* Outer Ring */}
        <motion.div
          className="absolute inset-0 w-24 h-24 border-2 border-primary/20 rounded-full"
          animate={{ rotate: 360 }}
          transition={{ duration: 8, repeat: Infinity, ease: 'linear' }}
        />
        
        {/* Middle Ring */}
        <motion.div
          className="absolute inset-2 w-20 h-20 border-2 border-primary/40 rounded-full border-dashed"
          animate={{ rotate: -360 }}
          transition={{ duration: 6, repeat: Infinity, ease: 'linear' }}
        />
        
        {/* Inner Shield */}
        <motion.div
          className="w-16 h-16 bg-gradient-to-br from-primary to-primary/80 rounded-2xl flex items-center justify-center shadow-lg"
          animate={{
            scale: [1, 1.05, 1],
            boxShadow: [
              '0 4px 20px rgba(var(--primary), 0.3)',
              '0 8px 30px rgba(var(--primary), 0.4)',
              '0 4px 20px rgba(var(--primary), 0.3)',
            ],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        >
          <svg
            className="w-8 h-8 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            {getAuthIcon(variant)}
          </svg>
        </motion.div>
      </motion.div>

      {/* Brand */}
      <motion.div
        className="flex items-center space-x-3 mb-8"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        <motion.div
          className="w-10 h-10 bg-gradient-to-br from-primary to-primary/80 rounded-xl flex items-center justify-center"
          animate={{ rotate: [0, 360] }}
          transition={{ duration: 10, repeat: Infinity, ease: 'linear' }}
        >
          <span className="text-white font-bold text-lg">I</span>
        </motion.div>
        <span className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
          IsotopeAI
        </span>
      </motion.div>

      {/* Loading Content */}
      <motion.div
        className="text-center space-y-6"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <LoadingSpinner variant="orbit" size="lg" color="primary" />
        
        <div className="space-y-2">
          <h2 className="text-lg font-semibold text-foreground">
            {displayMessage}
          </h2>
          <p className="text-sm text-muted-foreground max-w-sm">
            Please wait while we securely process your request
          </p>
        </div>
      </motion.div>

      {/* Security Indicators */}
      <motion.div
        className="flex items-center space-x-6 mt-8"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.4 }}
      >
        {['Encrypted', 'Secure', 'Protected'].map((label, index) => (
          <motion.div
            key={label}
            className="flex items-center space-x-2"
            animate={{
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: index * 0.3,
            }}
          >
            <div className="w-2 h-2 bg-green-500 rounded-full" />
            <span className="text-xs text-muted-foreground">{label}</span>
          </motion.div>
        ))}
      </motion.div>

      {/* Footer */}
      <motion.div
        className="absolute bottom-8 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.6 }}
      >
        <p className="text-xs text-muted-foreground">
          Powered by secure authentication
        </p>
      </motion.div>
    </motion.div>
  );
};