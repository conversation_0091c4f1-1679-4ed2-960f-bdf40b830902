import React, { useState, useEffect, useRef, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Send, MessageCircle, Search, Filter } from 'lucide-react';
import { Button } from '../ui/button';
import { Textarea } from '../ui/textarea';
import { Input } from '../ui/input';
import { useToast } from '../ui/use-toast';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { supabase } from '@/integrations/supabase/client';
import { CommentComponent } from '../Comment';

// Types for discussion data
export interface DiscussionComment {
  id: string;
  chat_id: string;
  author_id: string;
  author_name: string | null;
  author_username: string | null;
  author_photo_url: string | null;
  content: string;
  parent_id: string | null;
  created_at: string;
  updated_at: string;
  replies?: DiscussionComment[];
}

interface DiscussionSidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

// Supabase functions for discussion operations
const addDiscussionComment = async (
  chatId: string, 
  content: string, 
  authorId: string, 
  authorName: string, 
  authorUsername: string, 
  authorPhotoUrl?: string, 
  parentId?: string
): Promise<DiscussionComment> => {
  const { data, error } = await (supabase as any)
    .from('discussions')
    .insert({
      chat_id: chatId,
      content: content,
      author_id: authorId,
      author_name: authorName,
      author_username: authorUsername,
      author_photo_url: authorPhotoUrl || null,
      parent_id: parentId || null
    })
    .select()
    .single();

  if (error) {
    console.error("Error adding discussion comment:", error);
    throw error;
  }

  return {
    ...data,
    replies: []
  };
};

const fetchCommentsWithReplies = async (chatId: string): Promise<DiscussionComment[]> => {
  try {
    const { data: allComments, error } = await (supabase as any)
      .from('discussions')
      .select('*')
      .eq('chat_id', chatId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error("Error fetching comments:", error);
      return [];
    }

    if (!allComments || allComments.length === 0) {
      return [];
    }

    // Organize comments with replies
    const commentsMap = new Map<string, DiscussionComment>();
    const rootComments: DiscussionComment[] = [];

    // First pass: create all comment objects
    allComments.forEach((comment: any) => {
      const commentObj: DiscussionComment = {
        ...comment,
        replies: []
      };
      commentsMap.set(comment.id, commentObj);
    });

    // Second pass: organize into tree structure
    allComments.forEach((comment: any) => {
      const commentObj = commentsMap.get(comment.id)!;
      
      if (comment.parent_id && commentsMap.has(comment.parent_id)) {
        const parent = commentsMap.get(comment.parent_id)!;
        parent.replies!.push(commentObj);
      } else {
        rootComments.push(commentObj);
      }
    });

    return rootComments;
  } catch (error) {
    console.error('Error in fetchCommentsWithReplies:', error);
    return [];
  }
};

const deleteDiscussionComment = async (commentId: string): Promise<void> => {
  const { error } = await (supabase as any)
    .from('discussions')
    .delete()
    .eq('id', commentId);

  if (error) {
    console.error("Error deleting discussion comment:", error);
    throw error;
  }
};

const updateDiscussionComment = async (commentId: string, content: string): Promise<void> => {
  const { error } = await (supabase as any)
    .from('discussions')
    .update({
      content: content,
      updated_at: new Date().toISOString()
    })
    .eq('id', commentId);

  if (error) {
    console.error("Error updating discussion comment:", error);
    throw error;
  }
};

const subscribeToComments = (chatId: string, callback: (comments: DiscussionComment[]) => void) => {
  const subscription = supabase
    .channel(`discussions:${chatId}`)
    .on(
      "postgres_changes",
      { event: "*", schema: "public", table: "discussions", filter: `chat_id=eq.${chatId}` },
      async (payload) => {
        const comments = await fetchCommentsWithReplies(chatId);
        callback(comments);
      }
    )
    .subscribe();

  return () => {
    subscription.unsubscribe();
  };
};

export const DiscussionSidebar: React.FC<DiscussionSidebarProps> = ({ isOpen, onClose }) => {
  const { user } = useSupabaseAuth();
  const [comments, setComments] = useState<DiscussionComment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [username, setUsername] = useState<string>("");
  const [userPhotoUrl, setUserPhotoUrl] = useState<string>("");
  const [expandedReplyIds, setExpandedReplyIds] = useState<Set<string>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'newest' | 'oldest'>('newest');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const sidebarRef = useRef<HTMLDivElement>(null);

  // Fixed chatId for global discussions - same as DiscussionSection
  const chatId = 'global-chat';

  // Filter and sort comments based on search query and sort preference
  const filteredComments = useMemo(() => {
    let filtered = comments;

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = comments.filter(comment =>
        comment.content.toLowerCase().includes(query) ||
        comment.author_username?.toLowerCase().includes(query) ||
        comment.author_name?.toLowerCase().includes(query) ||
        comment.replies?.some(reply =>
          reply.content.toLowerCase().includes(query) ||
          reply.author_username?.toLowerCase().includes(query) ||
          reply.author_name?.toLowerCase().includes(query)
        )
      );
    }

    // Sort comments
    filtered.sort((a, b) => {
      const dateA = new Date(a.created_at).getTime();
      const dateB = new Date(b.created_at).getTime();
      return sortBy === 'newest' ? dateB - dateA : dateA - dateB;
    });

    return filtered;
  }, [comments, searchQuery, sortBy]);

  // Fetch user profile
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!user) return;
      try {
        const { data, error } = await supabase
          .from('users')
          .select('username, photo_url')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error fetching user profile:', error);
          return;
        }

        setUsername(data?.username || "");
        setUserPhotoUrl(data?.photo_url || "");
      } catch (error) {
        console.error('Error fetching user profile:', error);
      }
    };

    fetchUserProfile();
  }, [user]);

  // Fetch comments for the global chat - same logic as DiscussionSection
  useEffect(() => {
    if (!chatId || !isOpen) return;

    console.log('Loading comments for chatId:', chatId);

    const loadComments = async () => {
      setIsLoading(true);
      try {
        const initialComments = await fetchCommentsWithReplies(chatId);
        console.log('Initial comments loaded:', initialComments);
        setComments(initialComments);
      } catch (error) {
        console.error('Error loading comments:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadComments();

    // Set up real-time subscription
    const unsubscribe = subscribeToComments(chatId, (updatedComments) => {
      console.log('Real-time comments update:', updatedComments);
      setComments(updatedComments);
    });

    return () => {
      try {
        unsubscribe();
      } catch (error) {
        console.error("Error unsubscribing from comments:", error);
      }
    };
  }, [chatId, isOpen]);

  // Handle click outside to close and keyboard shortcuts
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (sidebarRef.current && !sidebarRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
      // Ctrl/Cmd + Enter to submit comment
      if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
        if (newComment.trim() && chatId) {
          handleAddComment();
        }
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, onClose, newComment, chatId]);

  const handleAddComment = async () => {
    if (!newComment.trim() || !chatId || !user || !username || isSubmitting) return;

    setIsSubmitting(true);
    try {
      await addDiscussionComment(
        chatId,
        newComment.trim(),
        user.id,
        username,
        username,
        userPhotoUrl
      );

      setNewComment('');
      toast({
        title: "Success",
        description: "Comment added successfully!",
      });

      // Manually refresh comments to ensure UI updates
      const updatedComments = await fetchCommentsWithReplies(chatId);
      setComments(updatedComments);
    } catch (error: any) {
      console.error('Error adding comment:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to add comment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAddReply = async (parentId: string, content: string) => {
    if (!chatId || !user || !username) return;

    try {
      await addDiscussionComment(
        chatId,
        content,
        user.id,
        username,
        username,
        userPhotoUrl,
        parentId
      );

      toast({
        title: "Success",
        description: "Reply added successfully!",
      });
    } catch (error: any) {
      console.error('Error adding reply:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to add reply. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleExpandReply = (commentId: string) => {
    setExpandedReplyIds(prev => new Set([...prev, commentId]));
  };

  const handleCollapseReply = (commentId: string) => {
    setExpandedReplyIds(prev => {
      const newSet = new Set(prev);
      newSet.delete(commentId);
      return newSet;
    });
  };

  const handleDeleteComment = async (commentId: string) => {
    try {
      await deleteDiscussionComment(commentId);
      toast({
        title: "Success",
        description: "Comment deleted successfully!",
      });
    } catch (error: any) {
      console.error('Error deleting comment:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete comment. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleEditComment = async (commentId: string, content: string) => {
    try {
      await updateDiscussionComment(commentId, content);
      toast({
        title: "Success",
        description: "Comment updated successfully!",
      });
    } catch (error: any) {
      console.error('Error updating comment:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update comment. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteReply = async (commentId: string, replyId: string) => {
    try {
      await deleteDiscussionComment(replyId);
      toast({
        title: "Success",
        description: "Reply deleted successfully!",
      });
    } catch (error: any) {
      console.error('Error deleting reply:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete reply. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleEditReply = async (commentId: string, replyId: string, content: string) => {
    try {
      await updateDiscussionComment(replyId, content);
      toast({
        title: "Success",
        description: "Reply updated successfully!",
      });
    } catch (error: any) {
      console.error('Error updating reply:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update reply. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Convert DiscussionComment to Comment type for CommentComponent
  const convertToComment = (discussionComment: DiscussionComment) => ({
    id: discussionComment.id,
    author: discussionComment.author_name || discussionComment.author_username || 'Anonymous',
    authorUsername: discussionComment.author_username || 'Anonymous',
    authorPhotoURL: discussionComment.author_photo_url || '',
    content: discussionComment.content,
    timestamp: discussionComment.created_at,
    replies: discussionComment.replies?.map(convertToComment) || []
  });

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
            onClick={onClose}
          />
          
          {/* Sidebar */}
          <motion.div
            ref={sidebarRef}
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{
              type: 'spring',
              damping: 25,
              stiffness: 200,
              mass: 0.8
            }}
            className="fixed right-0 top-0 h-full w-full sm:w-96 lg:w-[28rem] bg-white dark:bg-[#030303] border-l border-gray-200 dark:border-gray-800 z-50 flex flex-col font-onest shadow-2xl"
            drag="x"
            dragConstraints={{ left: 0, right: 0 }}
            dragElastic={0.2}
            onDragEnd={(event, info) => {
              if (info.offset.x > 100) {
                onClose();
              }
            }}
          >
            {/* Mobile Drag Indicator */}
            <div className="sm:hidden flex justify-center py-2 border-b border-gray-200 dark:border-gray-800">
              <div className="w-12 h-1 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
            </div>

            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-800">
              <div className="flex items-center gap-3">
                <motion.div
                  className="p-2 rounded-lg bg-violet-500/10"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <MessageCircle className="h-5 w-5 text-violet-500 dark:text-violet-400" />
                </motion.div>
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Discussions</h2>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Chat with the community</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Search and Filter */}
            {chatId && (
              <div className="p-4 border-b border-gray-200 dark:border-gray-800 space-y-3">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500 dark:text-gray-400" />
                  <Input
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search discussions..."
                    className="pl-10 bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-violet-500 focus:border-violet-500"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">Sort by:</span>
                  </div>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as 'newest' | 'oldest')}
                    className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white text-sm rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-violet-500"
                  >
                    <option value="newest">Newest first</option>
                    <option value="oldest">Oldest first</option>
                  </select>
                </div>
                {searchQuery && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {filteredComments.length} result{filteredComments.length !== 1 ? 's' : ''} found
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSearchQuery('')}
                      className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white h-6 px-2"
                    >
                      Clear
                    </Button>
                  </div>
                )}
              </div>
            )}

            {/* Comments Section */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-violet-500"></div>
                </div>
              ) : filteredComments.length === 0 ? (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <MessageCircle className="h-12 w-12 mx-auto mb-3 opacity-50" />
                  {searchQuery ? (
                    <>
                      <p>No discussions found</p>
                      <p className="text-sm">Try adjusting your search terms</p>
                    </>
                  ) : (
                    <>
                      <p>No discussions yet</p>
                      <p className="text-sm">Be the first to start a conversation!</p>
                      <p className="text-xs text-gray-500 mt-2">
                        Debug: ChatId = {chatId}, Comments count = {comments.length}
                      </p>
                    </>
                  )}
                </div>
              ) : (
                filteredComments.map((comment) => (
                  <CommentComponent
                    key={comment.id}
                    comment={convertToComment(comment)}
                    onReply={handleAddReply}
                    expandedReplyIds={expandedReplyIds}
                    onExpandReply={handleExpandReply}
                    onCollapseReply={handleCollapseReply}
                    isDeveloperMode={username === "Developer"}
                    handleDeleteComment={handleDeleteComment}
                    handleEditComment={handleEditComment}
                    handleDeleteReply={handleDeleteReply}
                    handleEditReply={handleEditReply}
                  />
                ))
              )}
            </div>

            {/* Add Comment Section */}
            {chatId && (
              <div className="p-4 border-t border-gray-200 dark:border-gray-800 bg-white/50 dark:bg-gray-900/50">
                <div className="space-y-3">
                  <div className="relative">
                    <Textarea
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                      placeholder="Share your thoughts..."
                      className="bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-violet-500 focus:border-violet-500 resize-none pr-16 sm:pr-20 text-sm sm:text-base"
                      rows={3}
                      maxLength={500}
                      onKeyDown={(e) => {
                        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                          e.preventDefault();
                          handleAddComment();
                        }
                      }}
                    />
                    <div className="absolute bottom-2 right-2 text-xs text-gray-500 dark:text-gray-400">
                      {newComment.length}/500
                    </div>
                  </div>
                  <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
                    <span className="text-xs text-gray-600 dark:text-gray-400 order-2 sm:order-1">
                      <span className="hidden sm:inline">Press Ctrl+Enter to submit</span>
                      <span className="sm:hidden">Tap to submit</span>
                    </span>
                    <Button
                      onClick={handleAddComment}
                      disabled={!newComment.trim() || !username || isSubmitting}
                      className="bg-violet-600 hover:bg-violet-700 text-white disabled:opacity-50 w-full sm:w-auto order-1 sm:order-2 touch-manipulation"
                      size="sm"
                    >
                      {isSubmitting ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      ) : (
                        <Send className="h-4 w-4 mr-2" />
                      )}
                      {isSubmitting ? 'Posting...' : 'Post Comment'}
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};
