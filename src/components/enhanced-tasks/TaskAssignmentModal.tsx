import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Check, ChevronsUpDown, Users, X, UserPlus } from 'lucide-react';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { cn } from '@/lib/utils';

interface TaskAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  taskIds: string[];
}

interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

// Mock users for demonstration - in a real app, this would come from your user management system
const mockUsers: User[] = [
  {
    id: 'user-1',
    name: 'John Doe',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face',
  },
  {
    id: 'user-2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face',
  },
  {
    id: 'user-3',
    name: 'Mike Johnson',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face',
  },
  {
    id: 'user-4',
    name: 'Sarah Wilson',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop&crop=face',
  },
];

export function TaskAssignmentModal({ isOpen, onClose, taskIds }: TaskAssignmentModalProps) {
  const { user } = useSupabaseAuth();
  const { board, updateTask, bulkOperation } = useEnhancedTodoStore();
  
  const [selectedUsers, setSelectedUsers] = useState<User[]>([]);
  const [isUserSelectorOpen, setIsUserSelectorOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isAssigning, setIsAssigning] = useState(false);

  // Get current assignments for the selected tasks
  useEffect(() => {
    if (isOpen && taskIds.length > 0) {
      const tasks = taskIds.map(id => board.tasks[id]).filter(Boolean);
      const assignedUserIds = new Set(tasks.map(task => task.assignedTo).filter(Boolean));
      
      // Find users that are assigned to all selected tasks
      const commonAssignees = mockUsers.filter(user => assignedUserIds.has(user.id));
      setSelectedUsers(commonAssignees);
    }
  }, [isOpen, taskIds, board.tasks]);

  const filteredUsers = mockUsers.filter(user =>
    user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleUserSelect = (user: User) => {
    setSelectedUsers(prev => {
      const isSelected = prev.some(u => u.id === user.id);
      if (isSelected) {
        return prev.filter(u => u.id !== user.id);
      } else {
        return [...prev, user];
      }
    });
  };

  const handleRemoveUser = (userId: string) => {
    setSelectedUsers(prev => prev.filter(u => u.id !== userId));
  };

  const handleAssignTasks = async () => {
    if (taskIds.length === 0) return;

    setIsAssigning(true);
    try {
      if (taskIds.length === 1) {
        // Single task assignment
        const assignedUser = selectedUsers[0];
        await updateTask(taskIds[0], {
          assignedTo: assignedUser?.id || null,
          assignedToName: assignedUser?.name || null,
          assignedToPhotoURL: assignedUser?.avatar || null,
        });
      } else {
        // Bulk assignment
        const assignedUser = selectedUsers[0];
        await bulkOperation({
          type: 'assign',
          taskIds,
          value: {
            assignedTo: assignedUser?.id || null,
            assignedToName: assignedUser?.name || null,
            assignedToPhotoURL: assignedUser?.avatar || null,
          },
        });
      }

      onClose();
    } catch (error) {
      console.error('Failed to assign tasks:', error);
    } finally {
      setIsAssigning(false);
    }
  };

  const handleUnassignTasks = async () => {
    setIsAssigning(true);
    try {
      if (taskIds.length === 1) {
        await updateTask(taskIds[0], {
          assignedTo: null,
          assignedToName: null,
          assignedToPhotoURL: null,
        });
      } else {
        await bulkOperation({
          type: 'assign',
          taskIds,
          value: {
            assignedTo: null,
            assignedToName: null,
            assignedToPhotoURL: null,
          },
        });
      }

      setSelectedUsers([]);
      onClose();
    } catch (error) {
      console.error('Failed to unassign tasks:', error);
    } finally {
      setIsAssigning(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md bg-white/95 dark:bg-[#030303]/95 backdrop-blur-md border-gray-200 dark:border-gray-800">
        <DialogHeader>
          <DialogTitle className="font-onest text-xl text-gray-900 dark:text-white flex items-center gap-2">
            <Users className="h-5 w-5 text-violet-400" />
            Assign Tasks
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Task Count */}
          <div className="text-sm text-gray-600 dark:text-gray-400">
            Assigning {taskIds.length} task{taskIds.length !== 1 ? 's' : ''}
          </div>

          {/* Selected Users */}
          {selectedUsers.length > 0 && (
            <div className="space-y-2">
              <Label>Assigned to:</Label>
              <div className="flex flex-wrap gap-2">
                {selectedUsers.map((user) => (
                  <Badge
                    key={user.id}
                    variant="secondary"
                    className="flex items-center gap-2 px-3 py-1"
                  >
                    <Avatar className="w-4 h-4">
                      <AvatarImage src={user.avatar} alt={user.name} />
                      <AvatarFallback className="text-xs">
                        {user.name.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <span className="text-xs">{user.name}</span>
                    <button
                      onClick={() => handleRemoveUser(user.id)}
                      className="ml-1 hover:text-red-500"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* User Selector */}
          <div className="space-y-2">
            <Label>Assign to user:</Label>
            <Popover open={isUserSelectorOpen} onOpenChange={setIsUserSelectorOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={isUserSelectorOpen}
                  className="w-full justify-between"
                >
                  <div className="flex items-center gap-2">
                    <UserPlus className="h-4 w-4" />
                    Select user...
                  </div>
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-full p-0">
                <Command>
                  <CommandInput 
                    placeholder="Search users..." 
                    value={searchQuery}
                    onValueChange={setSearchQuery}
                  />
                  <CommandList>
                    <CommandEmpty>No users found.</CommandEmpty>
                    <CommandGroup>
                      {filteredUsers.map((user) => (
                        <CommandItem
                          key={user.id}
                          value={user.id}
                          onSelect={() => {
                            handleUserSelect(user);
                            setIsUserSelectorOpen(false);
                          }}
                        >
                          <div className="flex items-center gap-3 flex-1">
                            <Avatar className="w-6 h-6">
                              <AvatarImage src={user.avatar} alt={user.name} />
                              <AvatarFallback className="text-xs">
                                {user.name.split(' ').map(n => n[0]).join('')}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1">
                              <div className="font-medium">{user.name}</div>
                              <div className="text-xs text-gray-500">{user.email}</div>
                            </div>
                            <Check
                              className={cn(
                                "ml-auto h-4 w-4",
                                selectedUsers.some(u => u.id === user.id)
                                  ? "opacity-100"
                                  : "opacity-0"
                              )}
                            />
                          </div>
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>

          {/* Actions */}
          <div className="flex gap-2">
            <Button
              onClick={handleAssignTasks}
              disabled={isAssigning || selectedUsers.length === 0}
              className="flex-1 bg-violet-500 hover:bg-violet-600"
            >
              {isAssigning ? 'Assigning...' : 'Assign'}
            </Button>
            
            <Button
              onClick={handleUnassignTasks}
              disabled={isAssigning}
              variant="outline"
              className="border-red-500/50 text-red-400 hover:bg-red-500/20"
            >
              Unassign
            </Button>
            
            <Button
              onClick={onClose}
              variant="outline"
              disabled={isAssigning}
            >
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
