# Design Document

## Overview

The Advanced Task Management System is a comprehensive, exam-focused task management platform designed specifically for students preparing for competitive exams. The system will replace the existing basic TodoBoard with a feature-rich interface that maintains compatibility with the current Supabase database schema while providing advanced functionality for academic planning, progress tracking, and study optimization.

The design follows a modular architecture with clear separation of concerns, leveraging React with TypeScript, Zustand for state management, and the existing Supabase infrastructure. The interface will implement IsotopeAI's design language with a focus on usability, accessibility, and performance.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    UI[Task Management UI] --> Store[Enhanced Todo Store]
    Store --> API[Supabase API Layer]
    API --> DB[(Supabase Database)]
    
    UI --> Components[Reusable Components]
    Components --> Hooks[Custom Hooks]
    
    Store --> Cache[Local Cache & Persistence]
    Store --> Sync[Real-time Sync]
    
    UI --> Analytics[Analytics Engine]
    Analytics --> Insights[Progress Insights]
```

### Database Schema Extensions

While maintaining compatibility with the existing `todos` table, we'll extend functionality through:

1. **Enhanced todos table usage:**
   - Utilize existing fields: `id`, `title`, `description`, `priority`, `dueDate`, `createdBy`, `createdAt`, `updatedAt`
   - Extend `description` field to store structured data (JSON) for subtasks and metadata
   - Use `column_id` for status tracking
   - Leverage `assignedTo` for subject assignment (mapping to userSubjects)

2. **New computed fields and relationships:**
   - Subject integration via `assignedTo` field mapping to `userSubjects.id`
   - Hierarchical structure stored in `description` as JSON metadata
   - Exam linkage through structured tags in `description`

### State Management Architecture

```typescript
interface EnhancedTodoState {
  // Core data
  tasks: Record<string, EnhancedTask>;
  columns: Record<string, TaskColumn>;
  subjects: Record<string, Subject>;
  
  // UI state
  filters: FilterState;
  searchQuery: string;
  selectedTasks: string[];
  viewMode: 'kanban' | 'table' | 'calendar';
  
  // Analytics
  analytics: AnalyticsData;
  
  // Loading states
  loading: LoadingState;
  error: ErrorState;
}
```

## Components and Interfaces

### Core Components Hierarchy

```
TaskManagementPage
├── TaskHeader
│   ├── SearchBar
│   ├── FilterPanel
│   └── ViewModeToggle
├── TaskContent
│   ├── KanbanView
│   │   ├── TaskColumn
│   │   └── TaskCard
│   ├── TableView
│   │   └── TaskTable
│   └── CalendarView
│       └── TaskCalendar
├── TaskSidebar
│   ├── SubjectFilter
│   ├── PriorityFilter
│   └── QuickActions
└── TaskModals
    ├── CreateTaskModal
    ├── EditTaskModal
    └── BulkActionModal
```

### Enhanced Task Interface

```typescript
interface EnhancedTask extends TodoItem {
  // Hierarchical structure
  parentId?: string;
  subtasks: string[];
  depth: number;
  
  // Subject integration
  subjectId?: string;
  subjectColor?: string;
  
  // Exam preparation
  examId?: string;
  chapterTags: string[];
  difficultyLevel: 'easy' | 'medium' | 'hard';
  
  // Progress tracking
  completionPercentage: number;
  timeEstimate?: number; // in minutes
  actualTimeSpent?: number;
  
  // Enhanced metadata
  tags: string[];
  attachments: Attachment[];
  notes: string;
  
  // Analytics
  viewCount: number;
  lastViewed?: number;
}
```

### Subject Integration Interface

```typescript
interface Subject {
  id: string;
  name: string;
  color: string;
  userId: string;
  
  // Task statistics
  totalTasks: number;
  completedTasks: number;
  overdueTasks: number;
  
  // Progress tracking
  completionPercentage: number;
  averagePriority: number;
}
```

### Filter and Search Interface

```typescript
interface FilterState {
  subjects: string[];
  priorities: ('low' | 'medium' | 'high')[];
  statuses: string[];
  dateRange: {
    start?: Date;
    end?: Date;
  };
  tags: string[];
  examIds: string[];
  showOverdue: boolean;
  showCompleted: boolean;
}
```

## Data Models

### Enhanced Task Storage Model

The system will extend the existing Supabase `todos` table with minimal database changes:

1. **Use existing fields** for core functionality (title, description, priority, dueDate, etc.)
2. **Add new columns** for enhanced features (no data migration required)
3. **Extend existing fields** where possible (assignedTo for subject mapping)
4. **Maintain full backward compatibility** with existing tasks

#### Database Schema Extension Strategy

```sql
-- Add new columns to existing todos table (minimal changes)
ALTER TABLE todos ADD COLUMN IF NOT EXISTS parentId text;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS subjectId text REFERENCES userSubjects(id);
ALTER TABLE todos ADD COLUMN IF NOT EXISTS examId text REFERENCES exams(id);
ALTER TABLE todos ADD COLUMN IF NOT EXISTS tags jsonb DEFAULT '[]';
ALTER TABLE todos ADD COLUMN IF NOT EXISTS chapterTags jsonb DEFAULT '[]';
ALTER TABLE todos ADD COLUMN IF NOT EXISTS difficultyLevel text DEFAULT 'medium';
ALTER TABLE todos ADD COLUMN IF NOT EXISTS timeEstimate integer; -- in minutes
ALTER TABLE todos ADD COLUMN IF NOT EXISTS actualTimeSpent integer; -- in minutes
ALTER TABLE todos ADD COLUMN IF NOT EXISTS completionPercentage integer DEFAULT 0;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS notes text;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS viewCount integer DEFAULT 0;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS lastViewed bigint;
```

#### Enhanced TypeScript Interface

```typescript
// Enhanced TodoRecord interface (extends existing with new columns)
interface EnhancedTodoRecord {
  // Existing fields (no changes)
  id: string;
  title: string;
  description: string; // Keep as plain text description
  priority: 'low' | 'medium' | 'high';
  dueDate?: number;
  createdBy: string;
  createdAt: number;
  updatedAt: number;
  column_id: string;
  columnId: string;
  assignedTo?: string; // Keep for backward compatibility
  assignedToName?: string;
  assignedToPhotoUrl?: string;
  groupId?: string;

  // New columns for enhanced features
  parentId?: string; // For hierarchical tasks
  subjectId?: string; // Maps to userSubjects.id
  examId?: string; // Maps to exams.id
  tags: string[]; // Stored as jsonb
  chapterTags: string[]; // Stored as jsonb
  difficultyLevel: 'easy' | 'medium' | 'hard';
  timeEstimate?: number; // in minutes
  actualTimeSpent?: number; // in minutes
  completionPercentage: number; // 0-100
  notes?: string;
  viewCount: number;
  lastViewed?: number;
}

// Client-side computed fields (not stored in database)
interface ComputedTaskFields {
  depth: number; // Calculated from parentId chain
  subtasks: string[]; // Calculated by querying children
  subjectColor?: string; // Fetched from userSubjects
  subjectName?: string; // Fetched from userSubjects
  examName?: string; // Fetched from exams
  examDate?: number; // Fetched from exams
  isOverdue: boolean; // Calculated from dueDate
  hasSubtasks: boolean; // Calculated from children count
}

// Complete enhanced task interface for client use
interface EnhancedTask extends EnhancedTodoRecord, ComputedTaskFields {}
```

#### Migration Strategy (No Data Migration Required)

```typescript
// Backward compatibility utilities
interface TaskCompatibility {
  // Check if task uses new enhanced features
  isEnhancedTask: (task: EnhancedTodoRecord) => boolean;

  // Convert legacy task to enhanced format (add default values)
  enhanceTask: (task: TodoRecord) => EnhancedTodoRecord;

  // Ensure all new fields have default values
  normalizeTask: (task: Partial<EnhancedTodoRecord>) => EnhancedTodoRecord;
}
```
```

### Hierarchical Task Structure

```typescript
interface SubtaskData {
  id: string;
  title: string;
  completed: boolean;
  createdAt: number;
  completedAt?: number;
}

interface TaskHierarchy {
  parentId?: string;
  children: string[];
  depth: number;
  path: string[]; // Array of parent IDs
}
```

## Error Handling

### Error Categories and Handling Strategy

1. **Network Errors:**
   - Implement retry logic with exponential backoff
   - Queue operations for offline sync
   - Display user-friendly error messages

2. **Validation Errors:**
   - Client-side validation before API calls
   - Real-time form validation feedback
   - Prevent invalid state transitions

3. **Database Errors:**
   - Handle constraint violations gracefully
   - Provide fallback options for failed operations
   - Maintain data consistency

```typescript
interface ErrorHandling {
  // Error types
  NetworkError: 'NETWORK_ERROR';
  ValidationError: 'VALIDATION_ERROR';
  DatabaseError: 'DATABASE_ERROR';
  AuthError: 'AUTH_ERROR';
  
  // Error recovery strategies
  retry: (operation: () => Promise<any>, maxRetries: number) => Promise<any>;
  queue: (operation: QueuedOperation) => void;
  fallback: (error: Error, fallbackAction: () => void) => void;
}
```

### Offline Capability

```typescript
interface OfflineQueue {
  operations: QueuedOperation[];
  syncStatus: 'synced' | 'pending' | 'syncing' | 'error';
  lastSyncTime: number;
  
  // Methods
  addOperation: (operation: QueuedOperation) => void;
  processQueue: () => Promise<void>;
  clearQueue: () => void;
}
```

## Testing Strategy

### Unit Testing

1. **Component Testing:**
   - Test individual components in isolation
   - Mock external dependencies
   - Verify prop handling and event emission

2. **Store Testing:**
   - Test state mutations and side effects
   - Verify API integration
   - Test error handling scenarios

3. **Utility Testing:**
   - Test helper functions and utilities
   - Verify data transformations
   - Test validation logic

### Integration Testing

1. **API Integration:**
   - Test Supabase operations
   - Verify real-time subscriptions
   - Test offline/online transitions

2. **User Workflows:**
   - Test complete user journeys
   - Verify drag-and-drop functionality
   - Test bulk operations

### Performance Testing

1. **Load Testing:**
   - Test with large numbers of tasks (1000+)
   - Verify virtualization performance
   - Test search and filter performance

2. **Memory Testing:**
   - Monitor memory usage with large datasets
   - Test for memory leaks
   - Verify cleanup on component unmount

## UI/UX Design Specifications

### IsotopeAI Design Language Implementation

1. **Color Palette:**
   ```css
   :root {
     --background: #030303;
     --foreground: #ffffff;
     --primary: #8b5cf6; /* violet */
     --secondary: #a855f7; /* purple */
     --accent: #f43f5e; /* rose */
     --success: #10b981; /* emerald */
     --warning: #f59e0b;
     --error: #ef4444;
   }
   ```

2. **Typography:**
   - Primary font: font-onest
   - Heading hierarchy: h1 (2xl), h2 (xl), h3 (lg)
   - Body text: base size with proper line height

3. **Spacing System:**
   - Base unit: 4px
   - Component spacing: 16px, 24px, 32px
   - Section spacing: 48px, 64px

### Responsive Design

1. **Breakpoints:**
   - Mobile: 320px - 768px
   - Tablet: 768px - 1024px
   - Desktop: 1024px+

2. **Mobile Optimizations:**
   - Touch-friendly tap targets (44px minimum)
   - Swipe gestures for task actions
   - Collapsible sidebar navigation
   - Bottom sheet modals

### Animation and Micro-interactions

1. **Task Interactions:**
   - Smooth drag-and-drop with visual feedback
   - Completion animations with celebration effects
   - Hover states with subtle elevation changes

2. **Transition Specifications:**
   - Duration: 200ms for quick interactions, 300ms for complex animations
   - Easing: cubic-bezier(0.4, 0, 0.2, 1) for natural motion
   - Stagger animations for list items

### Accessibility Features

1. **Keyboard Navigation:**
   - Tab order follows logical flow
   - Arrow keys for list navigation
   - Enter/Space for activation
   - Escape for modal dismissal

2. **Screen Reader Support:**
   - Semantic HTML structure
   - ARIA labels and descriptions
   - Live regions for dynamic updates
   - Skip links for main content

3. **Visual Accessibility:**
   - High contrast ratios (4.5:1 minimum)
   - Focus indicators with 2px outline
   - Color-blind friendly palette
   - Scalable text up to 200%

## Performance Optimizations

### Rendering Optimizations

1. **Virtualization:**
   - Implement virtual scrolling for large task lists (1000+ tasks)
   - Lazy load task details and attachments
   - Memoize expensive calculations (hierarchy computation, progress calculations)
   - Use React.memo for task cards and list items

2. **State Management:**
   - Selective re-rendering with React.memo and useMemo
   - Optimistic updates for better UX (immediate UI updates, rollback on error)
   - Debounced search and filter operations (300ms delay)
   - Batch state updates for bulk operations

3. **Hierarchical Task Optimization:**
   - Cache computed hierarchy trees
   - Lazy expand/collapse for deep task structures
   - Virtualize nested task lists
   - Optimize parent-child relationship calculations

### Data Loading Strategies

1. **Progressive Loading:**
   - Load essential data first (task titles, priorities, due dates)
   - Lazy load secondary information (descriptions, attachments, analytics)
   - Implement skeleton loading states for all components
   - Progressive enhancement for advanced features

2. **Caching Strategy:**
   - Cache frequently accessed data (subjects, exam data)
   - Implement cache invalidation on data changes
   - Use service worker for offline caching
   - Cache computed analytics and progress data

3. **Real-time Optimization:**
   - Throttle real-time updates to prevent UI thrashing
   - Batch multiple updates into single UI refresh
   - Implement conflict resolution for concurrent edits

### Bundle Optimization

1. **Code Splitting:**
   - Route-based code splitting for task management
   - Component-based lazy loading for advanced features
   - Dynamic imports for heavy features (analytics, calendar view)
   - Separate bundles for mobile and desktop optimizations

2. **Asset Optimization:**
   - Optimize images and icons used in task interface
   - Minimize CSS and JavaScript bundles
   - Use CDN for static assets
   - Implement progressive image loading for attachments

### Integration with IsotopeAI Ecosystem

1. **Study Timer Integration:**
   - Link task completion with study session data
   - Sync time estimates with actual study time
   - Integrate break analysis with task productivity

2. **Analytics Integration:**
   - Share task completion data with main analytics dashboard
   - Integrate subject-wise task progress with overall subject analytics
   - Sync exam preparation tasks with exam performance tracking

3. **Mock Test Integration:**
   - Create tasks automatically from mock test results
   - Link chapter-wise tasks with mock test performance
   - Integrate mistake analysis with task creation