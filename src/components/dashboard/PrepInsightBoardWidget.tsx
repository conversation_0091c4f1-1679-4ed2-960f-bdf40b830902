import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card';
import { BarChart3, Flame, Trophy, TrendingUp, Clock, Target } from 'lucide-react';

export const PrepInsightBoardWidget: React.FC = () => {
  return (
    <Card className="bg-white dark:bg-gray-900/50 border-l-4 border-l-teal-500 hover:shadow-lg transition-all duration-300 h-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-3 text-xl">
          <BarChart3 className="h-6 w-6 text-teal-500" />
          <span className="text-gray-800 dark:text-gray-200">
            Prep Insight Board
          </span>
        </CardTitle>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Your study metrics, streaks, and performance insights
        </p>
      </CardHeader>
      <CardContent className="pb-6">
        {/* Metrics Grid */}
        <div className="space-y-6">
          {/* Average Daily Study Time */}
          <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/10 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-800 rounded-full flex items-center justify-center">
                <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-blue-700 dark:text-blue-400">Avg Time Studied</p>
                <p className="text-xs text-blue-600 dark:text-blue-500">Per day (7-day average)</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">2h 45m</p>
              <div className="flex items-center gap-1">
                <TrendingUp className="h-3 w-3 text-green-500" />
                <span className="text-xs text-green-600">+12%</span>
              </div>
            </div>
          </div>

          {/* Highest Study Hours */}
          <div className="flex items-center justify-between p-4 bg-amber-50 dark:bg-amber-900/10 rounded-lg border border-amber-200 dark:border-amber-800">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-amber-100 dark:bg-amber-800 rounded-full flex items-center justify-center">
                <Trophy className="h-5 w-5 text-amber-600 dark:text-amber-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-amber-700 dark:text-amber-400">Highest Study Hours</p>
                <p className="text-xs text-amber-600 dark:text-amber-500">Personal record</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-amber-900 dark:text-amber-100">8h 30m</p>
              <p className="text-xs text-amber-600 dark:text-amber-500">Dec 15, 2024</p>
            </div>
          </div>

          {/* Study Streak */}
          <div className="flex items-center justify-between p-4 bg-emerald-50 dark:bg-emerald-900/10 rounded-lg border border-emerald-200 dark:border-emerald-800">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-emerald-100 dark:bg-emerald-800 rounded-full flex items-center justify-center">
                <Flame className="h-5 w-5 text-emerald-600 dark:text-emerald-400" />
              </div>
              <div>
                <p className="text-sm font-medium text-emerald-700 dark:text-emerald-400">Study Streak</p>
                <p className="text-xs text-emerald-600 dark:text-emerald-500">Current streak</p>
              </div>
            </div>
            <div className="text-right">
              <p className="text-2xl font-bold text-emerald-900 dark:text-emerald-100">12 days</p>
              <div className="flex items-center gap-1">
                <Target className="h-3 w-3 text-emerald-500" />
                <span className="text-xs text-emerald-600">Next: 14 days</span>
              </div>
            </div>
          </div>
        </div>

        {/* Mini Trend Chart Placeholder */}
        <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">7-Day Trend</span>
            <TrendingUp className="h-4 w-4 text-teal-500" />
          </div>
          <div className="h-16 bg-gradient-to-r from-teal-100 to-teal-200 dark:from-teal-900/20 dark:to-teal-800/20 rounded flex items-end justify-center">
            <span className="text-xs text-teal-600 dark:text-teal-400 mb-2">Chart will be implemented</span>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-4 flex gap-2">
          <button className="flex-1 px-3 py-2 bg-teal-600 hover:bg-teal-700 text-white text-sm rounded-lg transition-colors duration-200">
            View Analytics
          </button>
          <button className="px-3 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 text-sm rounded-lg transition-colors duration-200">
            History
          </button>
        </div>
      </CardContent>
    </Card>
  );
};
