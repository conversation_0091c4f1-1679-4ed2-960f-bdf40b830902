import { UpcomingTest, MockTest, TestStatus } from '@/types/mockTest';
import { upcomingTestStorage, enhancedMockTestUtils, dDayStorage } from './mockTestLocalStorage';
import { format, isAfter, isSameDay, parseISO } from 'date-fns';

export interface CompletedTestNotification {
  id: string;
  testName: string;
  testDate: string;
  daysOverdue: number;
  upcomingTest: UpcomingTest;
}

export interface TestCompletionStats {
  pendingResults: number;
  completedToday: number;
  missedTests: number;
  upcomingThisWeek: number;
}

/**
 * Service for managing test completion detection and status transitions
 */
export class TestCompletionService {
  private static instance: TestCompletionService;
  private notificationCallbacks: ((notifications: CompletedTestNotification[]) => void)[] = [];

  static getInstance(): TestCompletionService {
    if (!TestCompletionService.instance) {
      TestCompletionService.instance = new TestCompletionService();
    }
    return TestCompletionService.instance;
  }

  /**
   * Check for tests that should be marked as completed
   */
  checkForCompletedTests(userId: string): CompletedTestNotification[] {
    const upcomingTests = upcomingTestStorage.getAll(userId);
    const completedNotifications: CompletedTestNotification[] = [];
    const today = new Date();

    upcomingTests.forEach(test => {
      const testDate = parseISO(test.date);
      
      // Check if test date has passed
      if (isAfter(today, testDate) && !isSameDay(today, testDate)) {
        // Check if this test hasn't been converted to a completed test yet
        const existingMockTest = this.findMockTestByUpcomingId(userId, test.id);
        
        if (!existingMockTest) {
          const daysOverdue = Math.floor((today.getTime() - testDate.getTime()) / (1000 * 60 * 60 * 24));
          
          completedNotifications.push({
            id: test.id,
            testName: test.name,
            testDate: test.date,
            daysOverdue,
            upcomingTest: test
          });
        }
      }
    });

    return completedNotifications;
  }

  /**
   * Get tests that are happening today
   */
  getTodaysTests(userId: string): UpcomingTest[] {
    const upcomingTests = upcomingTestStorage.getAll(userId);
    const today = format(new Date(), 'yyyy-MM-dd');

    return upcomingTests.filter(test => test.date === today);
  }

  /**
   * Get comprehensive test completion statistics
   */
  getCompletionStats(userId: string): TestCompletionStats {
    const upcomingTests = upcomingTestStorage.getAll(userId);
    const mockTests = enhancedMockTestUtils.getAll(userId);
    const today = new Date();
    const todayStr = format(today, 'yyyy-MM-dd');
    
    // Calculate week range
    const weekFromNow = new Date();
    weekFromNow.setDate(today.getDate() + 7);
    const weekFromNowStr = format(weekFromNow, 'yyyy-MM-dd');

    const pendingResults = this.checkForCompletedTests(userId).length;
    
    const completedToday = mockTests.filter(test => 
      test.date === todayStr && test.status === 'completed'
    ).length;

    const missedTests = mockTests.filter(test => 
      test.status === 'missed'
    ).length;

    const upcomingThisWeek = upcomingTests.filter(test => 
      test.date >= todayStr && test.date <= weekFromNowStr
    ).length;

    return {
      pendingResults,
      completedToday,
      missedTests,
      upcomingThisWeek
    };
  }

  /**
   * Manually mark a test as completed and create a pending mock test
   */
  markTestAsCompleted(userId: string, upcomingTestId: string): MockTest {
    const tests = upcomingTestStorage.getAll(userId);
    const upcomingTest = tests.find(t => t.id === upcomingTestId);
    if (!upcomingTest) {
      throw new Error('Upcoming test not found');
    }

    // Create a mock test with pending status
    const mockTest: MockTest = {
      id: `mock-${Date.now()}`,
      name: upcomingTest.name,
      date: upcomingTest.date,
      time: upcomingTest.time,
      subjectMarks: [], // Will be filled when results are entered
      totalMarksObtained: 0,
      totalMarks: 0,
      notes: '',
      createdAt: new Date().toISOString(),
      userId: userId,
      
      // Enhanced fields from upcoming test
      testType: upcomingTest.testType || 'mock',
      testEnvironment: upcomingTest.testEnvironment,
      duration: upcomingTest.duration,
      totalQuestions: upcomingTest.totalQuestions,
      categoryId: upcomingTest.categoryId,
      testPaperUrl: upcomingTest.testPaperUrl,
      
      targetScore: upcomingTest.targetScore || 0,
      confidenceLevel: upcomingTest.confidenceLevel,
      preparationTime: upcomingTest.preparationTime,
      studyMaterials: upcomingTest.studyMaterials,
      
      difficulty: upcomingTest.expectedDifficulty || 'medium',
      isReviewed: false,
      mistakes: [],
      takeaways: [],
      analysisCompleted: false,
      mistakesAnalyzed: false,
      takeawaysRecorded: false,
      
      status: 'completed_pending',
      completedAt: new Date().toISOString(),
      isFromUpcoming: true,
      upcomingTestId: upcomingTest.id
    };

    // Save the mock test
    const savedMockTest = enhancedMockTestUtils.save(userId, mockTest);

    // Update D-Day status if synced
    try {
      const dDayExam = dDayStorage.getById(userId, upcomingTest.id);
      if (dDayExam) {
        dDayStorage.save(userId, {
          ...dDayExam,
          status: 'completed',
          updatedAt: new Date().toISOString()
        });
      }
    } catch (error) {
      console.warn('Failed to update D-Day status:', error);
    }

    return savedMockTest;
  }

  /**
   * Mark a test as missed
   */
  markTestAsMissed(userId: string, upcomingTestId: string): void {
    const tests = upcomingTestStorage.getAll(userId);
    const upcomingTest = tests.find(t => t.id === upcomingTestId);
    if (!upcomingTest) {
      throw new Error('Upcoming test not found');
    }

    // Create a mock test with missed status
    const mockTest: MockTest = {
      id: `mock-missed-${Date.now()}`,
      name: upcomingTest.name,
      date: upcomingTest.date,
      time: upcomingTest.time,
      subjectMarks: [],
      totalMarksObtained: 0,
      totalMarks: 0,
      notes: 'Test was missed',
      createdAt: new Date().toISOString(),
      userId: userId,
      
      testType: upcomingTest.testType || 'mock',
      testEnvironment: upcomingTest.testEnvironment,
      categoryId: upcomingTest.categoryId,
      
      targetScore: upcomingTest.targetScore || 0,
      difficulty: 'medium',
      isReviewed: false,
      mistakes: [],
      takeaways: [],
      analysisCompleted: false,
      mistakesAnalyzed: false,
      takeawaysRecorded: false,
      
      status: 'missed',
      isFromUpcoming: true,
      upcomingTestId: upcomingTest.id
    };

    enhancedMockTestUtils.save(userId, mockTest);

    // Remove from upcoming tests
    upcomingTestStorage.delete(userId, upcomingTestId);

    // Update D-Day status
    try {
      const dDayExam = dDayStorage.getById(userId, upcomingTest.id);
      if (dDayExam) {
        dDayStorage.save(userId, {
          ...dDayExam,
          status: 'missed',
          updatedAt: new Date().toISOString()
        });
      }
    } catch (error) {
      console.warn('Failed to update D-Day status:', error);
    }
  }

  /**
   * Find mock test created from an upcoming test
   */
  private findMockTestByUpcomingId(userId: string, upcomingTestId: string): MockTest | null {
    const mockTests = enhancedMockTestUtils.getAll(userId);
    return mockTests.find(test => test.upcomingTestId === upcomingTestId) || null;
  }

  /**
   * Subscribe to completion notifications
   */
  onCompletionNotifications(callback: (notifications: CompletedTestNotification[]) => void): () => void {
    this.notificationCallbacks.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.notificationCallbacks.indexOf(callback);
      if (index > -1) {
        this.notificationCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Trigger notification callbacks
   */
  private notifyCallbacks(notifications: CompletedTestNotification[]): void {
    this.notificationCallbacks.forEach(callback => {
      try {
        callback(notifications);
      } catch (error) {
        console.error('Error in completion notification callback:', error);
      }
    });
  }

  /**
   * Run daily check and notify subscribers
   */
  runDailyCheck(userId: string): CompletedTestNotification[] {
    const notifications = this.checkForCompletedTests(userId);
    if (notifications.length > 0) {
      this.notifyCallbacks(notifications);
    }
    return notifications;
  }
}

// Export singleton instance
export const testCompletionService = TestCompletionService.getInstance();
