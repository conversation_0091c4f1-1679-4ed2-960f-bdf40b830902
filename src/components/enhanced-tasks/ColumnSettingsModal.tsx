import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Trash2, Plus, Settings, Palette } from 'lucide-react';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { TodoColumn } from '@/types/todo';

interface ColumnSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  columnId?: string;
}

const predefinedColors = [
  { name: 'Indigo', value: '#6366f1' },
  { name: '<PERSON>', value: '#8b5cf6' },
  { name: 'Pink', value: '#ec4899' },
  { name: '<PERSON>', value: '#f43f5e' },
  { name: 'Orange', value: '#f97316' },
  { name: 'Amber', value: '#f59e0b' },
  { name: 'Yellow', value: '#eab308' },
  { name: 'Lime', value: '#84cc16' },
  { name: 'Green', value: '#22c55e' },
  { name: 'Emerald', value: '#10b981' },
  { name: 'Teal', value: '#14b8a6' },
  { name: 'Cyan', value: '#06b6d4' },
  { name: 'Sky', value: '#0ea5e9' },
  { name: 'Blue', value: '#3b82f6' },
  { name: 'Violet', value: '#7c3aed' },
  { name: 'Gray', value: '#6b7280' },
];

export function ColumnSettingsModal({ isOpen, onClose, columnId }: ColumnSettingsModalProps) {
  const { board, addColumn, updateColumn, deleteColumn } = useEnhancedTodoStore();
  
  const [columns, setColumns] = useState<TodoColumn[]>([]);
  const [editingColumn, setEditingColumn] = useState<TodoColumn | null>(null);
  const [newColumnTitle, setNewColumnTitle] = useState('');
  const [newColumnColor, setNewColumnColor] = useState('#6366f1');
  const [isAddingColumn, setIsAddingColumn] = useState(false);

  useEffect(() => {
    if (isOpen) {
      const columnList = Object.values(board.columns);
      setColumns(columnList);
      
      if (columnId) {
        const column = board.columns[columnId];
        if (column) {
          setEditingColumn(column);
        }
      }
    }
  }, [isOpen, board.columns, columnId]);

  const handleAddColumn = async () => {
    if (!newColumnTitle.trim()) return;
    
    try {
      await addColumn(newColumnTitle.trim(), newColumnColor);
      setNewColumnTitle('');
      setNewColumnColor('#6366f1');
      setIsAddingColumn(false);
    } catch (error) {
      console.error('Failed to add column:', error);
    }
  };

  const handleUpdateColumn = async (column: TodoColumn) => {
    try {
      await updateColumn(column.id, column.title, column.color);
      setEditingColumn(null);
    } catch (error) {
      console.error('Failed to update column:', error);
    }
  };

  const handleDeleteColumn = async (columnId: string) => {
    if (columns.length <= 1) {
      alert('Cannot delete the last column');
      return;
    }
    
    if (confirm('Are you sure you want to delete this column? All tasks will be moved to the first column.')) {
      try {
        await deleteColumn(columnId);
      } catch (error) {
        console.error('Failed to delete column:', error);
      }
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto bg-white/95 dark:bg-[#030303]/95 backdrop-blur-md border-gray-200 dark:border-gray-800">
        <DialogHeader>
          <DialogTitle className="font-onest text-xl text-gray-900 dark:text-white flex items-center gap-2">
            <Settings className="h-5 w-5 text-violet-400" />
            Column Settings
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Existing Columns */}
          <div className="space-y-4">
            <h3 className="font-onest text-lg font-semibold text-gray-900 dark:text-white">
              Manage Columns
            </h3>
            
            {columns.map((column) => (
              <motion.div
                key={column.id}
                className="flex items-center gap-3 p-4 rounded-lg border border-gray-200 dark:border-gray-800 bg-gray-50/50 dark:bg-gray-900/50"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <div
                  className="w-4 h-4 rounded-full"
                  style={{ backgroundColor: column.color }}
                />
                
                {editingColumn?.id === column.id ? (
                  <div className="flex-1 flex items-center gap-2">
                    <Input
                      value={editingColumn.title}
                      onChange={(e) => setEditingColumn({
                        ...editingColumn,
                        title: e.target.value
                      })}
                      className="flex-1"
                    />
                    
                    <Select
                      value={editingColumn.color}
                      onValueChange={(color) => setEditingColumn({
                        ...editingColumn,
                        color
                      })}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {predefinedColors.map((color) => (
                          <SelectItem key={color.value} value={color.value}>
                            <div className="flex items-center gap-2">
                              <div
                                className="w-3 h-3 rounded-full"
                                style={{ backgroundColor: color.value }}
                              />
                              {color.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    
                    <Button
                      size="sm"
                      onClick={() => handleUpdateColumn(editingColumn)}
                      className="bg-emerald-500 hover:bg-emerald-600"
                    >
                      Save
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setEditingColumn(null)}
                    >
                      Cancel
                    </Button>
                  </div>
                ) : (
                  <>
                    <div className="flex-1">
                      <span className="font-medium text-gray-900 dark:text-white">
                        {column.title}
                      </span>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {board.columns[column.id]?.taskIds.length || 0} tasks
                      </div>
                    </div>
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setEditingColumn(column)}
                      className="border-violet-500/50 text-violet-400 hover:bg-violet-500/20"
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                    
                    {columns.length > 1 && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDeleteColumn(column.id)}
                        className="border-red-500/50 text-red-400 hover:bg-red-500/20"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </>
                )}
              </motion.div>
            ))}
          </div>

          {/* Add New Column */}
          <div className="space-y-4">
            <h3 className="font-onest text-lg font-semibold text-gray-900 dark:text-white">
              Add New Column
            </h3>
            
            {isAddingColumn ? (
              <div className="space-y-4 p-4 rounded-lg border border-gray-200 dark:border-gray-800 bg-gray-50/50 dark:bg-gray-900/50">
                <div className="space-y-2">
                  <Label htmlFor="column-title">Column Title</Label>
                  <Input
                    id="column-title"
                    value={newColumnTitle}
                    onChange={(e) => setNewColumnTitle(e.target.value)}
                    placeholder="Enter column title..."
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="column-color">Column Color</Label>
                  <Select value={newColumnColor} onValueChange={setNewColumnColor}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {predefinedColors.map((color) => (
                        <SelectItem key={color.value} value={color.value}>
                          <div className="flex items-center gap-2">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: color.value }}
                            />
                            {color.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    onClick={handleAddColumn}
                    disabled={!newColumnTitle.trim()}
                    className="bg-emerald-500 hover:bg-emerald-600"
                  >
                    Add Column
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={() => {
                      setIsAddingColumn(false);
                      setNewColumnTitle('');
                      setNewColumnColor('#6366f1');
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            ) : (
              <Button
                onClick={() => setIsAddingColumn(true)}
                className="border-violet-500/50 text-violet-400 hover:bg-violet-500/20"
                variant="outline"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add New Column
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
