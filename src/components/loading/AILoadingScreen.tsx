import React from 'react';
import { motion } from 'framer-motion';
import { LoadingSpinner } from '../ui/loading-spinner';
import { cn } from '@/lib/utils';

interface AILoadingScreenProps {
  message?: string;
  variant?: 'thinking' | 'processing' | 'generating';
  className?: string;
}

const thinkingMessages = [
  'Analyzing your question...',
  'Processing information...',
  'Generating response...',
  'Almost ready...',
];

const processingMessages = [
  'Understanding context...',
  'Searching knowledge base...',
  'Formulating answer...',
  'Finalizing response...',
];

const generatingMessages = [
  'Creating solution...',
  'Building explanation...',
  'Adding examples...',
  'Polishing response...',
];

export const AILoadingScreen: React.FC<AILoadingScreenProps> = ({
  message,
  variant = 'thinking',
  className,
}) => {
  const [currentMessageIndex, setCurrentMessageIndex] = React.useState(0);
  const [displayMessage, setDisplayMessage] = React.useState(message || '');

  const getMessages = () => {
    switch (variant) {
      case 'processing':
        return processingMessages;
      case 'generating':
        return generatingMessages;
      default:
        return thinkingMessages;
    }
  };

  React.useEffect(() => {
    if (!message) {
      const messages = getMessages();
      const interval = setInterval(() => {
        setCurrentMessageIndex((prev) => (prev + 1) % messages.length);
      }, 2000);

      return () => clearInterval(interval);
    }
  }, [message, variant]);

  React.useEffect(() => {
    if (!message) {
      setDisplayMessage(getMessages()[currentMessageIndex]);
    }
  }, [currentMessageIndex, message, variant]);

  return (
    <motion.div
      className={cn(
        'flex flex-col items-center justify-center space-y-6 p-8',
        className
      )}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      transition={{ duration: 0.3 }}
    >
      {/* AI Brain Animation */}
      <div className="relative">
        <motion.div
          className="w-16 h-16 bg-gradient-to-br from-primary to-primary/60 rounded-2xl flex items-center justify-center"
          animate={{
            rotate: [0, 5, -5, 0],
            scale: [1, 1.05, 1],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        >
          <motion.div
            className="text-white text-2xl font-bold"
            animate={{
              opacity: [0.7, 1, 0.7],
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
            }}
          >
            AI
          </motion.div>
        </motion.div>

        {/* Thinking Particles */}
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-primary/40 rounded-full"
            style={{
              top: '50%',
              left: '50%',
              transformOrigin: '0 0',
            }}
            animate={{
              rotate: [0, 360],
              scale: [0, 1, 0],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: i * 0.3,
            }}
            initial={{
              x: Math.cos((i * 60 * Math.PI) / 180) * 40,
              y: Math.sin((i * 60 * Math.PI) / 180) * 40,
            }}
          />
        ))}
      </div>

      {/* Loading Spinner */}
      <LoadingSpinner variant="wave" size="lg" color="primary" />

      {/* Dynamic Message */}
      <motion.div
        className="text-center space-y-2"
        key={displayMessage}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        transition={{ duration: 0.4 }}
      >
        <h3 className="text-lg font-semibold text-foreground">
          {displayMessage}
        </h3>
        <p className="text-sm text-muted-foreground">
          Our AI is working on your request
        </p>
      </motion.div>

      {/* Progress Indicator */}
      <div className="flex space-x-2">
        {[...Array(4)].map((_, i) => (
          <motion.div
            key={i}
            className="w-2 h-2 bg-primary/30 rounded-full"
            animate={{
              scale: [1, 1.5, 1],
              backgroundColor: [
                'rgba(var(--primary), 0.3)',
                'rgba(var(--primary), 1)',
                'rgba(var(--primary), 0.3)',
              ],
            }}
            transition={{
              duration: 1.2,
              repeat: Infinity,
              delay: i * 0.2,
            }}
          />
        ))}
      </div>
    </motion.div>
  );
};