import { format } from "date-fns";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Pen<PERSON>l,
  Trash,
  Award,
  Calendar as CalendarIcon,
  BarChart3,
  FileText,
  CheckCircle,
  ExternalLink,
  Eye,
  Target,
  Clock,
  BookOpen,
  TrendingUp,
  AlertTriangle,
  Lightbulb,
  Timer,
  Users,
  Home,
  Monitor,
  Globe,
  Star,
  Brain,
  Zap,
  CheckCircle2,
  XCircle,
  AlertCircle,
  HelpCircle
} from "lucide-react";
import { MockTest, TestCategory } from "@/types/mockTest";
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface MockTestCardProps {
  mockTest: MockTest;
  onEditClick: (mockTest: MockTest) => void;
  onDeleteClick: (mockTestId: string) => void;
  onViewDetails?: (mockTest: MockTest) => void;
  categories?: TestCategory[];
}

export function MockTestCard({
  mockTest,
  onEditClick,
  onDeleteClick,
  onViewDetails,
  categories = []
}: MockTestCardProps) {
  const percentage = mockTest.totalMarks && mockTest.totalMarks > 0
    ? (mockTest.totalMarksObtained || 0) / mockTest.totalMarks * 100
    : 0;
  const formattedPercentage = percentage.toFixed(1);

  const getCategoryName = (categoryId?: string) => {
    if (!categoryId) return null;
    return categories.find(cat => cat.id === categoryId)?.name;
  };

  const getCategoryColor = (categoryId?: string) => {
    if (!categoryId) return '#6b7280';
    return categories.find(cat => cat.id === categoryId)?.color || '#6b7280';
  };

  const getGradeColor = (percentage: number) => {
    if (percentage >= 90) return "bg-emerald-500";
    if (percentage >= 75) return "bg-green-500";
    if (percentage >= 60) return "bg-yellow-500";
    if (percentage >= 40) return "bg-orange-500";
    return "bg-red-500";
  };

  const getTextColor = (percentage: number) => {
    if (percentage >= 90) return "text-emerald-500";
    if (percentage >= 75) return "text-green-500";
    if (percentage >= 60) return "text-yellow-500";
    if (percentage >= 40) return "text-orange-500";
    return "text-red-500";
  };

  const getPerformanceBadge = (percentage: number) => {
    if (percentage >= 90) return { label: "Excellent", color: "bg-emerald-500/10 text-emerald-600 border-emerald-500/20" };
    if (percentage >= 75) return { label: "Good", color: "bg-green-500/10 text-green-600 border-green-500/20" };
    if (percentage >= 60) return { label: "Average", color: "bg-yellow-500/10 text-yellow-600 border-yellow-500/20" };
    if (percentage >= 40) return { label: "Below Average", color: "bg-orange-500/10 text-orange-600 border-orange-500/20" };
    return { label: "Poor", color: "bg-red-500/10 text-red-600 border-red-500/20" };
  };

  const getTestTypeIcon = (testType?: string) => {
    switch (testType) {
      case 'practice': return <BookOpen className="h-4 w-4" />;
      case 'mock': return <Target className="h-4 w-4" />;
      case 'actual_exam': return <Award className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const getTestEnvironmentIcon = (environment?: string) => {
    switch (environment) {
      case 'home': return <Home className="h-3.5 w-3.5" />;
      case 'center': return <Users className="h-3.5 w-3.5" />;
      case 'online': return <Monitor className="h-3.5 w-3.5" />;
      default: return <Globe className="h-3.5 w-3.5" />;
    }
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'easy': return "text-green-500";
      case 'medium': return "text-yellow-500";
      case 'hard': return "text-orange-500";
      case 'very_hard': return "text-red-500";
      default: return "text-gray-500";
    }
  };

  const getReviewStatusIcon = () => {
    if (mockTest.analysisCompleted) return <CheckCircle2 className="h-4 w-4 text-emerald-500" />;
    if (mockTest.isReviewed) return <Eye className="h-4 w-4 text-blue-500" />;
    return <AlertCircle className="h-4 w-4 text-orange-500" />;
  };

  const formatDuration = (minutes?: number) => {
    if (!minutes) return 'N/A';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const performanceBadge = getPerformanceBadge(percentage);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      whileHover={{ y: -4, scale: 1.02, transition: { duration: 0.2 } }}
      className="h-full"
    >
      <Card className="flex flex-col h-full overflow-hidden transition-all duration-300 hover:shadow-xl border border-border bg-card/50 backdrop-blur-sm relative group">
        {/* Performance indicator bar */}
        <div className={`absolute top-0 left-0 right-0 h-1.5 ${getGradeColor(percentage)}`} />

        <CardHeader className="p-5 relative z-10">
          <div className="flex justify-between items-start w-full">
            <div className="flex-1 mr-3">
              {/* Title with test type icon */}
              <div className="flex items-center gap-2 mb-3">
                <div className="flex items-center gap-2">
                  {getTestTypeIcon(mockTest.testType)}
                  <h3 className="font-bold text-lg line-clamp-1 text-foreground transition-colors duration-300">
                    {mockTest.name}
                  </h3>
                </div>
                {getReviewStatusIcon()}
              </div>

              {/* Enhanced Metadata */}
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <div className="flex items-center gap-1.5">
                    <CalendarIcon className="h-3.5 w-3.5" />
                    <span>
                      {mockTest.date ? format(new Date(mockTest.date), "MMM dd, yyyy") : "No date"}
                    </span>
                  </div>
                  {mockTest.time && (
                    <>
                      <span className="text-muted-foreground/50">•</span>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>{mockTest.time}</span>
                      </div>
                    </>
                  )}
                </div>

                <div className="flex items-center gap-2 text-xs">
                  {getCategoryName(mockTest.categoryId) && (
                    <Badge
                      variant="secondary"
                      className="text-xs font-normal"
                      style={{
                        backgroundColor: `${getCategoryColor(mockTest.categoryId)}15`,
                        color: getCategoryColor(mockTest.categoryId),
                        borderColor: `${getCategoryColor(mockTest.categoryId)}30`
                      }}
                    >
                      {getCategoryName(mockTest.categoryId)}
                    </Badge>
                  )}

                  <Badge className={`text-xs font-medium border ${performanceBadge.color}`}>
                    {performanceBadge.label}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Enhanced Score Circle */}
            <div className="flex flex-col items-center gap-2">
              <div className={`flex items-center justify-center h-16 w-16 rounded-full bg-gradient-to-br ${getGradeColor(percentage)}/10 border-2 ${getGradeColor(percentage).replace('bg-', 'border-')}/30`}>
                <span className={`text-lg font-bold ${getTextColor(percentage)}`}>
                  {formattedPercentage}%
                </span>
              </div>
              {mockTest.targetScore && (
                <div className="text-xs text-muted-foreground text-center">
                  Target: {mockTest.targetScore}%
                </div>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-5 flex-grow space-y-4">
          {/* Test Configuration Info */}
          <div className="grid grid-cols-2 gap-3 text-xs">
            {mockTest.duration && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <Timer className="h-3.5 w-3.5" />
                <span>{formatDuration(mockTest.duration)}</span>
              </div>
            )}

            {mockTest.totalQuestions && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <HelpCircle className="h-3.5 w-3.5" />
                <span>{mockTest.totalQuestions} Qs</span>
              </div>
            )}

            {mockTest.testEnvironment && (
              <div className="flex items-center gap-2 text-muted-foreground">
                {getTestEnvironmentIcon(mockTest.testEnvironment)}
                <span className="capitalize">{mockTest.testEnvironment}</span>
              </div>
            )}

            {mockTest.difficulty && (
              <div className="flex items-center gap-2">
                <Star className={`h-3.5 w-3.5 ${getDifficultyColor(mockTest.difficulty)}`} />
                <span className={`capitalize ${getDifficultyColor(mockTest.difficulty)}`}>
                  {mockTest.difficulty}
                </span>
              </div>
            )}
          </div>

          {/* Subject Badges */}
          {mockTest.subjectMarks && mockTest.subjectMarks.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {mockTest.subjectMarks.map((subject, index) => (
                <Badge
                  key={index}
                  style={{
                    backgroundColor: `${subject.subjectColor}15`,
                    color: subject.subjectColor,
                    borderColor: `${subject.subjectColor}30`
                  }}
                  className="text-xs font-medium border"
                >
                  {subject.subject}
                </Badge>
              ))}
            </div>
          )}

          {/* Subject-wise Performance */}
          {mockTest.subjectMarks && mockTest.subjectMarks.length > 0 && (
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-foreground flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Subject Performance
              </h4>
              <div className="space-y-2.5">
                {mockTest.subjectMarks.map((subject, index) => {
                  const subjectPercentage = subject.totalMarks > 0 ? (subject.marksObtained / subject.totalMarks) * 100 : 0;
                  return (
                    <div key={index} className="space-y-1.5">
                      <div className="flex items-center justify-between text-xs">
                        <div className="flex items-center gap-2">
                          <div
                            className="h-2.5 w-2.5 rounded-full flex-shrink-0"
                            style={{ backgroundColor: subject.subjectColor }}
                          />
                          <span className="font-medium text-foreground">
                            {subject.subject}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className={`font-semibold ${getTextColor(subjectPercentage)}`}>
                            {subjectPercentage.toFixed(1)}%
                          </span>
                          <span className="font-mono text-muted-foreground">
                            {subject.marksObtained}/{subject.totalMarks}
                          </span>
                        </div>
                      </div>
                      <div className="relative h-2 w-full rounded-full bg-muted overflow-hidden">
                        <div
                          className="h-full rounded-full transition-all duration-300"
                          style={{
                            backgroundColor: subject.subjectColor,
                            width: `${Math.min(100, subjectPercentage)}%`
                          }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Analysis Status */}
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex items-center gap-2">
              {mockTest.mistakes && mockTest.mistakes.length > 0 ? (
                <>
                  <AlertTriangle className="h-3.5 w-3.5 text-orange-500" />
                  <span className="text-orange-600">{mockTest.mistakes.length} Mistakes</span>
                </>
              ) : (
                <>
                  <CheckCircle className="h-3.5 w-3.5 text-green-500" />
                  <span className="text-green-600">No Mistakes</span>
                </>
              )}
            </div>

            <div className="flex items-center gap-2">
              {mockTest.takeaways && mockTest.takeaways.length > 0 ? (
                <>
                  <Lightbulb className="h-3.5 w-3.5 text-blue-500" />
                  <span className="text-blue-600">{mockTest.takeaways.length} Takeaways</span>
                </>
              ) : (
                <>
                  <Brain className="h-3.5 w-3.5 text-gray-400" />
                  <span className="text-muted-foreground">No Takeaways</span>
                </>
              )}
            </div>
          </div>
        </CardContent>

        <CardFooter className="p-4 flex justify-between items-center border-t border-border bg-muted/20 backdrop-blur-sm">
          <div className="flex gap-2">
            {onViewDetails && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onViewDetails(mockTest)}
                className="gap-2 text-xs hover:bg-violet-500/10 hover:text-violet-600 transition-colors"
              >
                <Eye className="h-3.5 w-3.5" />
                Details
              </Button>
            )}
            {mockTest.testPaperUrl && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.open(mockTest.testPaperUrl, '_blank')}
                className="gap-2 text-xs hover:bg-blue-500/10 hover:text-blue-600 transition-colors"
              >
                <ExternalLink className="h-3.5 w-3.5" />
                Paper
              </Button>
            )}
          </div>

          <div className="flex items-center gap-2">
            {/* Quick Stats */}
            <div className="flex items-center gap-3 text-xs text-muted-foreground mr-2">
              {mockTest.analysisCompleted && (
                <div className="flex items-center gap-1">
                  <Zap className="h-3 w-3 text-emerald-500" />
                  <span className="text-emerald-600">Analyzed</span>
                </div>
              )}
              {mockTest.status && (
                <Badge
                  variant="outline"
                  className={cn(
                    "text-xs px-2 py-0.5",
                    mockTest.status === 'completed' && "border-green-500/30 text-green-600",
                    mockTest.status === 'missed' && "border-red-500/30 text-red-600",
                    mockTest.status === 'in_progress' && "border-blue-500/30 text-blue-600"
                  )}
                >
                  {mockTest.status.replace('_', ' ')}
                </Badge>
              )}
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 hover:bg-muted/50 transition-colors"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem
                  onClick={() => onEditClick(mockTest)}
                  className="gap-2"
                >
                  <Pencil className="h-4 w-4" />
                  Edit Test
                </DropdownMenuItem>
                {!mockTest.analysisCompleted && (
                  <DropdownMenuItem
                    onClick={() => onViewDetails?.(mockTest)}
                    className="gap-2"
                  >
                    <BarChart3 className="h-4 w-4" />
                    Complete Analysis
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem
                  onClick={() => onDeleteClick(mockTest.id)}
                  className="text-destructive gap-2"
                >
                  <Trash className="h-4 w-4" />
                  Delete Test
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardFooter>
      </Card>
    </motion.div>
  );
}