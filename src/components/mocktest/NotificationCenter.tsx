import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  Bell, 
  BellRing, 
  X, 
  AlertTriangle, 
  CheckCircle, 
  Lightbulb,
  Target,
  Clock,
  MoreHorizontal
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  <PERSON>et,
  Sheet<PERSON>ontent,
  Sheet<PERSON>eader,
  Sheet<PERSON>itle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
// Note: ScrollArea component not available, using div with overflow
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { DDayExam } from "@/utils/mockTestLocalStorage";
import { 
  ExamNotificationSystem, 
  ExamNotification 
} from "@/utils/examNotifications";
import { cn } from "@/lib/utils";
import { formatDistanceToNow } from "date-fns";

interface NotificationCenterProps {
  exams: DDayExam[];
}

export function NotificationCenter({ exams }: NotificationCenterProps) {
  const { user } = useSupabaseAuth();
  const [notifications, setNotifications] = useState<ExamNotification[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  const notificationSystem = ExamNotificationSystem.getInstance();

  useEffect(() => {
    if (!user?.id) return;

    // Generate notifications for all exams
    exams.forEach(exam => {
      const examNotifications = notificationSystem.generateNotifications(exam);
      examNotifications.forEach(notification => {
        notificationSystem.addNotification(user.id, notification);
      });
    });

    // Load notifications
    const userNotifications = notificationSystem.getNotifications(user.id);
    setNotifications(userNotifications);
    setUnreadCount(notificationSystem.getUnreadCount(user.id));
  }, [exams, user?.id]);

  const handleMarkAsRead = (notificationId: string) => {
    if (!user?.id) return;
    
    notificationSystem.markAsRead(user.id, notificationId);
    setNotifications(prev => 
      prev.map(n => n.id === notificationId ? { ...n, isRead: true } : n)
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const handleMarkAllAsRead = () => {
    if (!user?.id) return;
    
    notifications.forEach(notification => {
      if (!notification.isRead) {
        notificationSystem.markAsRead(user.id, notification.id);
      }
    });
    
    setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
    setUnreadCount(0);
  };

  const getNotificationIcon = (type: ExamNotification['type']) => {
    switch (type) {
      case 'reminder': return Clock;
      case 'preparation': return Target;
      case 'urgent': return AlertTriangle;
      case 'motivation': return CheckCircle;
      default: return Bell;
    }
  };

  const getNotificationColor = (type: ExamNotification['type'], priority: ExamNotification['priority']) => {
    if (type === 'urgent' || priority === 'critical') {
      return 'border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-950/20';
    }
    if (type === 'preparation' || priority === 'high') {
      return 'border-orange-200 dark:border-orange-800 bg-orange-50 dark:bg-orange-950/20';
    }
    if (type === 'motivation') {
      return 'border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-950/20';
    }
    return 'border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-950/20';
  };

  const sortedNotifications = notifications.sort((a, b) => {
    // Unread first, then by priority, then by date
    if (a.isRead !== b.isRead) return a.isRead ? 1 : -1;
    
    const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
    const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
    if (priorityDiff !== 0) return priorityDiff;
    
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button 
          variant="outline" 
          size="sm" 
          className="relative"
        >
          <motion.div
            animate={unreadCount > 0 ? { scale: [1, 1.1, 1] } : { scale: 1 }}
            transition={{ duration: 0.5, repeat: unreadCount > 0 ? Infinity : 0, repeatDelay: 2 }}
          >
            {unreadCount > 0 ? (
              <BellRing className="h-4 w-4" />
            ) : (
              <Bell className="h-4 w-4" />
            )}
          </motion.div>
          
          {unreadCount > 0 && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="absolute -top-2 -right-2"
            >
              <Badge 
                variant="destructive" 
                className="h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
              >
                {unreadCount > 99 ? '99+' : unreadCount}
              </Badge>
            </motion.div>
          )}
        </Button>
      </SheetTrigger>
      
      <SheetContent className="w-full sm:max-w-md">
        <SheetHeader>
          <div className="flex items-center justify-between">
            <SheetTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Notifications
              {unreadCount > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {unreadCount} new
                </Badge>
              )}
            </SheetTitle>
            
            {unreadCount > 0 && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={handleMarkAllAsRead}
                className="text-xs"
              >
                Mark all read
              </Button>
            )}
          </div>
        </SheetHeader>

        <div className="h-[calc(100vh-120px)] mt-6 overflow-y-auto">
          {sortedNotifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Bell className="h-16 w-16 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No notifications</h3>
              <p className="text-muted-foreground text-center text-sm">
                You're all caught up! Notifications will appear here when you have upcoming exams.
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              <AnimatePresence>
                {sortedNotifications.map((notification, index) => {
                  const Icon = getNotificationIcon(notification.type);
                  const colorClasses = getNotificationColor(notification.type, notification.priority);
                  
                  return (
                    <motion.div
                      key={notification.id}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ delay: index * 0.05 }}
                      className={cn(
                        "p-4 rounded-lg border transition-all duration-200",
                        colorClasses,
                        !notification.isRead && "ring-2 ring-violet-200 dark:ring-violet-800"
                      )}
                    >
                      <div className="flex items-start gap-3">
                        <div className={cn(
                          "p-2 rounded-full flex-shrink-0",
                          notification.type === 'urgent' && "bg-red-100 dark:bg-red-900/30",
                          notification.type === 'preparation' && "bg-orange-100 dark:bg-orange-900/30",
                          notification.type === 'motivation' && "bg-green-100 dark:bg-green-900/30",
                          notification.type === 'reminder' && "bg-blue-100 dark:bg-blue-900/30"
                        )}>
                          <Icon className={cn(
                            "h-4 w-4",
                            notification.type === 'urgent' && "text-red-600 dark:text-red-400",
                            notification.type === 'preparation' && "text-orange-600 dark:text-orange-400",
                            notification.type === 'motivation' && "text-green-600 dark:text-green-400",
                            notification.type === 'reminder' && "text-blue-600 dark:text-blue-400"
                          )} />
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between mb-1">
                            <h4 className="font-semibold text-sm truncate">
                              {notification.title}
                            </h4>
                            
                            <div className="flex items-center gap-1 ml-2">
                              {!notification.isRead && (
                                <div className="w-2 h-2 bg-violet-600 rounded-full flex-shrink-0" />
                              )}
                              
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                    <MoreHorizontal className="h-3 w-3" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  {!notification.isRead && (
                                    <DropdownMenuItem onClick={() => handleMarkAsRead(notification.id)}>
                                      <CheckCircle className="h-4 w-4 mr-2" />
                                      Mark as read
                                    </DropdownMenuItem>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                          
                          <p className="text-sm text-muted-foreground mb-2">
                            {notification.message}
                          </p>
                          
                          <div className="flex items-center justify-between">
                            <Badge 
                              variant="outline" 
                              className={cn(
                                "text-xs",
                                notification.priority === 'critical' && "border-red-300 text-red-700 dark:border-red-700 dark:text-red-400",
                                notification.priority === 'high' && "border-orange-300 text-orange-700 dark:border-orange-700 dark:text-orange-400",
                                notification.priority === 'medium' && "border-yellow-300 text-yellow-700 dark:border-yellow-700 dark:text-yellow-400",
                                notification.priority === 'low' && "border-green-300 text-green-700 dark:border-green-700 dark:text-green-400"
                              )}
                            >
                              {notification.priority}
                            </Badge>
                            
                            <span className="text-xs text-muted-foreground">
                              {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                            </span>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  );
                })}
              </AnimatePresence>
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
