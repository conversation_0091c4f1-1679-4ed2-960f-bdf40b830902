# Advanced Task Management System - Implementation Guide

## 🎯 Project Overview

You are tasked with completely rebuilding the IsotopeAI Tasks page (`/src/pages/Tasks.tsx`) to create a comprehensive, exam-focused task management system for students preparing for competitive exams (JEE, NEET, UPSC, CBSE, SAT, etc.).

## 📊 Current State Analysis

### ✅ What's Working
- Basic Supabase integration with CRUD operations
- Real-time subscriptions for task updates
- Drag-and-drop Kanban board functionality
- Basic task creation, editing, and deletion
- Existing database schema with `todos`, `userSubjects`, and `exams` tables

### ❌ What's Broken/Missing
- Limited task management features (no subtasks, no subject integration)
- Outdated UI that doesn't follow IsotopeAI design language
- No exam-specific features or deadline management
- Missing analytics and progress tracking
- No mobile optimization for student use cases
- No advanced search and filtering capabilities

### 🗄️ Database Schema Strategy
**Minimal Database Changes - Add New Columns Only**

```sql
-- Extend existing todos table with new columns (no data migration)
ALTER TABLE todos ADD COLUMN IF NOT EXISTS parentId text;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS subjectId text REFERENCES userSubjects(id);
ALTER TABLE todos ADD COLUMN IF NOT EXISTS examId text REFERENCES exams(id);
ALTER TABLE todos ADD COLUMN IF NOT EXISTS tags jsonb DEFAULT '[]';
ALTER TABLE todos ADD COLUMN IF NOT EXISTS chapterTags jsonb DEFAULT '[]';
ALTER TABLE todos ADD COLUMN IF NOT EXISTS difficultyLevel text DEFAULT 'medium';
ALTER TABLE todos ADD COLUMN IF NOT EXISTS timeEstimate integer; -- in minutes
ALTER TABLE todos ADD COLUMN IF NOT EXISTS actualTimeSpent integer; -- in minutes
ALTER TABLE todos ADD COLUMN IF NOT EXISTS completionPercentage integer DEFAULT 0;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS notes text;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS viewCount integer DEFAULT 0;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS lastViewed bigint;

-- Existing tables remain unchanged
-- userSubjects table (existing) - no changes needed
-- exams table (existing) - no changes needed
```

**Benefits of This Approach:**
- ✅ No data migration required
- ✅ Existing tasks continue working immediately
- ✅ New columns have default values for backward compatibility
- ✅ Foreign key relationships for data integrity
- ✅ Minimal database downtime

## 🚀 Implementation Strategy

### Phase 1: Foundation (Start Here)
**Goal:** Set up enhanced data models and maintain backward compatibility

#### 1.1 Database Schema Extension
Add new columns to existing todos table:

```sql
-- Execute these SQL commands to extend the existing table
ALTER TABLE todos ADD COLUMN IF NOT EXISTS parentId text;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS subjectId text REFERENCES userSubjects(id);
ALTER TABLE todos ADD COLUMN IF NOT EXISTS examId text REFERENCES exams(id);
ALTER TABLE todos ADD COLUMN IF NOT EXISTS tags jsonb DEFAULT '[]';
ALTER TABLE todos ADD COLUMN IF NOT EXISTS chapterTags jsonb DEFAULT '[]';
ALTER TABLE todos ADD COLUMN IF NOT EXISTS difficultyLevel text DEFAULT 'medium';
ALTER TABLE todos ADD COLUMN IF NOT EXISTS timeEstimate integer;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS actualTimeSpent integer;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS completionPercentage integer DEFAULT 0;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS notes text;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS viewCount integer DEFAULT 0;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS lastViewed bigint;
```

#### 1.2 Enhanced Type Definitions
Update TypeScript interfaces to include new columns:

```typescript
// Enhanced TodoItem interface (extends existing with new database columns)
interface EnhancedTodoItem extends TodoItem {
  // New database columns
  parentId?: string; // For hierarchical tasks
  subjectId?: string; // Foreign key to userSubjects
  examId?: string; // Foreign key to exams
  tags: string[]; // Stored as jsonb in database
  chapterTags: string[]; // Stored as jsonb in database
  difficultyLevel: 'easy' | 'medium' | 'hard';
  timeEstimate?: number; // in minutes
  actualTimeSpent?: number; // in minutes
  completionPercentage: number; // 0-100
  notes?: string;
  viewCount: number;
  lastViewed?: number;

  // Computed fields (calculated on client, not stored)
  depth?: number; // Calculated from parentId chain
  subtasks?: string[]; // Calculated by querying children
  subjectColor?: string; // Fetched from userSubjects
  subjectName?: string; // Fetched from userSubjects
  examName?: string; // Fetched from exams
  examDate?: number; // Fetched from exams
  isOverdue?: boolean; // Calculated from dueDate
  hasSubtasks?: boolean; // Calculated from children count
}

// Supabase row type for new enhanced todos table
interface EnhancedTodoRow {
  id: string;
  title: string;
  description: string; // Keep as plain text
  priority: 'low' | 'medium' | 'high';
  dueDate?: number;
  createdBy: string;
  createdAt: number;
  updatedAt: number;
  column_id: string;
  columnId: string;
  assignedTo?: string;
  assignedToName?: string;
  assignedToPhotoUrl?: string;
  groupId?: string;
  // New columns
  parentId?: string;
  subjectId?: string;
  examId?: string;
  tags: string[];
  chapterTags: string[];
  difficultyLevel: 'easy' | 'medium' | 'hard';
  timeEstimate?: number;
  actualTimeSpent?: number;
  completionPercentage: number;
  notes?: string;
  viewCount: number;
  lastViewed?: number;
}
```

#### 1.3 Backward Compatibility Strategy
Ensure existing tasks work with new columns:

```typescript
// Utility functions for handling existing tasks
const normalizeTask = (task: Partial<EnhancedTodoRow>): EnhancedTodoRow => {
  return {
    ...task,
    // Provide defaults for new columns
    parentId: task.parentId || null,
    subjectId: task.subjectId || null,
    examId: task.examId || null,
    tags: task.tags || [],
    chapterTags: task.chapterTags || [],
    difficultyLevel: task.difficultyLevel || 'medium',
    timeEstimate: task.timeEstimate || null,
    actualTimeSpent: task.actualTimeSpent || null,
    completionPercentage: task.completionPercentage || 0,
    notes: task.notes || null,
    viewCount: task.viewCount || 0,
    lastViewed: task.lastViewed || null,
  } as EnhancedTodoRow;
};
```

### Phase 2: Core Rebuild (Next Steps)
**Goal:** Replace existing TodoBoard with enhanced version

#### 2.1 Enhanced Supabase Store
Extend `useSupabaseTodoStore` with:
- Hierarchical task support
- Subject integration
- Enhanced error handling with retry logic
- Offline queue for failed operations

#### 2.2 New Component Architecture
```
EnhancedTaskManagement/
├── TaskHeader/
│   ├── SearchBar.tsx
│   ├── FilterPanel.tsx
│   └── ViewModeToggle.tsx
├── TaskViews/
│   ├── KanbanView.tsx
│   ├── TableView.tsx
│   └── CalendarView.tsx
├── TaskComponents/
│   ├── EnhancedTaskCard.tsx
│   ├── TaskCreationModal.tsx
│   └── SubtaskManager.tsx
└── TaskAnalytics/
    ├── ProgressDashboard.tsx
    └── SubjectAnalytics.tsx
```

## 🎨 Design Requirements

### IsotopeAI Design Language
- **Background:** `bg-[#030303]` (dark theme)
- **Colors:** violet/purple/rose/emerald palette
- **Typography:** `font-onest`
- **Animations:** Framer Motion with smooth transitions
- **Mobile-first:** Responsive design for student phone usage

### Key UI Components
1. **Enhanced Task Cards:** Subject color coding, priority indicators, progress bars
2. **Hierarchical Display:** Expandable subtask trees with indentation
3. **Smart Filters:** Multi-criteria filtering with saved presets
4. **Mobile Optimization:** Touch-friendly interactions, swipe gestures

## 📱 Mobile-First Considerations
- Touch-friendly tap targets (44px minimum)
- Swipe gestures for task actions
- Bottom sheet modals for mobile
- Collapsible navigation
- Optimized for phone usage during lectures

## 🔧 Technical Implementation Notes

### State Management
- Extend existing Zustand store pattern
- Implement optimistic updates
- Add offline capability with sync queue
- Cache computed hierarchies and analytics

### Performance Optimization
- Virtual scrolling for 1000+ tasks
- Lazy loading of task details
- Memoized calculations for hierarchy
- Debounced search (300ms)

### Integration Points
- Link with existing study timer data
- Sync with analytics dashboard
- Connect to mock test results
- Integrate with exam scheduling

## 🧪 Testing Strategy
- Unit tests for all new components
- Integration tests for Supabase operations
- E2E tests for critical user workflows
- Accessibility compliance testing
- Performance testing with large datasets

## 📋 Immediate Next Steps

1. **Start with Phase 1.1:** Add new columns to existing Supabase todos table
2. **Phase 1.2:** Update TypeScript interfaces to include new columns
3. **Phase 1.3:** Create backward compatibility utilities
4. **Test with existing data:** Ensure current tasks continue to work with new columns

### Database Migration Commands
```sql
-- Run these commands in Supabase SQL editor
ALTER TABLE todos ADD COLUMN IF NOT EXISTS parentId text;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS subjectId text REFERENCES userSubjects(id);
ALTER TABLE todos ADD COLUMN IF NOT EXISTS examId text REFERENCES exams(id);
ALTER TABLE todos ADD COLUMN IF NOT EXISTS tags jsonb DEFAULT '[]';
ALTER TABLE todos ADD COLUMN IF NOT EXISTS chapterTags jsonb DEFAULT '[]';
ALTER TABLE todos ADD COLUMN IF NOT EXISTS difficultyLevel text DEFAULT 'medium';
ALTER TABLE todos ADD COLUMN IF NOT EXISTS timeEstimate integer;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS actualTimeSpent integer;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS completionPercentage integer DEFAULT 0;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS notes text;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS viewCount integer DEFAULT 0;
ALTER TABLE todos ADD COLUMN IF NOT EXISTS lastViewed bigint;
```

## 🎯 Success Criteria
- All existing functionality preserved (no data migration required)
- New columns added successfully with default values
- Existing tasks continue working immediately
- New hierarchical task management working
- Subject integration with color coding via subjectId foreign key
- Mobile-responsive design
- Performance with 1000+ tasks
- Comprehensive analytics dashboard
- Exam-specific features operational

---

**Ready to start? Begin with adding the new columns to your Supabase todos table, then update the TypeScript interfaces. This approach ensures zero downtime and immediate backward compatibility.**
