import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { TaskHeader } from './TaskHeader';
import { EnhancedKanbanView } from './EnhancedKanbanView';
import { EnhancedTableView } from './EnhancedTableView';
import { EnhancedCalendarView } from './EnhancedCalendarView';
import { BulkOperationsToolbar } from './BulkOperationsToolbar';
import { TaskGamificationSystem } from './gamification/TaskGamificationSystem';
import { TaskRecommendationEngine } from './ai/TaskRecommendationEngine';
import { KeyboardNavigationProvider } from './accessibility/KeyboardNavigation';
import { ScreenReaderProvider } from './accessibility/ScreenReaderSupport';
import { PullToRefresh } from './mobile/BottomSheetModal';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  AlertTriangle,
  RefreshCw,
  Wifi,
  WifiOff,
  BarChart3,
} from 'lucide-react';

interface EnhancedTaskManagementProps {
  className?: string;
}

export function EnhancedTaskManagement({ className = '' }: EnhancedTaskManagementProps) {
  const { user } = useSupabaseAuth();
  const store = useEnhancedTodoStore();

  // Destructure with fallbacks
  const {
    fetchTodos,
    fetchSubjects,
    subscribeToUpdates,
    loading = { tasks: false, subjects: false, exams: false, analytics: false },
    error = {},
    viewMode = 'kanban',
    selectedTasks = [],
    clearSelection,
    reset,
  } = store || {};

  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showAnalytics, setShowAnalytics] = useState(false);

  // Debug logging
  useEffect(() => {
    console.log('EnhancedTaskManagement render:', {
      user: user?.id,
      loading,
      error,
      viewMode,
      selectedTasksCount: selectedTasks?.length,
      storeExists: !!store
    });
  });

  // Handle online/offline status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Initialize data and subscriptions
  useEffect(() => {
    if (!user) {
      reset();
      return;
    }

    const initializeData = async () => {
      try {
        console.log('Initializing enhanced task management for user:', user.id);
        // Fetch all data in parallel
        await Promise.all([
          fetchTodos(user.id).catch(err => {
            console.error('Failed to fetch todos:', err);
            return [];
          }),
          fetchSubjects(user.id).catch(err => {
            console.error('Failed to fetch subjects:', err);
            return [];
          }),
        ]);
        console.log('Enhanced task management initialized successfully');
      } catch (error) {
        console.error('Failed to initialize enhanced task management:', error);
      }
    };

    initializeData();

    // Set up real-time subscription
    let unsubscribe: (() => void) | undefined;
    try {
      unsubscribe = subscribeToUpdates(user.id);
    } catch (error) {
      console.error('Failed to set up real-time subscription:', error);
    }

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [user, fetchTodos, fetchSubjects, subscribeToUpdates, reset]);

  // Handle refresh functionality
  const handleRefresh = async () => {
    if (!user?.id) return;

    setIsRefreshing(true);
    try {
      await Promise.all([
        fetchTodos(user.id),
        fetchSubjects(user.id),
      ]);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Handle keyboard actions
  const handleKeyboardAction = (action: string, focusedElement?: string) => {
    switch (action) {
      case 'create_task':
        // Trigger task creation modal
        break;
      case 'search':
        // Focus search input
        break;
      case 'toggle_view':
        // Toggle between view modes
        break;
      case 'complete_task':
        if (focusedElement) {
          // Complete the focused task
        }
        break;
      case 'edit_task':
        if (focusedElement) {
          // Edit the focused task
        }
        break;
      case 'delete_task':
        if (focusedElement) {
          // Delete the focused task
        }
        break;
    }
  };

  // Handle retry
  const handleRetry = async () => {
    if (!user) return;

    try {
      await Promise.all([
        fetchTodos(user.id),
        fetchSubjects(user.id),
      ]);
    } catch (error) {
      console.error('Retry failed:', error);
    }
  };

  // Loading state
  if (loading.tasks || loading.subjects || loading.exams) {
    return (
      <div className={`space-y-6 ${className}`}>
        {/* Header skeleton */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <Skeleton className="h-8 w-48 bg-gray-200 dark:bg-gray-800" />
              <Skeleton className="h-4 w-32 bg-gray-200 dark:bg-gray-800" />
            </div>
            <div className="flex gap-2">
              <Skeleton className="h-9 w-24 bg-gray-200 dark:bg-gray-800" />
              <Skeleton className="h-9 w-28 bg-gray-200 dark:bg-gray-800" />
            </div>
          </div>

          {/* Search skeleton */}
          <Skeleton className="h-10 w-full bg-gray-200 dark:bg-gray-800" />

          {/* View toggle skeleton */}
          <div className="flex items-center justify-between">
            <Skeleton className="h-8 w-48 bg-gray-200 dark:bg-gray-800" />
          </div>
        </div>

        {/* Content skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="space-y-4">
              <Skeleton className="h-32 w-full bg-gray-200 dark:bg-gray-800" />
              <Skeleton className="h-24 w-full bg-gray-200 dark:bg-gray-800" />
              <Skeleton className="h-28 w-full bg-gray-200 dark:bg-gray-800" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error.tasks || error.subjects || error.exams) {
    const errorMessage = error.tasks || error.subjects || error.exams;

    return (
      <div className={`space-y-6 ${className}`}>
        <Alert className="border-red-500/50 bg-red-50/80 dark:bg-red-950/20">
          <AlertTriangle className="h-4 w-4 text-red-600 dark:text-red-400" />
          <AlertDescription className="text-red-700 dark:text-red-300">
            <div className="flex items-center justify-between">
              <span>Failed to load task data: {errorMessage}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRetry}
                className="border-red-500/50 text-red-600 dark:text-red-400 hover:bg-red-500/20"
              >
                <RefreshCw className="h-3 w-3 mr-1" />
                Retry
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // No user state
  if (!user) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="text-center space-y-4">
          <AlertTriangle className="h-12 w-12 text-amber-600 dark:text-amber-400 mx-auto" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Authentication Required</h3>
            <p className="text-gray-600 dark:text-gray-400">Please sign in to access task management.</p>
          </div>
        </div>
      </div>
    );
  }

  // Store not available
  if (!store || !fetchTodos || !fetchSubjects) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="text-center space-y-4">
          <AlertTriangle className="h-12 w-12 text-red-600 dark:text-red-400 mx-auto" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Store Initialization Error</h3>
            <p className="text-gray-600 dark:text-gray-400">Task management store is not available. Please refresh the page.</p>
          </div>
        </div>
      </div>
    );
  }

  // Main content animations
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      }
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <ScreenReaderProvider>
      <KeyboardNavigationProvider onAction={handleKeyboardAction}>
        <PullToRefresh onRefresh={handleRefresh}>
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className={`space-y-4 md:space-y-6 ${className} touch-manipulation`}
          >
            {/* Online/Offline Indicator */}
            <AnimatePresence>
              {!isOnline && (
                <motion.div
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                >
                  <Alert className="border-amber-500/50 bg-amber-50/80 dark:bg-amber-950/20">
                    <WifiOff className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                    <AlertDescription className="text-amber-700 dark:text-amber-300">
                      You're currently offline. Changes will be synced when connection is restored.
                    </AlertDescription>
                  </Alert>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Task Header */}
            <motion.div variants={itemVariants}>
              <TaskHeader />
            </motion.div>

            {/* AI Recommendations */}
            <motion.div variants={itemVariants}>
              <TaskRecommendationEngine />
            </motion.div>

            {/* Main Content Area */}
            <motion.div variants={itemVariants} className="min-h-[600px]">
              <AnimatePresence mode="wait">
                {viewMode === 'kanban' && (
                  <motion.div
                    key="kanban"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <EnhancedKanbanView />
                  </motion.div>
                )}

                {viewMode === 'table' && (
                  <motion.div
                    key="table"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <EnhancedTableView />
                  </motion.div>
                )}

                {viewMode === 'calendar' && (
                  <motion.div
                    key="calendar"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <EnhancedCalendarView />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>

            {/* Gamification System - Moved to bottom */}
            <motion.div variants={itemVariants}>
              <TaskGamificationSystem />
            </motion.div>

            {/* Analytics Modal/Panel */}
            <AnimatePresence>
              {showAnalytics && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
                  onClick={() => setShowAnalytics(false)}
                >
                  <motion.div
                    initial={{ scale: 0.9, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0.9, opacity: 0 }}
                    className="bg-white/95 dark:bg-[#030303]/95 backdrop-blur-md border border-gray-200 dark:border-gray-800 rounded-lg p-6 max-w-4xl w-full max-h-[80vh] overflow-y-auto"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <div className="flex items-center justify-between mb-6">
                      <h2 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center gap-2">
                        <BarChart3 className="h-5 w-5 text-violet-600 dark:text-violet-400" />
                        Task Analytics
                      </h2>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowAnalytics(false)}
                        className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white"
                      >
                        ✕
                      </Button>
                    </div>

                    <div className="text-center py-12">
                      <div className="text-6xl mb-4">📊</div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Analytics Dashboard</h3>
                      <p className="text-gray-600 dark:text-gray-400">Comprehensive analytics coming soon!</p>
                    </div>
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Connection Status Indicator */}
            <div className="fixed bottom-4 right-4 z-40">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className={`
            flex items-center gap-2 px-3 py-2 rounded-full text-xs font-medium
            ${isOnline
                    ? 'bg-emerald-500/20 text-emerald-400 border border-emerald-500/30'
                    : 'bg-red-500/20 text-red-400 border border-red-500/30'
                  }
            backdrop-blur-md
          `}
              >
                {isOnline ? (
                  <>
                    <Wifi className="h-3 w-3" />
                    Online
                  </>
                ) : (
                  <>
                    <WifiOff className="h-3 w-3" />
                    Offline
                  </>
                )}
              </motion.div>
            </div>

            {/* Bulk Operations Toolbar */}
            <BulkOperationsToolbar
              selectedTasks={selectedTasks}
              onClearSelection={clearSelection}
            />

            {/* Accessibility Toolbar */}
          </motion.div>
        </PullToRefresh>
      </KeyboardNavigationProvider>
    </ScreenReaderProvider>
  );
}
