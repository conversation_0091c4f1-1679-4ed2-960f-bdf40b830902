import {
  MockTest,
  UpcomingTest,
  UnifiedTestData,
  UnifiedSyllabus,
  UnifiedMistake,
  UnifiedTakeaway,
  TestDataValidation,
  StandardTestFormData,
  SubjectMarks
} from '@/types/mockTest';

export interface ValidationRule<T> {
  field: keyof T;
  validator: (value: any, data: T) => string | null;
  severity: 'error' | 'warning';
}

export interface ValidationContext {
  mode: 'create' | 'edit';
  testType: 'upcoming' | 'completed';
  userId: string;
}

/**
 * Comprehensive validation system for mock test data
 */
export class TestDataValidator {
  private static instance: TestDataValidator;

  static getInstance(): TestDataValidator {
    if (!TestDataValidator.instance) {
      TestDataValidator.instance = new TestDataValidator();
    }
    return TestDataValidator.instance;
  }

  // ============================================================================
  // CORE VALIDATION METHODS
  // ============================================================================

  validateUnifiedTestData(data: Partial<UnifiedTestData>, context: ValidationContext): TestDataValidation {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Basic field validation
    this.validateBasicFields(data, errors, warnings);
    
    // Date and time validation
    this.validateDateTimeFields(data, errors, warnings);
    
    // Syllabus validation
    this.validateSyllabus(data.syllabus, errors, warnings);
    
    // Mistakes validation
    this.validateMistakes(data.mistakes, errors, warnings);
    
    // Takeaways validation
    this.validateTakeaways(data.takeaways, errors, warnings);
    
    // Context-specific validation
    this.validateByContext(data, context, errors, warnings);

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  validateMockTest(mockTest: Partial<MockTest>, context: ValidationContext): TestDataValidation {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate unified data
    const unifiedValidation = this.validateUnifiedTestData(mockTest, context);
    errors.push(...unifiedValidation.errors);
    warnings.push(...unifiedValidation.warnings);

    // Mock test specific validation
    this.validateSubjectMarks(mockTest.subjectMarks, errors, warnings);
    this.validatePerformanceData(mockTest, errors, warnings);
    this.validateCompletionStatus(mockTest, errors, warnings);

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  validateUpcomingTest(upcomingTest: Partial<UpcomingTest>, context: ValidationContext): TestDataValidation {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validate unified data
    const unifiedValidation = this.validateUnifiedTestData(upcomingTest, context);
    errors.push(...unifiedValidation.errors);
    warnings.push(...unifiedValidation.warnings);

    // Upcoming test specific validation
    this.validateUpcomingTestFields(upcomingTest, errors, warnings);
    this.validateFutureDate(upcomingTest.date, errors, warnings);

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  validateFormData(formData: StandardTestFormData, context: ValidationContext): TestDataValidation {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Basic form validation
    this.validateFormBasics(formData, errors, warnings);
    
    // Subject inputs validation
    this.validateSubjectInputs(formData.subjectInputs, context.testType, errors, warnings);
    
    // Syllabus validation
    this.validateSyllabusTopics(formData.syllabusTopics, errors, warnings);
    
    // Analysis data validation
    this.validateAnalysisData(formData, errors, warnings);

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  // ============================================================================
  // SPECIFIC VALIDATION METHODS
  // ============================================================================

  private validateBasicFields(data: Partial<UnifiedTestData>, errors: string[], warnings: string[]): void {
    // Required fields
    if (!data.name?.trim()) {
      errors.push('Test name is required');
    } else if (data.name.length > 100) {
      errors.push('Test name must be 100 characters or less');
    }

    if (!data.userId?.trim()) {
      errors.push('User ID is required');
    }

    if (!data.date) {
      errors.push('Test date is required');
    }

    // Optional field validation
    if (data.testPaperUrl && !this.isValidUrl(data.testPaperUrl)) {
      warnings.push('Test paper URL appears to be invalid');
    }

    if (data.duration !== undefined && (data.duration < 1 || data.duration > 600)) {
      warnings.push('Test duration should be between 1 and 600 minutes');
    }

    if (data.totalQuestions !== undefined && (data.totalQuestions < 1 || data.totalQuestions > 1000)) {
      warnings.push('Total questions should be between 1 and 1000');
    }

    if (data.targetScore !== undefined && data.targetScore < 0) {
      errors.push('Target score cannot be negative');
    }

    if (data.confidenceLevel !== undefined && (data.confidenceLevel < 1 || data.confidenceLevel > 5)) {
      errors.push('Confidence level must be between 1 and 5');
    }

    if (data.preparationTime !== undefined && data.preparationTime < 0) {
      warnings.push('Preparation time cannot be negative');
    }
  }

  private validateDateTimeFields(data: Partial<UnifiedTestData>, errors: string[], warnings: string[]): void {
    // Date format validation
    if (data.date && !/^\d{4}-\d{2}-\d{2}$/.test(data.date)) {
      errors.push('Date must be in YYYY-MM-DD format');
    }

    // Time format validation
    if (data.time && !/^\d{2}:\d{2}$/.test(data.time)) {
      errors.push('Time must be in HH:MM format');
    }

    // Date range validation
    if (data.date) {
      const testDate = new Date(data.date);
      const minDate = new Date('2020-01-01');
      const maxDate = new Date();
      maxDate.setFullYear(maxDate.getFullYear() + 5);

      if (testDate < minDate || testDate > maxDate) {
        warnings.push('Test date seems unusual (should be between 2020 and 5 years from now)');
      }
    }
  }

  private validateSyllabus(syllabus: UnifiedSyllabus | undefined, errors: string[], warnings: string[]): void {
    if (!syllabus) return;

    if (!Array.isArray(syllabus.topics)) {
      errors.push('Syllabus topics must be an array');
    } else {
      if (syllabus.topics.length === 0) {
        warnings.push('No syllabus topics specified');
      }

      syllabus.topics.forEach((topic, index) => {
        if (typeof topic !== 'string' || !topic.trim()) {
          errors.push(`Syllabus topic ${index + 1} is invalid`);
        }
      });
    }

    if (syllabus.overallProgress !== undefined) {
      if (typeof syllabus.overallProgress !== 'number' || 
          syllabus.overallProgress < 0 || 
          syllabus.overallProgress > 100) {
        errors.push('Overall progress must be a number between 0 and 100');
      }
    }

    if (syllabus.chapters) {
      syllabus.chapters.forEach((chapter, index) => {
        if (!chapter.name?.trim()) {
          errors.push(`Chapter ${index + 1} name is required`);
        }
        if (!Array.isArray(chapter.topics)) {
          errors.push(`Chapter ${index + 1} topics must be an array`);
        }
        if (chapter.confidence !== undefined && (chapter.confidence < 1 || chapter.confidence > 5)) {
          errors.push(`Chapter ${index + 1} confidence must be between 1 and 5`);
        }
      });
    }
  }

  private validateMistakes(mistakes: UnifiedMistake[] | undefined, errors: string[], warnings: string[]): void {
    if (!mistakes) return;

    if (!Array.isArray(mistakes)) {
      errors.push('Mistakes must be an array');
      return;
    }

    mistakes.forEach((mistake, index) => {
      if (!mistake.description?.trim()) {
        errors.push(`Mistake ${index + 1}: Description is required`);
      }

      if (!mistake.category) {
        errors.push(`Mistake ${index + 1}: Category is required`);
      } else if (!['conceptual', 'calculation', 'silly', 'time_management', 'other'].includes(mistake.category)) {
        errors.push(`Mistake ${index + 1}: Invalid category`);
      }

      if (!mistake.severity || !['low', 'medium', 'high'].includes(mistake.severity)) {
        errors.push(`Mistake ${index + 1}: Invalid severity level`);
      }

      if (!mistake.createdAt) {
        errors.push(`Mistake ${index + 1}: Created date is required`);
      }
    });
  }

  private validateTakeaways(takeaways: UnifiedTakeaway[] | undefined, errors: string[], warnings: string[]): void {
    if (!takeaways) return;

    if (!Array.isArray(takeaways)) {
      errors.push('Takeaways must be an array');
      return;
    }

    takeaways.forEach((takeaway, index) => {
      if (!takeaway.description?.trim()) {
        errors.push(`Takeaway ${index + 1}: Description is required`);
      }

      if (!takeaway.category) {
        errors.push(`Takeaway ${index + 1}: Category is required`);
      } else if (!['strength', 'weakness', 'strategy', 'concept', 'time_management', 'other'].includes(takeaway.category)) {
        errors.push(`Takeaway ${index + 1}: Invalid category`);
      }

      if (!takeaway.priority || !['high', 'medium', 'low'].includes(takeaway.priority)) {
        errors.push(`Takeaway ${index + 1}: Invalid priority level`);
      }

      if (!takeaway.createdAt) {
        errors.push(`Takeaway ${index + 1}: Created date is required`);
      }
    });
  }

  private validateSubjectMarks(subjectMarks: SubjectMarks[] | undefined, errors: string[], warnings: string[]): void {
    if (!subjectMarks) return;

    if (!Array.isArray(subjectMarks)) {
      errors.push('Subject marks must be an array');
      return;
    }

    if (subjectMarks.length === 0) {
      warnings.push('No subject marks provided');
      return;
    }

    subjectMarks.forEach((mark, index) => {
      if (!mark.subject?.trim()) {
        errors.push(`Subject ${index + 1}: Subject name is required`);
      }

      if (typeof mark.marksObtained !== 'number' || mark.marksObtained < 0) {
        errors.push(`Subject ${index + 1}: Invalid marks obtained`);
      }

      if (typeof mark.totalMarks !== 'number' || mark.totalMarks <= 0) {
        errors.push(`Subject ${index + 1}: Invalid total marks`);
      }

      if (mark.marksObtained > mark.totalMarks) {
        errors.push(`Subject ${index + 1}: Marks obtained cannot exceed total marks`);
      }
    });
  }

  private validatePerformanceData(mockTest: Partial<MockTest>, errors: string[], warnings: string[]): void {
    if (mockTest.totalMarksObtained !== undefined && mockTest.totalMarks !== undefined) {
      if (mockTest.totalMarksObtained > mockTest.totalMarks) {
        errors.push('Total marks obtained cannot exceed total marks');
      }
    }

    if (mockTest.timeSpent !== undefined && mockTest.timeSpent < 0) {
      warnings.push('Time spent cannot be negative');
    }

    if (mockTest.stressLevel !== undefined && (mockTest.stressLevel < 1 || mockTest.stressLevel > 5)) {
      errors.push('Stress level must be between 1 and 5');
    }
  }

  private validateCompletionStatus(mockTest: Partial<MockTest>, errors: string[], warnings: string[]): void {
    if (mockTest.status === 'completed') {
      if (!mockTest.completedAt) {
        warnings.push('Completed test should have completion date');
      }

      if (!mockTest.subjectMarks || mockTest.subjectMarks.length === 0) {
        warnings.push('Completed test should have subject marks');
      }
    }
  }

  private validateUpcomingTestFields(upcomingTest: Partial<UpcomingTest>, errors: string[], warnings: string[]): void {
    if (upcomingTest.daysLeft !== undefined && upcomingTest.daysLeft < 0) {
      warnings.push('Test appears to be overdue');
    }

    if (upcomingTest.isNotificationEnabled === undefined) {
      warnings.push('Notification preference not specified');
    }
  }

  private validateFutureDate(date: string | undefined, errors: string[], warnings: string[]): void {
    if (!date) return;

    const testDate = new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (testDate < today) {
      warnings.push('Test date is in the past');
    }
  }

  private validateFormBasics(formData: StandardTestFormData, errors: string[], warnings: string[]): void {
    if (!formData.name?.trim()) {
      errors.push('Test name is required');
    }

    if (!formData.date) {
      errors.push('Test date is required');
    }

    if (!formData.testType) {
      errors.push('Test type is required');
    }
  }

  private validateSubjectInputs(
    subjectInputs: any[], 
    testType: 'upcoming' | 'completed', 
    errors: string[], 
    warnings: string[]
  ): void {
    if (testType === 'completed') {
      const hasValidSubject = subjectInputs.some(input =>
        input.subjectId?.trim() && input.marksObtained && input.totalMarks
      );

      if (!hasValidSubject) {
        errors.push('At least one subject with marks is required for completed tests');
      }
    }

    subjectInputs.forEach((input, index) => {
      if (input.subjectId?.trim() && input.marksObtained && input.totalMarks) {
        const obtained = parseFloat(input.marksObtained);
        const total = parseFloat(input.totalMarks);

        if (isNaN(obtained) || obtained < 0) {
          errors.push(`Subject ${index + 1}: Invalid marks obtained`);
        }

        if (isNaN(total) || total <= 0) {
          errors.push(`Subject ${index + 1}: Invalid total marks`);
        }

        if (obtained > total) {
          errors.push(`Subject ${index + 1}: Marks obtained cannot exceed total marks`);
        }
      }
    });
  }

  private validateSyllabusTopics(topics: string[], errors: string[], warnings: string[]): void {
    if (topics.length === 0) {
      warnings.push('No syllabus topics specified');
    }

    topics.forEach((topic, index) => {
      if (!topic.trim()) {
        errors.push(`Syllabus topic ${index + 1} cannot be empty`);
      }
    });
  }

  private validateAnalysisData(formData: StandardTestFormData, errors: string[], warnings: string[]): void {
    // Validate study materials
    formData.studyMaterials.forEach((material, index) => {
      if (!material.trim()) {
        errors.push(`Study material ${index + 1} cannot be empty`);
      }
    });

    // Validate mistakes
    formData.mistakes.forEach((mistake, index) => {
      if (!mistake.description?.trim()) {
        errors.push(`Mistake ${index + 1}: Description is required`);
      }
    });

    // Validate takeaways
    formData.takeaways.forEach((takeaway, index) => {
      if (!takeaway.description?.trim()) {
        errors.push(`Takeaway ${index + 1}: Description is required`);
      }
    });
  }

  private validateByContext(
    data: Partial<UnifiedTestData>, 
    context: ValidationContext, 
    errors: string[], 
    warnings: string[]
  ): void {
    // Mode-specific validation
    if (context.mode === 'create' && !data.createdAt) {
      warnings.push('Created date will be set automatically');
    }

    // Test type specific validation (status is not part of UnifiedTestData)
    // Status validation is handled at the MockTest/UpcomingTest level
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
}

// Export singleton instance
export const testDataValidator = TestDataValidator.getInstance();
