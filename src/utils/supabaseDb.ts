import { supabase } from '../integrations/supabase/client';
import { ensureAuthenticated } from './supabase';

export async function updateUserLastActive(userId: string) {
  try {
    // Ensure user is authenticated
    const session = await ensureAuthenticated();
    console.log('Updating last active for user:', userId, 'with session:', session.user.id);

    const { data, error } = await supabase
      .from('users')
      .update({ last_login: new Date().toISOString() })
      .eq('id', userId);

    if (error) {
      console.error('Error updating user last active:', error);
      throw error;
    }
    return data;
  } catch (error) {
    console.error('Error in updateUserLastActive:', error);
    // Don't throw error for lastActive updates to avoid disrupting user experience
    return null;
  }
}
