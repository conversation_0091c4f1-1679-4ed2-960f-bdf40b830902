import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { EnhancedTodoItem } from '@/types/todo';
import {
  CheckCircle2,
  Trash2,
  MoreHorizontal,
  X,
  Flag,
  BookOpen,
  Calendar,
  Tag,
  Archive,
  Copy,
  Move,
  Loader2,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface BulkOperationsToolbarProps {
  selectedTasks: string[];
  onClearSelection: () => void;
}

export function BulkOperationsToolbar({ selectedTasks, onClearSelection }: BulkOperationsToolbarProps) {
  const {
    board,
    subjects,
    presetExams,
    updateTask,
    deleteTask,
    bulkUpdateTasks,
    clearSelection,
  } = useEnhancedTodoStore();

  const [isLoading, setIsLoading] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [bulkAction, setBulkAction] = useState<string>('');

  // Get selected task objects
  const selectedTaskObjects = selectedTasks.map(id => board.tasks[id]).filter(Boolean);

  // Bulk operation handlers
  const handleBulkComplete = useCallback(async () => {
    setIsLoading(true);
    setLoadingProgress(0);
    
    try {
      const updates = selectedTasks.map(taskId => ({
        id: taskId,
        updates: { completionPercentage: 100 }
      }));

      await bulkUpdateTasks(updates, (progress) => {
        setLoadingProgress(progress);
      });

      clearSelection();
    } catch (error) {
      console.error('Failed to complete tasks:', error);
    } finally {
      setIsLoading(false);
      setLoadingProgress(0);
    }
  }, [selectedTasks, bulkUpdateTasks, clearSelection]);

  const handleBulkDelete = useCallback(async () => {
    setIsLoading(true);
    setLoadingProgress(0);
    
    try {
      for (let i = 0; i < selectedTasks.length; i++) {
        await deleteTask(selectedTasks[i]);
        setLoadingProgress(((i + 1) / selectedTasks.length) * 100);
      }

      clearSelection();
      setShowDeleteDialog(false);
    } catch (error) {
      console.error('Failed to delete tasks:', error);
    } finally {
      setIsLoading(false);
      setLoadingProgress(0);
    }
  }, [selectedTasks, deleteTask, clearSelection]);

  const handleBulkPriorityChange = useCallback(async (priority: 'low' | 'medium' | 'high') => {
    setIsLoading(true);
    setLoadingProgress(0);
    
    try {
      const updates = selectedTasks.map(taskId => ({
        id: taskId,
        updates: { priority }
      }));

      await bulkUpdateTasks(updates, (progress) => {
        setLoadingProgress(progress);
      });

      clearSelection();
    } catch (error) {
      console.error('Failed to update priority:', error);
    } finally {
      setIsLoading(false);
      setLoadingProgress(0);
    }
  }, [selectedTasks, bulkUpdateTasks, clearSelection]);

  const handleBulkSubjectAssignment = useCallback(async (subjectId: string) => {
    setIsLoading(true);
    setLoadingProgress(0);
    
    try {
      const updates = selectedTasks.map(taskId => ({
        id: taskId,
        updates: { subjectId }
      }));

      await bulkUpdateTasks(updates, (progress) => {
        setLoadingProgress(progress);
      });

      clearSelection();
    } catch (error) {
      console.error('Failed to assign subject:', error);
    } finally {
      setIsLoading(false);
      setLoadingProgress(0);
    }
  }, [selectedTasks, bulkUpdateTasks, clearSelection]);

  const handleBulkDifficultyChange = useCallback(async (difficultyLevel: 'easy' | 'medium' | 'hard') => {
    setIsLoading(true);
    setLoadingProgress(0);
    
    try {
      const updates = selectedTasks.map(taskId => ({
        id: taskId,
        updates: { difficultyLevel }
      }));

      await bulkUpdateTasks(updates, (progress) => {
        setLoadingProgress(progress);
      });

      clearSelection();
    } catch (error) {
      console.error('Failed to update difficulty:', error);
    } finally {
      setIsLoading(false);
      setLoadingProgress(0);
    }
  }, [selectedTasks, bulkUpdateTasks, clearSelection]);

  // Get statistics about selected tasks
  const stats = {
    total: selectedTasks.length,
    completed: selectedTaskObjects.filter(task => task.completionPercentage === 100).length,
    highPriority: selectedTaskObjects.filter(task => task.priority === 'high').length,
    withSubject: selectedTaskObjects.filter(task => task.subjectId).length,
    overdue: selectedTaskObjects.filter(task => 
      task.dueDate && new Date(task.dueDate) < new Date() && task.completionPercentage < 100
    ).length,
  };

  if (selectedTasks.length === 0) {
    return null;
  }

  return (
    <>
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 50 }}
          className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50"
        >
          <div className="bg-card border border-border rounded-lg shadow-lg p-4 min-w-[400px]">
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <Badge variant="secondary" className="text-sm">
                  {selectedTasks.length} selected
                </Badge>
                
                {stats.completed > 0 && (
                  <Badge variant="outline" className="text-xs">
                    {stats.completed} completed
                  </Badge>
                )}
                
                {stats.overdue > 0 && (
                  <Badge variant="destructive" className="text-xs">
                    {stats.overdue} overdue
                  </Badge>
                )}
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={onClearSelection}
                className="h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Progress bar (shown during bulk operations) */}
            <AnimatePresence>
              {isLoading && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mb-4"
                >
                  <div className="flex items-center gap-2 mb-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-sm text-muted-foreground">
                      Processing {selectedTasks.length} tasks...
                    </span>
                  </div>
                  <Progress value={loadingProgress} className="h-2" />
                </motion.div>
              )}
            </AnimatePresence>

            {/* Action buttons */}
            <div className="flex items-center gap-2">
              {/* Quick actions */}
              <Button
                variant="default"
                size="sm"
                onClick={handleBulkComplete}
                disabled={isLoading || stats.completed === stats.total}
                className="flex items-center gap-2"
              >
                <CheckCircle2 className="h-4 w-4" />
                Complete All
              </Button>

              <Button
                variant="destructive"
                size="sm"
                onClick={() => setShowDeleteDialog(true)}
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                <Trash2 className="h-4 w-4" />
                Delete
              </Button>

              {/* Priority dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" disabled={isLoading}>
                    <Flag className="h-4 w-4 mr-2" />
                    Priority
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuLabel>Set Priority</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => handleBulkPriorityChange('high')}>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-red-500" />
                      High Priority
                    </div>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleBulkPriorityChange('medium')}>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-yellow-500" />
                      Medium Priority
                    </div>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleBulkPriorityChange('low')}>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-green-500" />
                      Low Priority
                    </div>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Subject assignment */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" disabled={isLoading}>
                    <BookOpen className="h-4 w-4 mr-2" />
                    Subject
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuLabel>Assign Subject</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {Object.values(subjects).map(subject => (
                    <DropdownMenuItem 
                      key={subject.id}
                      onClick={() => handleBulkSubjectAssignment(subject.id)}
                    >
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-2 h-2 rounded-full" 
                          style={{ backgroundColor: subject.color }}
                        />
                        {subject.name}
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* More actions */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" disabled={isLoading}>
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuLabel>More Actions</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  
                  <DropdownMenuItem onClick={() => handleBulkDifficultyChange('easy')}>
                    <Tag className="h-4 w-4 mr-2 text-green-500" />
                    Mark as Easy
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleBulkDifficultyChange('medium')}>
                    <Tag className="h-4 w-4 mr-2 text-yellow-500" />
                    Mark as Medium
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleBulkDifficultyChange('hard')}>
                    <Tag className="h-4 w-4 mr-2 text-red-500" />
                    Mark as Hard
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Delete confirmation dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Selected Tasks</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {selectedTasks.length} selected task{selectedTasks.length > 1 ? 's' : ''}? 
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleBulkDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete {selectedTasks.length} Task{selectedTasks.length > 1 ? 's' : ''}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
