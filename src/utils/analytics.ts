import { supabase } from '../integrations/supabase/client';

interface StudySession {
  subject: string;
  duration: number;
  mode: "pomodoro" | "stopwatch";
  phase: "work" | "shortBreak" | "longBreak";
  completed: boolean;
  start_time: string; // Use start_time from Supabase
  // date, weekNumber, month, year will be derived
}

interface UserStats {
  totalStudyTime: number;
  completedPomodoros: number;
  totalSubjects: number;
  studyStreak: number;
  subjectDistribution: {
    subject: string;
    totalDuration: number;
    completedPomodoros: number;
  }[];
  dailyProgress: {
    date: string;
    totalDuration: number;
    completedPomodoros: number;
  }[];
  weeklyProgress: {
    weekNumber: number;
    weekDisplay: string;
    totalDuration: number;
    completedPomodoros: number;
  }[];
  monthlyProgress: {
    month: string;
    totalDuration: number;
    completedPomodoros: number;
  }[];
}

export async function getUserStudyAnalytics(userId: string): Promise<UserStats | null> {
  const { data: sessions, error } = await supabase
    .from('study_sessions')
    .select('*')
    .eq('user_id', userId);

  if (error) {
    console.error('Error fetching study sessions:', error);
    return null;
  }

  if (!sessions) {
    return null;
  }

  return processUserStats(sessions as any[]);
}

function processUserStats(sessions: StudySession[]): UserStats {
    const stats: UserStats = {
      totalStudyTime: 0,
      completedPomodoros: 0,
      totalSubjects: 0,
      studyStreak: 0,
      subjectDistribution: [],
      dailyProgress: [],
      weeklyProgress: [],
      monthlyProgress: []
    }

    const subjectMap = new Map<string, { totalDuration: number; completedPomodoros: number }>()
    const dailyMap = new Map<string, { totalDuration: number; completedPomodoros: number }>()
    const weeklyMap = new Map<string, { totalDuration: number; completedPomodoros: number }>()
    const monthlyMap = new Map<string, { totalDuration: number; completedPomodoros: number }>()

    sessions.forEach(session => {
      const sessionDate = new Date(session.start_time);
      const dateString = sessionDate.toISOString().split('T')[0]; // YYYY-MM-DD
      const year = sessionDate.getFullYear();
      const month = sessionDate.toLocaleString('default', { month: 'long' });
      const weekNumber = Math.ceil((sessionDate.getTime() - new Date(year, 0, 1).getTime()) / (86400000 * 7)); // Simple week number calculation

      // Update total study time
      stats.totalStudyTime += session.duration;

      // Update completed pomodoros
      if (session.completed && session.mode === "pomodoro") {
        stats.completedPomodoros++;
      }

      // Update subject distribution
      if (!subjectMap.has(session.subject)) {
        subjectMap.set(session.subject, { totalDuration: 0, completedPomodoros: 0 });
      }
      const subjectStats = subjectMap.get(session.subject)!;
      subjectStats.totalDuration += session.duration;
      if (session.completed && session.mode === "pomodoro") {
        subjectStats.completedPomodoros++;
      }

      // Update daily progress
      if (!dailyMap.has(dateString)) {
        dailyMap.set(dateString, { totalDuration: 0, completedPomodoros: 0 });
      }
      const dailyStats = dailyMap.get(dateString)!;
      dailyStats.totalDuration += session.duration;
      if (session.completed && session.mode === "pomodoro") {
        dailyStats.completedPomodoros++;
      }

      // Update weekly progress
      const weekKey = `${year}-W${weekNumber}`;
      if (!weeklyMap.has(weekKey)) {
        weeklyMap.set(weekKey, { totalDuration: 0, completedPomodoros: 0 });
      }
      const weeklyStats = weeklyMap.get(weekKey)!;
      weeklyStats.totalDuration += session.duration;
      if (session.completed && session.mode === "pomodoro") {
        weeklyStats.completedPomodoros++;
      }

      // Update monthly progress
      if (!monthlyMap.has(month)) {
        monthlyMap.set(month, { totalDuration: 0, completedPomodoros: 0 });
      }
      const monthlyStats = monthlyMap.get(month)!;
      monthlyStats.totalDuration += session.duration;
      if (session.completed && session.mode === "pomodoro") {
        monthlyStats.completedPomodoros++;
      }
    });

    // Convert maps to arrays
    stats.subjectDistribution = Array.from(subjectMap.entries()).map(([subject, data]) => ({
      subject,
      ...data
    }))
    stats.totalSubjects = stats.subjectDistribution.length

    stats.dailyProgress = Array.from(dailyMap.entries()).map(([date, data]) => ({
      date,
      ...data
    })).sort((a, b) => a.date.localeCompare(b.date))

    stats.weeklyProgress = Array.from(weeklyMap.entries()).map(([weekKey, data]) => ({
      weekNumber: parseInt(weekKey.split('-W')[1]),
      weekDisplay: `Week ${weekKey.split('-W')[1]}`, // Ensure this is correctly populated
      ...data
    })).sort((a, b) => a.weekNumber - b.weekNumber)

    stats.monthlyProgress = Array.from(monthlyMap.entries()).map(([month, data]) => ({
      month,
      ...data
    })).sort((a, b) => a.month.localeCompare(b.month))

    // Calculate study streak
    let currentStreak = 0
    let maxStreak = 0
    let lastDate = ''
    stats.dailyProgress.forEach(day => {
      if (lastDate === '') {
        currentStreak = 1
      } else {
        const dayDiff = Math.floor((new Date(day.date).getTime() - new Date(lastDate).getTime()) / (1000 * 60 * 60 * 24))
        if (dayDiff === 1) {
          currentStreak++
        } else {
          currentStreak = 1
        }
      }
      maxStreak = Math.max(maxStreak, currentStreak)
      lastDate = day.date
    })
    stats.studyStreak = maxStreak

    return stats
}
