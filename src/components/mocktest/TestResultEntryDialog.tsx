import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FileText, 
  Clock, 
  Target, 
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Plus,
  Trash2
} from 'lucide-react';
import { format } from 'date-fns';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { calculateTestTotals } from '@/utils/mockTestUtils';
import { testCompletionService } from '@/utils/testCompletionService';
import { 
  MockTest, 
  UpcomingTest,
  SubjectMarks,
  DifficultyAssessment,
  TimeManagement,
  ConfidenceLevel
} from '@/types/mockTest';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogHeader,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { cn } from '@/lib/utils';
import { useSupabaseSubjectStore } from '@/stores/supabaseSubjectStore';
import { enhancedMockTestUtils, upcomingTestStorage } from '@/utils/mockTestLocalStorage';

interface TestResultEntryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  upcomingTest: UpcomingTest | null;
  onResultsEntered?: () => void;
}

interface SubjectMarkInput {
  subjectId: string;
  marksObtained: string;
  totalMarks: string;
  timeSpent?: string;
  questionsAttempted?: string;
  questionsCorrect?: string;
}

interface FormData {
  // Subject Performance
  subjectInputs: SubjectMarkInput[];
  
  // Overall Performance
  actualTimeSpent: string;
  actualDifficulty: DifficultyAssessment;
  timeManagement: TimeManagement;
  stressLevel: ConfidenceLevel;
  
  // Initial Analysis
  initialObservations: string;
  keyMistakes: string[];
  keyTakeaways: string[];
  areasForImprovement: string[];
  
  // Notes
  notes: string;
}

export function TestResultEntryDialog({
  open,
  onOpenChange,
  upcomingTest,
  onResultsEntered
}: TestResultEntryDialogProps) {
  const { user } = useSupabaseAuth();
  const { subjects } = useSupabaseSubjectStore();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    subjectInputs: [],
    actualTimeSpent: '',
    actualDifficulty: 'as_expected',
    timeManagement: 'good',
    stressLevel: 3,
    initialObservations: '',
    keyMistakes: [''],
    keyTakeaways: [''],
    areasForImprovement: [''],
    notes: ''
  });

  // Initialize form when upcoming test changes
  useEffect(() => {
    if (upcomingTest && open) {
      // Initialize subject inputs based on upcoming test syllabus or default subjects
      const initialSubjects = upcomingTest.syllabus?.topics && upcomingTest.syllabus.topics.length > 0
        ? upcomingTest.syllabus.topics.map(topic => {
            // Try to match syllabus topics with subjects
            const matchedSubject = subjects.find(s => 
              s.name.toLowerCase().includes(topic.toLowerCase()) ||
              topic.toLowerCase().includes(s.name.toLowerCase())
            );
            return {
              subjectId: matchedSubject?.id || '',
              marksObtained: '',
              totalMarks: '',
              timeSpent: '',
              questionsAttempted: '',
              questionsCorrect: ''
            };
          })
        : [{ subjectId: '', marksObtained: '', totalMarks: '', timeSpent: '', questionsAttempted: '', questionsCorrect: '' }];

      setFormData(prev => ({
        ...prev,
        subjectInputs: initialSubjects,
        actualTimeSpent: upcomingTest.duration?.toString() || '',
        actualDifficulty: 'as_expected',
        timeManagement: 'good',
        stressLevel: 3,
        initialObservations: '',
        keyMistakes: [''],
        keyTakeaways: [''],
        areasForImprovement: [''],
        notes: ''
      }));
    }
  }, [upcomingTest, open, subjects]);

  const updateFormData = (updates: Partial<FormData>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const addSubjectInput = () => {
    setFormData(prev => ({
      ...prev,
      subjectInputs: [...prev.subjectInputs, { 
        subjectId: '', 
        marksObtained: '', 
        totalMarks: '', 
        timeSpent: '', 
        questionsAttempted: '',
        questionsCorrect: ''
      }]
    }));
  };

  const removeSubjectInput = (index: number) => {
    setFormData(prev => ({
      ...prev,
      subjectInputs: prev.subjectInputs.filter((_, i) => i !== index)
    }));
  };

  const updateSubjectInput = (index: number, updates: Partial<SubjectMarkInput>) => {
    setFormData(prev => ({
      ...prev,
      subjectInputs: prev.subjectInputs.map((input, i) => 
        i === index ? { ...input, ...updates } : input
      )
    }));
  };

  const addListItem = (listKey: 'keyMistakes' | 'keyTakeaways' | 'areasForImprovement') => {
    setFormData(prev => ({
      ...prev,
      [listKey]: [...prev[listKey], '']
    }));
  };

  const removeListItem = (listKey: 'keyMistakes' | 'keyTakeaways' | 'areasForImprovement', index: number) => {
    setFormData(prev => ({
      ...prev,
      [listKey]: prev[listKey].filter((_, i) => i !== index)
    }));
  };

  const updateListItem = (listKey: 'keyMistakes' | 'keyTakeaways' | 'areasForImprovement', index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      [listKey]: prev[listKey].map((item, i) => i === index ? value : item)
    }));
  };

  const validateForm = (): boolean => {
    // Check if at least one subject has marks
    const hasValidSubject = formData.subjectInputs.some(input => 
      input.subjectId && input.marksObtained && input.totalMarks
    );
    
    if (!hasValidSubject) {
      toast({
        title: "Validation Error",
        description: "At least one subject with marks is required",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!user?.id || !upcomingTest || !validateForm()) return;

    setIsSubmitting(true);
    try {
      // Process subject inputs
      const subjectMarks: SubjectMarks[] = formData.subjectInputs
        .filter(input => input.subjectId && input.marksObtained && input.totalMarks)
        .map(input => {
          const subject = subjects.find(s => s.id === input.subjectId);
          if (!subject) throw new Error("Subject not found");
          
          return {
            subject: subject.name,
            subjectColor: subject.color,
            marksObtained: parseFloat(input.marksObtained),
            totalMarks: parseFloat(input.totalMarks)
          };
        });

      // Calculate totals
      const { totalMarksObtained, totalMarks } = calculateTestTotals(subjectMarks);

      // Create comprehensive mock test from upcoming test
      const mockTest: MockTest = {
        id: `mock-${Date.now()}`,
        name: upcomingTest.name,
        date: upcomingTest.date,
        time: upcomingTest.time,
        subjectMarks,
        totalMarksObtained,
        totalMarks,
        notes: formData.notes,
        createdAt: new Date().toISOString(),
        userId: user.id,
        
        // Enhanced fields from upcoming test
        testType: upcomingTest.testType || 'mock',
        testEnvironment: upcomingTest.testEnvironment,
        duration: upcomingTest.duration,
        totalQuestions: upcomingTest.totalQuestions,
        categoryId: upcomingTest.categoryId,
        testPaperUrl: upcomingTest.testPaperUrl,
        
        // Performance data
        timeSpent: formData.actualTimeSpent ? parseInt(formData.actualTimeSpent) : undefined,
        actualDifficulty: formData.actualDifficulty,
        timeManagement: formData.timeManagement,
        stressLevel: formData.stressLevel,
        
        // Goals from upcoming test
        targetScore: upcomingTest.targetScore || 0,
        confidenceLevel: upcomingTest.confidenceLevel,
        preparationTime: upcomingTest.preparationTime,
        studyMaterials: upcomingTest.studyMaterials,
        
        difficulty: upcomingTest.expectedDifficulty || 'medium',
        isReviewed: false,
        mistakes: [], // Will be populated later if needed
        takeaways: [], // Will be populated later if needed
        analysisCompleted: false,
        mistakesAnalyzed: false,
        takeawaysRecorded: false,
        
        status: 'completed',
        completedAt: new Date().toISOString(),
        resultEnteredAt: new Date().toISOString(),
        isFromUpcoming: true,
        upcomingTestId: upcomingTest.id
      };

      // Save the mock test
      await enhancedMockTestUtils.save(user.id, mockTest);

      // Remove from upcoming tests
      upcomingTestStorage.delete(user.id, upcomingTest.id);

      // Reset form and close dialog
      resetForm();
      onOpenChange(false);

      toast({
        title: "Success",
        description: "Test results entered successfully! The test has been moved to your completed tests.",
      });
      
      if (onResultsEntered) {
        onResultsEntered();
      }
    } catch (error) {
      console.error("Error saving test results:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to save test results",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setFormData({
      subjectInputs: [],
      actualTimeSpent: '',
      actualDifficulty: 'as_expected',
      timeManagement: 'good',
      stressLevel: 3,
      initialObservations: '',
      keyMistakes: [''],
      keyTakeaways: [''],
      areasForImprovement: [''],
      notes: ''
    });
  };

  const handleClose = () => {
    resetForm();
    onOpenChange(false);
  };

  if (!upcomingTest) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-violet-600" />
            Enter Test Results - {upcomingTest.name}
          </DialogTitle>
          <div className="text-sm text-muted-foreground">
            Test Date: {format(new Date(upcomingTest.date), 'PPP')}
            {upcomingTest.time && ` at ${upcomingTest.time}`}
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Test Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Target className="h-5 w-5 text-violet-600" />
                Test Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium text-muted-foreground">Type:</span>
                <p className="capitalize">{upcomingTest.testType || 'Mock Test'}</p>
              </div>
              {upcomingTest.duration && (
                <div>
                  <span className="font-medium text-muted-foreground">Expected Duration:</span>
                  <p>{upcomingTest.duration} minutes</p>
                </div>
              )}
              {upcomingTest.targetScore && (
                <div>
                  <span className="font-medium text-muted-foreground">Target Score:</span>
                  <p>{upcomingTest.targetScore}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Subject-wise Performance */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Subject-wise Performance</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {formData.subjectInputs.map((input, index) => (
                <div key={index} className="p-4 border rounded-lg space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Subject {index + 1}</h4>
                    {formData.subjectInputs.length > 1 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeSubjectInput(index)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    <div className="space-y-2">
                      <Label>Subject *</Label>
                      <Select 
                        value={input.subjectId} 
                        onValueChange={(value) => updateSubjectInput(index, { subjectId: value })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select subject" />
                        </SelectTrigger>
                        <SelectContent>
                          {subjects.map((subject) => (
                            <SelectItem key={subject.id} value={subject.id}>
                              <div className="flex items-center gap-2">
                                <div 
                                  className="w-3 h-3 rounded-full" 
                                  style={{ backgroundColor: subject.color }}
                                />
                                {subject.name}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Time Spent (minutes)</Label>
                      <Input
                        type="number"
                        placeholder="e.g., 45"
                        value={input.timeSpent}
                        onChange={(e) => updateSubjectInput(index, { timeSpent: e.target.value })}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                    <div className="space-y-2">
                      <Label>Marks Obtained *</Label>
                      <Input
                        type="number"
                        placeholder="e.g., 85"
                        value={input.marksObtained}
                        onChange={(e) => updateSubjectInput(index, { marksObtained: e.target.value })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Total Marks *</Label>
                      <Input
                        type="number"
                        placeholder="e.g., 100"
                        value={input.totalMarks}
                        onChange={(e) => updateSubjectInput(index, { totalMarks: e.target.value })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Questions Attempted</Label>
                      <Input
                        type="number"
                        placeholder="e.g., 25"
                        value={input.questionsAttempted}
                        onChange={(e) => updateSubjectInput(index, { questionsAttempted: e.target.value })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Questions Correct</Label>
                      <Input
                        type="number"
                        placeholder="e.g., 20"
                        value={input.questionsCorrect}
                        onChange={(e) => updateSubjectInput(index, { questionsCorrect: e.target.value })}
                      />
                    </div>
                  </div>

                  {input.marksObtained && input.totalMarks && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                      <div className="p-2 bg-emerald-50 rounded text-sm">
                        <span className="font-medium text-emerald-700">Percentage: </span>
                        <span className="text-emerald-600">
                          {((parseFloat(input.marksObtained) / parseFloat(input.totalMarks)) * 100).toFixed(1)}%
                        </span>
                      </div>
                      
                      {input.questionsAttempted && input.questionsCorrect && (
                        <div className="p-2 bg-blue-50 rounded text-sm">
                          <span className="font-medium text-blue-700">Accuracy: </span>
                          <span className="text-blue-600">
                            {((parseFloat(input.questionsCorrect) / parseFloat(input.questionsAttempted)) * 100).toFixed(1)}%
                          </span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}

              <Button
                variant="outline"
                onClick={addSubjectInput}
                className="w-full border-dashed"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Another Subject
              </Button>
            </CardContent>
          </Card>

          {/* Overall Performance Assessment */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-violet-600" />
                Overall Performance Assessment
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="actualTimeSpent">Actual Time Spent (minutes)</Label>
                  <Input
                    id="actualTimeSpent"
                    type="number"
                    placeholder="e.g., 165"
                    value={formData.actualTimeSpent}
                    onChange={(e) => updateFormData({ actualTimeSpent: e.target.value })}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Difficulty Compared to Expectation</Label>
                  <Select
                    value={formData.actualDifficulty}
                    onValueChange={(value: DifficultyAssessment) => updateFormData({ actualDifficulty: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="easier">Easier than expected</SelectItem>
                      <SelectItem value="as_expected">As expected</SelectItem>
                      <SelectItem value="harder">Harder than expected</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Time Management</Label>
                  <Select
                    value={formData.timeManagement}
                    onValueChange={(value: TimeManagement) => updateFormData({ timeManagement: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="excellent">Excellent</SelectItem>
                      <SelectItem value="good">Good</SelectItem>
                      <SelectItem value="average">Average</SelectItem>
                      <SelectItem value="poor">Poor</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-3">
                  <Label>Stress Level During Test: {formData.stressLevel}/5</Label>
                  <Slider
                    value={[formData.stressLevel]}
                    onValueChange={([value]) => updateFormData({ stressLevel: value as ConfidenceLevel })}
                    max={5}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Very Low</span>
                    <span>Low</span>
                    <span>Medium</span>
                    <span>High</span>
                    <span>Very High</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Initial Analysis */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <AlertCircle className="h-5 w-5 text-violet-600" />
                Initial Analysis & Observations
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="initialObservations">Overall Observations</Label>
                <Textarea
                  id="initialObservations"
                  placeholder="How did the test go? Any immediate thoughts or observations..."
                  value={formData.initialObservations}
                  onChange={(e) => updateFormData({ initialObservations: e.target.value })}
                  className="min-h-[80px]"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <Label className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-red-500" />
                    Key Mistakes (Optional)
                  </Label>
                  {formData.keyMistakes.map((mistake, index) => (
                    <div key={index} className="flex gap-2">
                      <Input
                        placeholder="Describe a mistake or area of confusion..."
                        value={mistake}
                        onChange={(e) => updateListItem('keyMistakes', index, e.target.value)}
                      />
                      {formData.keyMistakes.length > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeListItem('keyMistakes', index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => addListItem('keyMistakes')}
                    className="w-full border-dashed"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Mistake
                  </Button>
                </div>

                <div className="space-y-3">
                  <Label className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-500" />
                    Key Takeaways (Optional)
                  </Label>
                  {formData.keyTakeaways.map((takeaway, index) => (
                    <div key={index} className="flex gap-2">
                      <Input
                        placeholder="What did you learn or do well..."
                        value={takeaway}
                        onChange={(e) => updateListItem('keyTakeaways', index, e.target.value)}
                      />
                      {formData.keyTakeaways.length > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeListItem('keyTakeaways', index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => addListItem('keyTakeaways')}
                    className="w-full border-dashed"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Takeaway
                  </Button>
                </div>
              </div>

              <div className="space-y-3">
                <Label className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-blue-500" />
                  Areas for Improvement (Optional)
                </Label>
                {formData.areasForImprovement.map((area, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      placeholder="What areas need more focus or practice..."
                      value={area}
                      onChange={(e) => updateListItem('areasForImprovement', index, e.target.value)}
                    />
                    {formData.areasForImprovement.length > 1 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeListItem('areasForImprovement', index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addListItem('areasForImprovement')}
                  className="w-full border-dashed"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Area
                </Button>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Additional Notes</Label>
                <Textarea
                  id="notes"
                  placeholder="Any additional notes, thoughts, or context about this test..."
                  value={formData.notes}
                  onChange={(e) => updateFormData({ notes: e.target.value })}
                  className="min-h-[80px]"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="bg-violet-600 hover:bg-violet-700"
          >
            {isSubmitting ? "Saving..." : "Save Results"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
