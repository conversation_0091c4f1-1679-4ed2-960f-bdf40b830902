import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Bell, 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  X, 
  Calendar,
  TrendingUp,
  FileText
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { 
  testCompletionService, 
  CompletedTestNotification, 
  TestCompletionStats 
} from '@/utils/testCompletionService';
import { format, parseISO } from 'date-fns';
import { cn } from '@/lib/utils';

interface TestCompletionNotificationsProps {
  onTestResultEntry?: (testId: string) => void;
  onMarkAsMissed?: (testId: string) => void;
}

export function TestCompletionNotifications({
  onTestResultEntry,
  onMarkAsMissed
}: TestCompletionNotificationsProps) {
  const { user } = useSupabaseAuth();
  const [notifications, setNotifications] = useState<CompletedTestNotification[]>([]);
  const [stats, setStats] = useState<TestCompletionStats>({
    pendingResults: 0,
    completedToday: 0,
    missedTests: 0,
    upcomingThisWeek: 0
  });
  const [isOpen, setIsOpen] = useState(false);
  const [dismissedNotifications, setDismissedNotifications] = useState<Set<string>>(new Set());

  // Load notifications and stats
  const loadData = () => {
    if (!user?.id) return;

    const completedNotifications = testCompletionService.checkForCompletedTests(user.id);
    const completionStats = testCompletionService.getCompletionStats(user.id);
    
    setNotifications(completedNotifications);
    setStats(completionStats);
  };

  useEffect(() => {
    loadData();
    
    // Set up periodic checking (every 5 minutes)
    const interval = setInterval(loadData, 5 * 60 * 1000);
    
    // Subscribe to completion notifications
    const unsubscribe = testCompletionService.onCompletionNotifications((newNotifications) => {
      setNotifications(newNotifications);
    });

    return () => {
      clearInterval(interval);
      unsubscribe();
    };
  }, [user?.id]);

  // Filter out dismissed notifications
  const activeNotifications = notifications.filter(
    notification => !dismissedNotifications.has(notification.id)
  );

  const totalPendingActions = activeNotifications.length;

  const handleEnterResults = (notification: CompletedTestNotification) => {
    if (onTestResultEntry) {
      onTestResultEntry(notification.id);
    }
    setIsOpen(false);
  };

  const handleMarkAsMissed = (notification: CompletedTestNotification) => {
    if (onMarkAsMissed) {
      onMarkAsMissed(notification.id);
    }
    dismissNotification(notification.id);
  };

  const dismissNotification = (notificationId: string) => {
    setDismissedNotifications(prev => new Set([...prev, notificationId]));
  };

  const getUrgencyColor = (daysOverdue: number) => {
    if (daysOverdue >= 7) return 'text-red-600 bg-red-50 border-red-200';
    if (daysOverdue >= 3) return 'text-orange-600 bg-orange-50 border-orange-200';
    return 'text-yellow-600 bg-yellow-50 border-yellow-200';
  };

  const getUrgencyBadge = (daysOverdue: number) => {
    if (daysOverdue >= 7) return { text: 'URGENT', variant: 'destructive' as const };
    if (daysOverdue >= 3) return { text: 'HIGH', variant: 'secondary' as const };
    return { text: 'MEDIUM', variant: 'outline' as const };
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className={cn(
            "relative transition-all duration-300",
            totalPendingActions > 0 && "border-violet-300 bg-violet-50 hover:bg-violet-100"
          )}
        >
          <Bell className="h-4 w-4" />
          {totalPendingActions > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs"
            >
              {totalPendingActions}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      
      <PopoverContent className="w-96 p-0" align="end">
        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Bell className="h-5 w-5 text-violet-600" />
              Test Notifications
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {/* Stats Overview */}
            <div className="grid grid-cols-2 gap-3">
              <div className="flex items-center gap-2 p-2 rounded-lg bg-violet-50 border border-violet-200">
                <FileText className="h-4 w-4 text-violet-600" />
                <div>
                  <p className="text-sm font-medium text-violet-700">{stats.pendingResults}</p>
                  <p className="text-xs text-violet-600">Pending Results</p>
                </div>
              </div>
              
              <div className="flex items-center gap-2 p-2 rounded-lg bg-emerald-50 border border-emerald-200">
                <CheckCircle className="h-4 w-4 text-emerald-600" />
                <div>
                  <p className="text-sm font-medium text-emerald-700">{stats.completedToday}</p>
                  <p className="text-xs text-emerald-600">Completed Today</p>
                </div>
              </div>
              
              <div className="flex items-center gap-2 p-2 rounded-lg bg-blue-50 border border-blue-200">
                <Calendar className="h-4 w-4 text-blue-600" />
                <div>
                  <p className="text-sm font-medium text-blue-700">{stats.upcomingThisWeek}</p>
                  <p className="text-xs text-blue-600">This Week</p>
                </div>
              </div>
              
              <div className="flex items-center gap-2 p-2 rounded-lg bg-red-50 border border-red-200">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <div>
                  <p className="text-sm font-medium text-red-700">{stats.missedTests}</p>
                  <p className="text-xs text-red-600">Missed Tests</p>
                </div>
              </div>
            </div>

            {/* Pending Results Notifications */}
            {activeNotifications.length > 0 ? (
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-orange-600" />
                  <h4 className="font-medium text-sm">Tests Awaiting Results</h4>
                </div>
                
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  <AnimatePresence>
                    {activeNotifications.map((notification) => {
                      const urgencyBadge = getUrgencyBadge(notification.daysOverdue);
                      
                      return (
                        <motion.div
                          key={notification.id}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -10 }}
                          className={cn(
                            "p-3 rounded-lg border transition-all duration-200",
                            getUrgencyColor(notification.daysOverdue)
                          )}
                        >
                          <div className="flex items-start justify-between gap-2">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <h5 className="font-medium text-sm truncate">
                                  {notification.testName}
                                </h5>
                                <Badge variant={urgencyBadge.variant} className="text-xs">
                                  {urgencyBadge.text}
                                </Badge>
                              </div>
                              
                              <p className="text-xs opacity-75 mb-2">
                                Test Date: {format(parseISO(notification.testDate), 'MMM d, yyyy')}
                                {notification.daysOverdue > 0 && (
                                  <span className="ml-1">
                                    ({notification.daysOverdue} day{notification.daysOverdue > 1 ? 's' : ''} ago)
                                  </span>
                                )}
                              </p>
                              
                              <div className="flex gap-1">
                                <Button
                                  size="sm"
                                  variant="default"
                                  className="h-6 px-2 text-xs bg-violet-600 hover:bg-violet-700"
                                  onClick={() => handleEnterResults(notification)}
                                >
                                  Enter Results
                                </Button>
                                
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="h-6 px-2 text-xs"
                                  onClick={() => handleMarkAsMissed(notification)}
                                >
                                  Mark Missed
                                </Button>
                              </div>
                            </div>
                            
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-6 w-6 p-0 opacity-50 hover:opacity-100"
                              onClick={() => dismissNotification(notification.id)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        </motion.div>
                      );
                    })}
                  </AnimatePresence>
                </div>
              </div>
            ) : (
              <div className="text-center py-6">
                <CheckCircle className="h-8 w-8 text-emerald-500 mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">
                  All caught up! No pending test results.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </PopoverContent>
    </Popover>
  );
}
