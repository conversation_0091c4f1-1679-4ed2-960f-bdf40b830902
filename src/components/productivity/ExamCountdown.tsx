import { useState, useEffect, useRef, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { CalendarIcon, X, Clock, TimerOff, Edit2, Save, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { format, addDays, isBefore } from "date-fns";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Badge } from "@/components/ui/badge";
import React from "react";
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';
import { dDayStorage, dDayIntegration, DDayExam } from "@/utils/mockTestLocalStorage";

export function ExamCountdown() {
  const [exams, setExams] = useState<DDayExam[]>([]);
  const [showAddExam, setShowAddExam] = useState(false);
  const [open, setOpen] = useState(false);
  const [newExam, setNewExam] = useState<{
    name: string;
    date: Date;
    time: string;
  }>({
    name: "",
    date: new Date(),
    time: "09:00",
  });
  const [editingExam, setEditingExam] = useState<string | null>(null);
  const [timeLeft, setTimeLeft] = useState<Record<string, { days: number; hours: number; minutes: number; seconds: number }>>({});
  const [closestExam, setClosestExam] = useState<{id: string, name: string, days: number} | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Get current user from auth context
  const { user } = useSupabaseAuth();
  
  // Use a ref to store the interval ID
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  
  // Prevent automatic open/close by using a manual control
  const handleOpenChange = useCallback((newOpen: boolean) => {
    setOpen(newOpen);
  }, []);

  // Calculate time left using a stable reference
  const calculateTimeLeft = useCallback(() => {
    if (exams.length === 0) {
      setTimeLeft({});
      setClosestExam(null);
      return;
    }
    
    const updatedTimeLeft: Record<string, { days: number; hours: number; minutes: number; seconds: number }> = {};
    let closest: {id: string, name: string, days: number, totalSeconds: number} | null = null;
    
    exams.forEach(exam => {
      const examDateTime = new Date(exam.date);
      const [hours, minutes] = exam.time.split(':').map(Number);
      examDateTime.setHours(hours, minutes, 0, 0);
      
      const now = new Date();
      const diff = examDateTime.getTime() - now.getTime();
      
      if (diff > 0) {
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);
        const totalSeconds = diff / 1000;
        
        updatedTimeLeft[exam.id] = { days, hours, minutes, seconds };
        
        // Update closest exam
        if (!closest || totalSeconds < closest.totalSeconds) {
          closest = { 
            id: exam.id, 
            name: exam.name, 
            days, 
            totalSeconds 
          };
        }
      }
    });
    
    setTimeLeft(updatedTimeLeft);
    
    // Only update if there's a meaningful change
    if (!closestExam && closest) {
      setClosestExam({ id: closest.id, name: closest.name, days: closest.days });
    } else if (closestExam && closest && (closestExam.id !== closest.id || closestExam.days !== closest.days)) {
      setClosestExam({ id: closest.id, name: closest.name, days: closest.days });
    } else if (closestExam && !closest) {
      setClosestExam(null);
    }
  }, [exams, closestExam]);

  // Set up the timer with cleanup
  useEffect(() => {
    // Initial calculation
    calculateTimeLeft();
    
    // Clear any existing interval
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    
    // Set up new interval
    intervalRef.current = setInterval(() => {
      calculateTimeLeft();
    }, 5000); // Reduce to every 5 seconds instead of every second
    
    // Cleanup
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [exams, calculateTimeLeft]);

  // Fetch exams from local storage when component mounts or user changes
  useEffect(() => {
    const fetchExams = () => {
      setLoading(true);
      setError(null);

      // Only fetch if user is logged in
      if (!user?.id) {
        setExams([]);
        setLoading(false);
        return;
      }

      try {
        // Get exams from local storage including upcoming tests synced with D-Day
        const examList = dDayIntegration.getUpcomingForDDay(user.id, 365); // Get exams for next year
        setExams(examList);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching exams:', err);
        setError('Failed to load your exams');
        setLoading(false);
      }
    };

    fetchExams();
  }, [user?.id]);

  const addExam = () => {
    if (!user?.id) {
      setError('You must be logged in to add exams');
      return;
    }

    if (newExam.name && newExam.date && newExam.time) {
      try {
        const examData: DDayExam = {
          id: `exam_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          name: newExam.name,
          date: newExam.date.toISOString().split('T')[0], // Store as YYYY-MM-DD
          time: newExam.time,
          userId: user.id,
          status: 'upcoming',
          priority: 'medium',
          reminderSettings: {
            enabled: true,
            intervals: [7, 3, 1] // 7 days, 3 days, 1 day before
          },
          preparationData: {
            chapters: [],
            totalTopics: []
          },
          syncedFromUpcoming: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        const savedExam = dDayStorage.save(user.id, examData);

        // Add the new exam to the local state
        setExams(prev => [...prev, savedExam]);

        setNewExam({ name: "", date: new Date(), time: "09:00" });
        setShowAddExam(false);
        setError(null);
      } catch (err) {
        console.error('Error adding exam:', err);
        setError('Failed to add exam');
      }
    }
  };

  const startEditExam = (exam: DDayExam) => {
    setEditingExam(exam.id);
    setNewExam({
      name: exam.name,
      date: new Date(exam.date),
      time: exam.time,
    });
    setShowAddExam(true);
    setError(null);
  };

  const saveEditedExam = () => {
    if (!user?.id) {
      setError('You must be logged in to edit exams');
      return;
    }

    if (editingExam && newExam.name && newExam.date && newExam.time) {
      try {
        const examUpdates = {
          name: newExam.name,
          date: newExam.date.toISOString().split('T')[0], // Store as YYYY-MM-DD
          time: newExam.time
        };

        const updatedExam = dDayStorage.update(user.id, editingExam, examUpdates);

        if (updatedExam) {
          // Update the local state
          setExams(prev => prev.map(exam =>
            exam.id === editingExam ? updatedExam : exam
          ));
        }

        setNewExam({ name: "", date: new Date(), time: "09:00" });
        setShowAddExam(false);
        setEditingExam(null);
        setError(null);
      } catch (err) {
        console.error('Error updating exam:', err);
        setError('Failed to update exam');
      }
    }
  };

  const cancelEdit = () => {
    setNewExam({ name: "", date: new Date(), time: "09:00" });
    setShowAddExam(false);
    setEditingExam(null);
    setError(null);
  };

  const removeExam = (id: string) => {
    if (!user?.id) {
      setError('You must be logged in to remove exams');
      return;
    }

    try {
      const success = dDayStorage.delete(user.id, id);

      if (success) {
        // Remove from local state
        setExams(prev => prev.filter(exam => exam.id !== id));
        setError(null);
      } else {
        setError('Exam not found');
      }
    } catch (err) {
      console.error('Error removing exam:', err);
      setError('Failed to remove exam');
    }
  };

  // Separate the trigger button from the Sheet component to prevent triggering
  return (
    <>
      {/* Button that opens the sheet */}
      <div className="sm:fixed sm:top-20 sm:right-6 sm:z-50">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => setOpen(true)}
          className={cn(
            "rounded-full backdrop-blur-sm border shadow-lg flex items-center gap-1.5 transition-all duration-300 px-3 py-2 h-auto",
            exams.length > 0 
              ? "bg-primary/10 text-primary border-primary/30 hover:bg-primary/20" 
              : "bg-background/70 dark:bg-slate-800/70 border-border dark:border-white/10 hover:bg-primary/10 hover:text-primary"
          )}
        >
          <Clock className="h-4 w-4" />
          <span>D-Day</span>
          {closestExam && (
            <Badge 
              variant="secondary" 
              className="ml-1 bg-primary text-primary-foreground text-xs"
            >
              {closestExam.days}d
            </Badge>
          )}
        </Button>
      </div>
      
      {/* Sheet that appears when button is clicked */}
      <Sheet open={open} onOpenChange={handleOpenChange}>
        <SheetContent className="sm:max-w-md z-[100] overflow-visible">
          <SheetHeader>
            <SheetTitle className="flex items-center">
              <Clock className="w-5 h-5 mr-2 text-primary" />
              Exam Countdown
            </SheetTitle>
          </SheetHeader>
          <div className="mt-6 overflow-visible">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-sm font-medium text-muted-foreground">
                Track your upcoming exams
              </h2>
              {user ? (
                !showAddExam ? (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowAddExam(true)}
                    className="border-dashed border-primary/50 text-primary hover:bg-primary/5 transition-colors"
                  >
                    Add Exam
                  </Button>
                ) : (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={cancelEdit}
                    className="border-dashed border-destructive/50 text-destructive hover:bg-destructive/5 transition-colors"
                  >
                    Cancel
                  </Button>
                )
              ) : (
                <div className="text-sm text-amber-500 flex items-center gap-1">
                  <AlertCircle className="h-4 w-4" />
                  Sign in to save exams
                </div>
              )}
            </div>

            {error && (
              <div className="mb-4 p-2 bg-destructive/10 text-destructive rounded-md text-sm flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                {error}
              </div>
            )}

            {showAddExam && user && (
              <div className="mb-4 p-3 bg-background/80 dark:bg-white/10 rounded-md animate-fadeIn overflow-visible">
                <div className="flex flex-col gap-3 overflow-visible">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium">
                      {editingExam ? "Edit Exam" : "Add New Exam"}
                    </h3>
                  </div>
                  
                  <Input
                    placeholder="Exam name"
                    value={newExam.name}
                    onChange={(e) => setNewExam({ ...newExam, name: e.target.value })}
                    className="bg-background/70"
                  />
                  
                  <div className="flex flex-col sm:flex-row gap-2 overflow-visible">
                    <div className="w-full sm:flex-1">
                      <label className="text-sm text-muted-foreground mb-1 block">Exam Date</label>
                      <div className="bg-background/70 border rounded-md p-1 w-full">
                        <Calendar
                          mode="single"
                          selected={newExam.date}
                          onSelect={(date) => {
                            if (date) {
                              setNewExam({ ...newExam, date });
                            }
                          }}
                          fromDate={new Date()}
                          className="rounded-md border-0 p-0"
                        />
                      </div>
                    </div>
                    
                    <div className="w-full sm:w-32">
                      <label className="text-sm text-muted-foreground mb-1 block">Time</label>
                      <Select
                        value={newExam.time}
                        onValueChange={(value) => setNewExam({ ...newExam, time: value })}
                      >
                        <SelectTrigger className="bg-background/70 w-full">
                          <SelectValue placeholder="Time" />
                        </SelectTrigger>
                        <SelectContent className="max-h-[300px] z-[150] w-[120px]">
                          {/* Generate times every 30 minutes */}
                          {Array.from({ length: 48 }).map((_, index) => {
                            const hour = Math.floor(index / 2);
                            const minute = (index % 2) * 30;
                            const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
                            return (
                              <SelectItem key={timeString} value={timeString}>
                                {timeString}
                              </SelectItem>
                            );
                          })}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <Button 
                    onClick={editingExam ? saveEditedExam : addExam} 
                    className={cn(
                      "w-full transition-colors flex items-center justify-center gap-2",
                      editingExam 
                        ? "bg-blue-600 hover:bg-blue-700" 
                        : "bg-primary hover:bg-primary/90"
                    )}
                  >
                    {editingExam ? (
                      <>
                        <Save className="h-4 w-4" />
                        Save Changes
                      </>
                    ) : (
                      "Add Exam"
                    )}
                  </Button>
                </div>
              </div>
            )}

            {loading ? (
              <div className="text-center py-10">
                <div className="animate-pulse">Loading your exams...</div>
              </div>
            ) : exams.length === 0 ? (
              <div className="text-center py-10 text-muted-foreground flex flex-col items-center">
                <TimerOff className="h-12 w-12 mb-2 opacity-20" />
                <p>No upcoming exams.</p>
                {user ? (
                  <p className="text-sm">Add your first exam to start tracking.</p>
                ) : (
                  <p className="text-sm">Sign in to save your exams.</p>
                )}
              </div>
            ) : (
              <div className="space-y-3 max-h-[60vh] overflow-y-auto pr-1 scrollbar-thin">
                {exams.map((exam) => (
                  <div
                    key={exam.id}
                    className="flex items-center justify-between p-3 bg-background/80 dark:bg-white/10 rounded-md hover:bg-background/90 dark:hover:bg-white/15 transition-colors"
                  >
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">{exam.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {format(new Date(exam.date), "PPP")} at {exam.time}
                      </div>
                    </div>
                    
                    {timeLeft[exam.id] && (
                      <div className="flex-shrink-0 text-right mr-4">
                        <div className="text-sm font-medium">
                          <span className="text-primary">{timeLeft[exam.id].days}</span>d{" "}
                          <span className="text-primary">{timeLeft[exam.id].hours}</span>h{" "}
                          <span className="text-primary">{timeLeft[exam.id].minutes}</span>m
                        </div>
                      </div>
                    )}
                    
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => startEditExam(exam)}
                        className="flex-shrink-0 h-8 w-8 hover:bg-primary/10 hover:text-primary transition-colors"
                      >
                        <Edit2 className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeExam(exam.id)}
                        className="flex-shrink-0 h-8 w-8 hover:bg-destructive/10 hover:text-destructive transition-colors"
                      >
                        <X className="h-4 w-4" />
                        <span className="sr-only">Remove</span>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </SheetContent>
      </Sheet>
    </>
  );
} 