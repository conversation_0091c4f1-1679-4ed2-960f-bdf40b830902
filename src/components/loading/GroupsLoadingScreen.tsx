import React from 'react';
import { motion } from 'framer-motion';
import { LoadingSkeleton } from '../ui/loading-skeleton';
import { LoadingSpinner } from '../ui/loading-spinner';
import { cn } from '@/lib/utils';

interface GroupsLoadingScreenProps {
  variant?: 'grid' | 'list' | 'detailed';
  className?: string;
}

export const GroupsLoadingScreen: React.FC<GroupsLoadingScreenProps> = ({
  variant = 'grid',
  className,
}) => {
  if (variant === 'detailed') {
    return (
      <motion.div
        className={cn('space-y-6 p-6', className)}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        {/* Header */}
        <motion.div
          className="text-center space-y-4"
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.1 }}
        >
          <div className="flex items-center justify-center space-x-3">
            <motion.div
              className="w-8 h-8 bg-gradient-to-br from-green-500 to-blue-600 rounded-lg flex items-center justify-center"
              animate={{
                rotate: [0, 360],
                scale: [1, 1.1, 1],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: 'easeInOut',
              }}
            >
              <svg
                className="w-4 h-4 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
            </motion.div>
            <h2 className="text-xl font-semibold text-foreground">
              Loading Study Groups
            </h2>
          </div>
          <LoadingSpinner variant="dots" size="md" color="primary" />
        </motion.div>

        {/* Group Cards Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="bg-card border rounded-lg p-6 space-y-4"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.3 + i * 0.1 }}
            >
              {/* Group Header */}
              <div className="flex items-center space-x-3">
                <motion.div
                  className="w-12 h-12 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full flex items-center justify-center"
                  animate={{
                    rotate: [0, 360],
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: 'linear',
                    delay: i * 0.5,
                  }}
                >
                  <div className="w-6 h-6 bg-primary/30 rounded-full" />
                </motion.div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted rounded animate-pulse" />
                  <div className="h-3 bg-muted rounded w-2/3 animate-pulse" />
                </div>
              </div>

              {/* Group Stats */}
              <div className="grid grid-cols-2 gap-4">
                {[...Array(4)].map((_, j) => (
                  <div key={j} className="space-y-1">
                    <div className="h-3 bg-muted rounded w-16 animate-pulse" />
                    <div className="h-5 bg-muted rounded w-8 animate-pulse" />
                  </div>
                ))}
              </div>

              {/* Member Avatars */}
              <div className="flex items-center space-x-2">
                <span className="text-xs text-muted-foreground">Members:</span>
                <div className="flex -space-x-2">
                  {[...Array(3)].map((_, k) => (
                    <motion.div
                      key={k}
                      className="w-6 h-6 bg-muted rounded-full border-2 border-background animate-pulse"
                      animate={{
                        scale: [1, 1.1, 1],
                      }}
                      transition={{
                        duration: 2,
                        repeat: Infinity,
                        delay: k * 0.2,
                      }}
                    />
                  ))}
                </div>
              </div>

              {/* Action Button */}
              <div className="h-8 bg-muted rounded animate-pulse" />
            </motion.div>
          ))}
        </motion.div>
      </motion.div>
    );
  }

  if (variant === 'list') {
    return (
      <motion.div
        className={cn('space-y-4 p-6', className)}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <LoadingSkeleton variant="list" count={8} animated />
      </motion.div>
    );
  }

  // Default grid variant
  return (
    <motion.div
      className={cn('p-6', className)}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      {/* Header */}
      <motion.div
        className="flex items-center justify-center space-x-3 mb-8"
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.1 }}
      >
        <LoadingSpinner variant="orbit" size="md" color="primary" />
        <span className="text-lg font-medium text-foreground">
          Loading your study groups...
        </span>
      </motion.div>

      {/* Grid Skeleton */}
      <motion.div
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="bg-card border rounded-lg p-4 space-y-4"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 0.3 + i * 0.05 }}
          >
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-muted rounded-full animate-pulse" />
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-muted rounded animate-pulse" />
                <div className="h-3 bg-muted rounded w-2/3 animate-pulse" />
              </div>
            </div>
            <div className="space-y-2">
              <div className="h-3 bg-muted rounded animate-pulse" />
              <div className="h-3 bg-muted rounded w-4/5 animate-pulse" />
            </div>
            <div className="flex justify-between items-center">
              <div className="h-6 bg-muted rounded w-16 animate-pulse" />
              <div className="h-8 bg-muted rounded w-20 animate-pulse" />
            </div>
          </motion.div>
        ))}
      </motion.div>
    </motion.div>
  );
};