import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  BarElement,
} from 'chart.js';
import { Line, Bar } from 'react-chartjs-2';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { MockTest } from '@/types/mockTest';
import { Clock, TrendingUp, Calendar } from 'lucide-react';
import { format, parseISO, startOfWeek, startOfMonth, endOfWeek, endOfMonth } from 'date-fns';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  BarElement
);

interface TimeAnalyticsChartProps {
  tests: MockTest[];
}

export function TimeAnalyticsChart({ tests }: TimeAnalyticsChartProps) {
  // Sort tests by date
  const sortedTests = React.useMemo(() => {
    return tests
      .filter(test => test.date)
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }, [tests]);

  // Weekly performance data
  const weeklyData = React.useMemo(() => {
    const weekMap = new Map<string, { total: number; obtained: number; count: number }>();
    
    sortedTests.forEach(test => {
      const testDate = new Date(test.date);
      const weekStart = startOfWeek(testDate);
      const weekKey = format(weekStart, 'yyyy-MM-dd');
      
      const existing = weekMap.get(weekKey) || { total: 0, obtained: 0, count: 0 };
      weekMap.set(weekKey, {
        total: existing.total + (test.totalMarks || 0),
        obtained: existing.obtained + (test.marksObtained || 0),
        count: existing.count + 1
      });
    });

    return Array.from(weekMap.entries())
      .map(([weekKey, data]) => ({
        week: format(new Date(weekKey), 'MMM dd'),
        percentage: data.total > 0 ? (data.obtained / data.total) * 100 : 0,
        testCount: data.count,
        date: new Date(weekKey)
      }))
      .sort((a, b) => a.date.getTime() - b.date.getTime());
  }, [sortedTests]);

  // Monthly performance data
  const monthlyData = React.useMemo(() => {
    const monthMap = new Map<string, { total: number; obtained: number; count: number }>();
    
    sortedTests.forEach(test => {
      const testDate = new Date(test.date);
      const monthStart = startOfMonth(testDate);
      const monthKey = format(monthStart, 'yyyy-MM');
      
      const existing = monthMap.get(monthKey) || { total: 0, obtained: 0, count: 0 };
      monthMap.set(monthKey, {
        total: existing.total + (test.totalMarks || 0),
        obtained: existing.obtained + (test.marksObtained || 0),
        count: existing.count + 1
      });
    });

    return Array.from(monthMap.entries())
      .map(([monthKey, data]) => ({
        month: format(new Date(monthKey + '-01'), 'MMM yyyy'),
        percentage: data.total > 0 ? (data.obtained / data.total) * 100 : 0,
        testCount: data.count,
        date: new Date(monthKey + '-01')
      }))
      .sort((a, b) => a.date.getTime() - b.date.getTime());
  }, [sortedTests]);

  const weeklyLineData = {
    labels: weeklyData.map(item => item.week),
    datasets: [
      {
        label: 'Weekly Performance (%)',
        data: weeklyData.map(item => item.percentage),
        borderColor: 'rgb(139, 92, 246)',
        backgroundColor: 'rgba(139, 92, 246, 0.1)',
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: 'rgb(139, 92, 246)',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 6,
        pointHoverRadius: 8,
      }
    ]
  };

  const monthlyBarData = {
    labels: monthlyData.map(item => item.month),
    datasets: [
      {
        label: 'Tests Taken',
        data: monthlyData.map(item => item.testCount),
        backgroundColor: 'rgba(6, 182, 212, 0.8)',
        borderColor: 'rgb(6, 182, 212)',
        borderWidth: 2,
        borderRadius: 8,
        borderSkipped: false,
      }
    ]
  };

  const lineOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `Performance: ${context.parsed.y.toFixed(1)}%`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        ticks: {
          callback: function(value: any) {
            return value + '%';
          }
        }
      }
    }
  };

  const barOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `Tests: ${context.parsed.y}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          stepSize: 1
        }
      }
    }
  };

  if (sortedTests.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Time Analytics
          </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <p className="text-muted-foreground">No test data available for time analysis</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden border border-border shadow-xl bg-card hover:shadow-2xl hover:-translate-y-1 transition-all duration-500 group relative">
      <div className="absolute inset-0 bg-muted/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      <CardHeader className="bg-muted/30 border-b border-border relative z-10">
        <CardTitle className="flex items-center gap-3 text-xl font-bold">
          <div className="p-2 rounded-lg bg-emerald-600 shadow-lg">
            <Clock className="h-6 w-6 text-white" />
          </div>
          <span className="text-foreground">
            Performance Over Time
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6 relative z-10">
        <Tabs defaultValue="weekly" className="w-full">
          <TabsList className="grid w-full grid-cols-2 bg-muted/30 border border-border shadow-sm">
            <TabsTrigger
              value="weekly"
              className="gap-2 data-[state=active]:bg-emerald-600 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-300"
            >
              <TrendingUp className="h-4 w-4" />
              Weekly Trend
            </TabsTrigger>
            <TabsTrigger
              value="monthly"
              className="gap-2 data-[state=active]:bg-violet-600 data-[state=active]:text-white data-[state=active]:shadow-md transition-all duration-300"
            >
              <Calendar className="h-4 w-4" />
              Monthly Activity
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="weekly" className="mt-6">
            <div className="h-96 p-4 rounded-lg bg-muted/30 border border-border shadow-inner">
              <Line data={weeklyLineData} options={lineOptions} />
            </div>
            <div className="mt-6 grid gap-4 md:grid-cols-3">
              <div className="p-5 rounded-xl border border-border shadow-lg bg-card text-center hover:shadow-xl transition-all duration-300">
                <p className="text-sm text-emerald-600 dark:text-emerald-400 font-medium">Best Week</p>
                <p className="text-3xl font-bold text-emerald-600 dark:text-emerald-400">
                  {Math.max(...weeklyData.map(w => w.percentage)).toFixed(1)}%
                </p>
              </div>
              <div className="p-5 rounded-xl border border-border shadow-lg bg-card text-center hover:shadow-xl transition-all duration-300">
                <p className="text-sm text-muted-foreground font-medium">Average</p>
                <p className="text-3xl font-bold text-foreground">
                  {(weeklyData.reduce((sum, w) => sum + w.percentage, 0) / weeklyData.length).toFixed(1)}%
                </p>
              </div>
              <div className="p-5 rounded-xl border border-border shadow-lg bg-card text-center hover:shadow-xl transition-all duration-300">
                <p className="text-sm text-violet-600 dark:text-violet-400 font-medium">Total Weeks</p>
                <p className="text-3xl font-bold text-violet-600 dark:text-violet-400">
                  {weeklyData.length}
                </p>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="monthly" className="mt-6">
            <div className="h-96 p-4 rounded-lg bg-muted/30 border border-border shadow-inner">
              <Bar data={monthlyBarData} options={barOptions} />
            </div>
            <div className="mt-6 grid gap-4 md:grid-cols-3">
              <div className="p-5 rounded-xl border border-border shadow-lg bg-card text-center hover:shadow-xl transition-all duration-300">
                <p className="text-sm text-rose-600 dark:text-rose-400 font-medium">Most Active Month</p>
                <p className="text-3xl font-bold text-rose-600 dark:text-rose-400">
                  {Math.max(...monthlyData.map(m => m.testCount))} tests
                </p>
              </div>
              <div className="p-5 rounded-xl border border-border shadow-lg bg-card text-center hover:shadow-xl transition-all duration-300">
                <p className="text-sm text-emerald-600 dark:text-emerald-400 font-medium">Avg Tests/Month</p>
                <p className="text-3xl font-bold text-emerald-600 dark:text-emerald-400">
                  {(monthlyData.reduce((sum, m) => sum + m.testCount, 0) / monthlyData.length).toFixed(1)}
                </p>
              </div>
              <div className="p-5 rounded-xl border border-border shadow-lg bg-card text-center hover:shadow-xl transition-all duration-300">
                <p className="text-sm text-violet-600 dark:text-violet-400 font-medium">Active Months</p>
                <p className="text-3xl font-bold text-violet-600 dark:text-violet-400">
                  {monthlyData.length}
                </p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
