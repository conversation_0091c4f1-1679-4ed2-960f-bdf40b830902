import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  LoadingScreen,
  LoadingSpinner,
  LoadingSkeleton,
  EnhancedProgress,
  AILoadingScreen,
  AnalyticsLoadingScreen,
  AuthLoadingScreen,
  GroupsLoadingScreen,
  LoadingStateManager,
  useLoadingState,
} from '@/components/loading';

const LoadingDemo: React.FC = () => {
  const [activeDemo, setActiveDemo] = useState<string | null>(null);
  const { isLoading, startLoading, stopLoading, LoadingComponent } = useLoadingState();

  const demoSections = [
    {
      id: 'spinners',
      title: 'Loading Spinners',
      description: 'Various animated spinner components',
      component: (
        <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
          {(['default', 'dots', 'pulse', 'orbit', 'wave'] as const).map((variant) => (
            <Card key={variant} className="p-6 text-center">
              <h4 className="font-medium mb-4 capitalize">{variant}</h4>
              <LoadingSpinner variant={variant} size="lg" />
            </Card>
          ))}
        </div>
      ),
    },
    {
      id: 'screens',
      title: 'Loading Screens',
      description: 'Full-screen loading experiences',
      component: (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {(['default', 'minimal', 'branded', 'gradient'] as const).map((variant) => (
            <Card key={variant} className="overflow-hidden">
              <CardHeader>
                <CardTitle className="capitalize">{variant} Screen</CardTitle>
              </CardHeader>
              <CardContent className="h-64 relative">
                <LoadingScreen
                  variant={variant}
                  title="Loading Demo"
                  subtitle="This is a preview"
                  className="absolute inset-0"
                />
              </CardContent>
            </Card>
          ))}
        </div>
      ),
    },
    {
      id: 'skeletons',
      title: 'Loading Skeletons',
      description: 'Content placeholder animations',
      component: (
        <div className="space-y-6">
          {(['card', 'list', 'table', 'chat', 'analytics', 'profile'] as const).map((variant) => (
            <Card key={variant}>
              <CardHeader>
                <CardTitle className="capitalize">{variant} Skeleton</CardTitle>
              </CardHeader>
              <CardContent>
                <LoadingSkeleton variant={variant} count={1} />
              </CardContent>
            </Card>
          ))}
        </div>
      ),
    },
    {
      id: 'progress',
      title: 'Progress Indicators',
      description: 'Enhanced progress bars and indicators',
      component: (
        <div className="space-y-6">
          {(['default', 'gradient', 'animated', 'stepped'] as const).map((variant) => (
            <Card key={variant} className="p-6">
              <h4 className="font-medium mb-4 capitalize">{variant} Progress</h4>
              <EnhancedProgress
                value={65}
                variant={variant}
                label={`${variant} progress`}
                showPercentage
                steps={variant === 'stepped' ? 5 : undefined}
              />
            </Card>
          ))}
        </div>
      ),
    },
    {
      id: 'specialized',
      title: 'Specialized Screens',
      description: 'Context-specific loading experiences',
      component: (
        <div className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>AI Loading Screen</CardTitle>
              <p className="text-sm text-muted-foreground">For AI chat and processing</p>
            </CardHeader>
            <CardContent className="h-64 relative">
              <AILoadingScreen variant="thinking" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Analytics Loading Screen</CardTitle>
              <p className="text-sm text-muted-foreground">For data visualization loading</p>
            </CardHeader>
            <CardContent className="h-96 relative overflow-hidden">
              <AnalyticsLoadingScreen />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Auth Loading Screen</CardTitle>
              <p className="text-sm text-muted-foreground">For authentication processes</p>
            </CardHeader>
            <CardContent className="h-64 relative">
              <AuthLoadingScreen variant="login" />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Groups Loading Screen</CardTitle>
              <p className="text-sm text-muted-foreground">For study groups and social features</p>
            </CardHeader>
            <CardContent className="h-64 relative overflow-hidden">
              <GroupsLoadingScreen variant="grid" />
            </CardContent>
          </Card>
        </div>
      ),
    },
    {
      id: 'manager',
      title: 'Loading State Manager',
      description: 'Centralized loading state management',
      component: (
        <div className="space-y-6">
          <Card className="p-6">
            <h4 className="font-medium mb-4">Interactive Demo</h4>
            <div className="flex flex-wrap gap-2 mb-4">
              {(['default', 'ai', 'analytics', 'auth', 'groups', 'tasks'] as const).map((type) => (
                <Button
                  key={type}
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    startLoading(type, `Loading ${type}...`);
                    setTimeout(stopLoading, 3000);
                  }}
                  className="capitalize"
                >
                  {type} Loading
                </Button>
              ))}
            </div>
            <div className="h-64 border rounded-lg relative overflow-hidden">
              {isLoading ? (
                <LoadingComponent />
              ) : (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  Click a button above to see the loading state
                </div>
              )}
            </div>
          </Card>
        </div>
      ),
    },
  ];

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto py-8 px-4">
        {/* Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
            Beautiful Loading Screens
          </h1>
          <p className="text-xl text-muted-foreground mb-6">
            Modern, animated, and aesthetic loading components for IsotopeAI
          </p>
          <div className="flex justify-center space-x-2">
            <Badge variant="secondary">Framer Motion</Badge>
            <Badge variant="secondary">Tailwind CSS</Badge>
            <Badge variant="secondary">React</Badge>
            <Badge variant="secondary">TypeScript</Badge>
          </div>
        </motion.div>

        {/* Demo Sections */}
        <Tabs defaultValue="spinners" className="space-y-8">
          <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6">
            {demoSections.map((section) => (
              <TabsTrigger key={section.id} value={section.id} className="text-xs">
                {section.title.split(' ')[0]}
              </TabsTrigger>
            ))}
          </TabsList>

          {demoSections.map((section) => (
            <TabsContent key={section.id} value={section.id}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card>
                  <CardHeader>
                    <CardTitle>{section.title}</CardTitle>
                    <p className="text-muted-foreground">{section.description}</p>
                  </CardHeader>
                  <CardContent>{section.component}</CardContent>
                </Card>
              </motion.div>
            </TabsContent>
          ))}
        </Tabs>

        {/* Features */}
        <motion.div
          className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Card className="text-center p-6">
            <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
              <span className="text-primary font-bold text-xl">🎨</span>
            </div>
            <h3 className="font-semibold mb-2">Beautiful Design</h3>
            <p className="text-sm text-muted-foreground">
              Modern, aesthetic loading screens that match your brand
            </p>
          </Card>

          <Card className="text-center p-6">
            <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
              <span className="text-primary font-bold text-xl">⚡</span>
            </div>
            <h3 className="font-semibold mb-2">Smooth Animations</h3>
            <p className="text-sm text-muted-foreground">
              Powered by Framer Motion for buttery smooth animations
            </p>
          </Card>

          <Card className="text-center p-6">
            <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
              <span className="text-primary font-bold text-xl">🔧</span>
            </div>
            <h3 className="font-semibold mb-2">Easy to Use</h3>
            <p className="text-sm text-muted-foreground">
              Simple API with TypeScript support and customizable options
            </p>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};

export default LoadingDemo;