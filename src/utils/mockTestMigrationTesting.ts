import { MockTest } from '@/types/mockTest';
import { mockTestMigrationService } from './mockTestMigration';
import { enhancedMockTestUtils } from './mockTestLocalStorage';

/**
 * Testing utilities for mock test migration
 * These functions help test various migration scenarios
 */
export class MockTestMigrationTesting {
  private static instance: MockTestMigrationTesting;

  static getInstance(): MockTestMigrationTesting {
    if (!MockTestMigrationTesting.instance) {
      MockTestMigrationTesting.instance = new MockTestMigrationTesting();
    }
    return MockTestMigrationTesting.instance;
  }

  /**
   * Create a sample mock test for testing
   */
  createSampleTest(overrides: Partial<MockTest> = {}): MockTest {
    const baseTest: MockTest = {
      id: `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: 'Sample Test',
      date: '2024-01-15',
      userId: 'test-user-id',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      subjectMarks: [
        {
          subject: 'Mathematics',
          marksObtained: 85,
          totalMarks: 100,
          subjectColor: '#14b8a6'
        },
        {
          subject: 'Physics',
          marksObtained: 78,
          totalMarks: 100,
          subjectColor: '#4f46e5'
        }
      ],
      totalMarksObtained: 163,
      totalMarks: 200,
      notes: 'Sample test notes',
      testType: 'mock',
      status: 'completed',
      isReviewed: false,
      analysisCompleted: false,
      mistakesAnalyzed: false,
      takeawaysRecorded: false,
      mistakes: [],
      takeaways: [],
      difficulty: null,
      ...overrides
    };

    return baseTest;
  }

  /**
   * Create sample Supabase data format
   */
  createSampleSupabaseData(overrides: any = {}): any {
    return {
      id: `supabase-test-${Date.now()}`,
      user_id: 'test-user-id',
      name: 'Supabase Test',
      test_date: '2024-01-10',
      subject_marks: [
        {
          subject: 'Chemistry',
          marksObtained: 92,
          totalMarks: 100,
          subjectColor: '#ec4899'
        }
      ],
      total_marks_obtained: 92,
      total_marks: 100,
      notes: 'Test from Supabase',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ...overrides
    };
  }

  /**
   * Test scenario: User with only Supabase data
   */
  async testSupabaseOnlyScenario(userId: string): Promise<{ success: boolean; details: any }> {
    console.log('🧪 Testing: User with only Supabase data');
    
    try {
      // Clear any existing local data
      this.clearLocalData(userId);
      
      // Reset migration status
      mockTestMigrationService.resetMigrationStatus(userId);
      
      // Simulate migration
      const result = await mockTestMigrationService.migrateUserData(userId);
      
      // Check results
      const localTests = enhancedMockTestUtils.getAll(userId);
      
      return {
        success: result.success,
        details: {
          migrationResult: result,
          localTestsAfter: localTests.length,
          migrationStatus: mockTestMigrationService.getMigrationStatus(userId)
        }
      };
    } catch (error) {
      return {
        success: false,
        details: { error: error.message }
      };
    }
  }

  /**
   * Test scenario: User with only local data
   */
  async testLocalOnlyScenario(userId: string): Promise<{ success: boolean; details: any }> {
    console.log('🧪 Testing: User with only local data');
    
    try {
      // Clear migration status
      mockTestMigrationService.resetMigrationStatus(userId);
      
      // Add some local test data
      const localTest = this.createSampleTest({ userId });
      enhancedMockTestUtils.save(userId, localTest);
      
      const localTestsBefore = enhancedMockTestUtils.getAll(userId);
      
      // Simulate migration (should find no Supabase data)
      const result = await mockTestMigrationService.migrateUserData(userId);
      
      // Check results
      const localTestsAfter = enhancedMockTestUtils.getAll(userId);
      
      return {
        success: result.success,
        details: {
          migrationResult: result,
          localTestsBefore: localTestsBefore.length,
          localTestsAfter: localTestsAfter.length,
          dataPreserved: localTestsBefore.length === localTestsAfter.length,
          migrationStatus: mockTestMigrationService.getMigrationStatus(userId)
        }
      };
    } catch (error) {
      return {
        success: false,
        details: { error: error.message }
      };
    }
  }

  /**
   * Test scenario: User with both Supabase and local data (conflict resolution)
   */
  async testConflictResolutionScenario(userId: string): Promise<{ success: boolean; details: any }> {
    console.log('🧪 Testing: Conflict resolution between Supabase and local data');
    
    try {
      // Clear migration status
      mockTestMigrationService.resetMigrationStatus(userId);
      
      // Create a test with same ID in both sources
      const testId = 'conflict-test-123';
      
      // Add local test with enhancements
      const localTest = this.createSampleTest({
        id: testId,
        userId,
        name: 'Local Enhanced Test',
        isReviewed: true,
        analysisCompleted: true,
        categoryId: 'local-category',
        updatedAt: new Date(Date.now() - 1000 * 60 * 60).toISOString() // 1 hour ago
      });
      enhancedMockTestUtils.save(userId, localTest);
      
      const localTestsBefore = enhancedMockTestUtils.getAll(userId);
      
      // Simulate migration with conflicting Supabase data
      // Note: In real testing, this would involve mocking the Supabase call
      const result = await mockTestMigrationService.migrateUserData(userId);
      
      const localTestsAfter = enhancedMockTestUtils.getAll(userId);
      const conflictTest = localTestsAfter.find(t => t.id === testId);
      
      return {
        success: result.success,
        details: {
          migrationResult: result,
          localTestsBefore: localTestsBefore.length,
          localTestsAfter: localTestsAfter.length,
          conflictResolved: !!conflictTest,
          enhancementsPreserved: conflictTest?.isReviewed && conflictTest?.analysisCompleted,
          migrationStatus: mockTestMigrationService.getMigrationStatus(userId)
        }
      };
    } catch (error) {
      return {
        success: false,
        details: { error: error.message }
      };
    }
  }

  /**
   * Test scenario: Network failure during migration
   */
  async testNetworkFailureScenario(userId: string): Promise<{ success: boolean; details: any }> {
    console.log('🧪 Testing: Network failure during migration');
    
    try {
      // Clear migration status
      mockTestMigrationService.resetMigrationStatus(userId);
      
      // Add some local data first
      const localTest = this.createSampleTest({ userId });
      enhancedMockTestUtils.save(userId, localTest);
      
      const localTestsBefore = enhancedMockTestUtils.getAll(userId);
      
      // Simulate migration (network failure would be handled gracefully)
      const result = await mockTestMigrationService.migrateUserData(userId);
      
      const localTestsAfter = enhancedMockTestUtils.getAll(userId);
      
      return {
        success: true, // Should succeed even with network failure
        details: {
          migrationResult: result,
          localTestsBefore: localTestsBefore.length,
          localTestsAfter: localTestsAfter.length,
          dataPreserved: localTestsBefore.length === localTestsAfter.length,
          gracefulFailure: !result.success, // Migration should fail gracefully
          migrationStatus: mockTestMigrationService.getMigrationStatus(userId)
        }
      };
    } catch (error) {
      return {
        success: false,
        details: { error: error.message }
      };
    }
  }

  /**
   * Run all migration tests
   */
  async runAllTests(userId: string = 'test-user-migration'): Promise<{ 
    overall: boolean; 
    results: { [testName: string]: { success: boolean; details: any } } 
  }> {
    console.log('🧪 Running comprehensive migration tests...');
    
    const results: { [testName: string]: { success: boolean; details: any } } = {};
    
    // Test 1: Supabase only
    results.supabaseOnly = await this.testSupabaseOnlyScenario(userId + '-supabase');
    
    // Test 2: Local only
    results.localOnly = await this.testLocalOnlyScenario(userId + '-local');
    
    // Test 3: Conflict resolution
    results.conflictResolution = await this.testConflictResolutionScenario(userId + '-conflict');
    
    // Test 4: Network failure
    results.networkFailure = await this.testNetworkFailureScenario(userId + '-network');
    
    const overall = Object.values(results).every(result => result.success);
    
    console.log('🧪 Migration tests completed:', { overall, results });
    
    return { overall, results };
  }

  /**
   * Clear local data for testing
   */
  private clearLocalData(userId: string): void {
    try {
      localStorage.removeItem(enhancedMockTestUtils.getStorageKey(userId));
      console.log(`🧹 Cleared local data for user: ${userId}`);
    } catch (error) {
      console.error('Error clearing local data:', error);
    }
  }

  /**
   * Validate migration data integrity
   */
  validateMigrationIntegrity(originalData: any[], migratedTests: MockTest[]): {
    isValid: boolean;
    issues: string[];
  } {
    const issues: string[] = [];
    
    // Check if all original tests were migrated
    if (originalData.length !== migratedTests.length) {
      issues.push(`Count mismatch: ${originalData.length} original vs ${migratedTests.length} migrated`);
    }
    
    // Check data integrity for each test
    for (const original of originalData) {
      const migrated = migratedTests.find(t => t.id === original.id);
      if (!migrated) {
        issues.push(`Missing migrated test: ${original.id}`);
        continue;
      }
      
      // Validate key fields
      if (migrated.name !== original.name) {
        issues.push(`Name mismatch for ${original.id}: ${original.name} vs ${migrated.name}`);
      }
      
      if (migrated.totalMarksObtained !== original.total_marks_obtained) {
        issues.push(`Marks mismatch for ${original.id}: ${original.total_marks_obtained} vs ${migrated.totalMarksObtained}`);
      }
      
      // Check if migration metadata was added
      if (!migrated.isFromSupabase) {
        issues.push(`Missing migration metadata for ${original.id}`);
      }
    }
    
    return {
      isValid: issues.length === 0,
      issues
    };
  }
}

// Export singleton instance
export const migrationTesting = MockTestMigrationTesting.getInstance();
