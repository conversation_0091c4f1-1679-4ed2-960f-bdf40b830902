import React, { useState, useRef } from 'react';
import { motion, PanInfo, useMotionValue, useTransform } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { 
  CheckCircle2, 
  Trash2, 
  Edit, 
  Star, 
  Archive,
  Clock,
  AlertTriangle 
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SwipeAction {
  id: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  color: string;
  bgColor: string;
  action: () => void;
}

interface MobileSwipeActionsProps {
  children: React.ReactNode;
  onComplete?: (e?: React.MouseEvent) => void;
  onDelete?: () => void;
  onEdit?: () => void;
  onTogglePriority?: () => void;
  isCompleted?: boolean;
  className?: string;
}

export function MobileSwipeActions({
  children,
  onComplete,
  onDelete,
  onEdit,
  onTogglePriority,
  isCompleted = false,
  className,
}: MobileSwipeActionsProps) {
  const [isRevealed, setIsRevealed] = useState(false);
  const [swipeDirection, setSwipeDirection] = useState<'left' | 'right' | null>(null);
  const constraintsRef = useRef(null);
  const x = useMotionValue(0);
  
  // Transform values for background colors
  const leftBg = useTransform(x, [-150, -75, 0], ['#ef4444', '#ef444440', '#00000000']);
  const rightBg = useTransform(x, [0, 75, 150], ['#00000000', '#10b98140', '#10b981']);

  // Define swipe actions
  const leftActions: SwipeAction[] = [
    {
      id: 'delete',
      icon: Trash2,
      label: 'Delete',
      color: 'text-white',
      bgColor: 'bg-red-500',
      action: () => onDelete?.(),
    },
    {
      id: 'priority',
      icon: AlertTriangle,
      label: 'Priority',
      color: 'text-white',
      bgColor: 'bg-orange-500',
      action: () => onTogglePriority?.(),
    },
  ];

  const rightActions: SwipeAction[] = [
    {
      id: 'complete',
      icon: isCompleted ? Clock : CheckCircle2,
      label: isCompleted ? 'Undo' : 'Complete',
      color: 'text-white',
      bgColor: isCompleted ? 'bg-gray-500' : 'bg-green-500',
      action: () => onComplete?.(),
    },
    {
      id: 'edit',
      icon: Edit,
      label: 'Edit',
      color: 'text-white',
      bgColor: 'bg-blue-500',
      action: () => onEdit?.(),
    },
  ];

  const handleDragEnd = (event: any, info: PanInfo) => {
    const threshold = 75;
    const velocity = info.velocity.x;
    const offset = info.offset.x;

    // Determine if swipe was significant enough
    if (Math.abs(offset) > threshold || Math.abs(velocity) > 500) {
      if (offset < 0) {
        // Swiped left - show left actions
        setSwipeDirection('left');
        setIsRevealed(true);
      } else {
        // Swiped right - show right actions
        setSwipeDirection('right');
        setIsRevealed(true);
      }
    } else {
      // Reset to center
      setIsRevealed(false);
      setSwipeDirection(null);
      x.set(0);
    }
  };

  const handleActionClick = (action: SwipeAction) => {
    action.action();
    // Reset after action
    setIsRevealed(false);
    setSwipeDirection(null);
    x.set(0);
  };

  const resetSwipe = () => {
    setIsRevealed(false);
    setSwipeDirection(null);
    x.set(0);
  };

  // Only enable on mobile devices
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;

  if (!isMobile) {
    return <div className={className}>{children}</div>;
  }

  return (
    <div className={cn("relative overflow-hidden", className)} ref={constraintsRef}>
      {/* Background Actions */}
      <motion.div
        className="absolute inset-0 flex items-center justify-between px-4"
        style={{ backgroundColor: swipeDirection === 'left' ? leftBg : rightBg }}
      >
        {/* Left Actions */}
        <div className="flex items-center gap-2">
          {swipeDirection === 'left' && leftActions.map((action) => (
            <motion.div
              key={action.id}
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.1 }}
            >
              <Button
                size="sm"
                className={cn(
                  "h-10 w-10 p-0 rounded-full shadow-lg",
                  action.bgColor,
                  action.color
                )}
                onClick={() => handleActionClick(action)}
              >
                <action.icon className="h-4 w-4" />
              </Button>
            </motion.div>
          ))}
        </div>

        {/* Right Actions */}
        <div className="flex items-center gap-2">
          {swipeDirection === 'right' && rightActions.map((action) => (
            <motion.div
              key={action.id}
              initial={{ scale: 0, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.1 }}
            >
              <Button
                size="sm"
                className={cn(
                  "h-10 w-10 p-0 rounded-full shadow-lg",
                  action.bgColor,
                  action.color
                )}
                onClick={() => handleActionClick(action)}
              >
                <action.icon className="h-4 w-4" />
              </Button>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Draggable Content */}
      <motion.div
        drag="x"
        dragConstraints={{ left: -150, right: 150 }}
        dragElastic={0.2}
        onDragEnd={handleDragEnd}
        style={{ x }}
        className="relative z-10 bg-background"
        whileTap={{ scale: 0.98 }}
        onClick={resetSwipe}
      >
        {children}
      </motion.div>

      {/* Swipe Hint Overlay */}
      {!isRevealed && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 pointer-events-none flex items-center justify-center"
        >
          <div className="bg-black/50 text-white text-xs px-2 py-1 rounded-full opacity-0 animate-pulse">
            ← Swipe for actions →
          </div>
        </motion.div>
      )}
    </div>
  );
}

// Hook for detecting mobile device
export function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false);

  React.useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);

    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  return isMobile;
}

// Mobile-optimized touch targets
export const MobileTouchTarget = ({ 
  children, 
  className,
  ...props 
}: React.ButtonHTMLAttributes<HTMLButtonElement> & { children: React.ReactNode }) => (
  <button
    className={cn(
      "min-h-[44px] min-w-[44px] touch-manipulation",
      "flex items-center justify-center",
      "active:scale-95 transition-transform duration-150",
      className
    )}
    {...props}
  >
    {children}
  </button>
);

// Mobile-optimized input with larger touch targets
export const MobileInput = React.forwardRef<
  HTMLInputElement,
  React.InputHTMLAttributes<HTMLInputElement>
>(({ className, ...props }, ref) => (
  <input
    ref={ref}
    className={cn(
      "min-h-[44px] px-4 py-3 text-base", // Larger touch target and text
      "border border-input bg-background",
      "rounded-md focus:outline-none focus:ring-2 focus:ring-ring",
      "touch-manipulation",
      className
    )}
    {...props}
  />
));

MobileInput.displayName = "MobileInput";

// Mobile-optimized select
export const MobileSelect = React.forwardRef<
  HTMLSelectElement,
  React.SelectHTMLAttributes<HTMLSelectElement>
>(({ className, children, ...props }, ref) => (
  <select
    ref={ref}
    className={cn(
      "min-h-[44px] px-4 py-3 text-base", // Larger touch target
      "border border-input bg-background",
      "rounded-md focus:outline-none focus:ring-2 focus:ring-ring",
      "touch-manipulation appearance-none",
      className
    )}
    {...props}
  >
    {children}
  </select>
));

MobileSelect.displayName = "MobileSelect";
