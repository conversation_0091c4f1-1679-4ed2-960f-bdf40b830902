
import { motion } from "framer-motion";
import { 
  Eye, 
  Calendar, 
  Target, 
  TrendingUp, 
  TrendingDown,
  Award,
  Clock,
  FileText,
  ExternalLink,
  Filter,
  SortAsc,
  SortDesc,
  CheckCircle2,
  XCircle,
  AlertCircle
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { MockTest, TestCategory } from "@/types/mockTest";
import { categoryStorage } from "@/utils/mockTestLocalStorage";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { cn } from "@/lib/utils";

interface EnhancedScoreTableProps {
  tests: MockTest[]; // Now receives already filtered and sorted tests
  categories: TestCategory[];
  onViewDetails: (test: MockTest) => void;
}

export function EnhancedScoreTable({ tests, categories, onViewDetails }: EnhancedScoreTableProps) {
  const { user } = useSupabaseAuth();

  // No local state for search/filters/sort, as it's managed by parent MockTestAnalysis.tsx
  // No useMemo for filteredAndSortedTests, as tests prop is already processed.

  const getCategoryName = (categoryId?: string) => {
    if (!categoryId) return null;
    return categories.find(cat => cat.id === categoryId)?.name;
  };

  const getCategoryColor = (categoryId?: string) => {
    if (!categoryId) return '#6b7280';
    return categories.find(cat => cat.id === categoryId)?.color || '#6b7280';
  };

  const getPercentageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-emerald-600';
    if (percentage >= 75) return 'text-emerald-500';
    if (percentage >= 60) return 'text-yellow-600';
    if (percentage >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  const getPerformanceBadge = (percentage: number) => {
    if (percentage >= 90) return { label: 'Excellent', variant: 'default' as const, color: 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300' };
    if (percentage >= 75) return { label: 'Good', variant: 'secondary' as const, color: 'bg-emerald-50 text-emerald-700 dark:bg-emerald-900/20 dark:text-emerald-400' };
    if (percentage >= 60) return { label: 'Average', variant: 'outline' as const, color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' };
    if (percentage >= 40) return { label: 'Below Average', variant: 'outline' as const, color: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' };
    return { label: 'Poor', variant: 'destructive' as const, color: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' };
  };



  if (!user) {
    return <div>Please log in to view test scores.</div>;
  }

  return (
    <div className="space-y-6">
      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Showing {tests.length} of {tests.length} tests
        </p>
        {tests.length > 0 && (
          <div className="flex items-center gap-4 text-sm">
            <span>
              Average: {(tests.reduce((sum, test) => {
                if (test.marksObtained && test.totalMarks) {
                  return sum + (test.marksObtained / test.totalMarks) * 100;
                }
                return sum;
              }, 0) / tests.filter(test => test.marksObtained && test.totalMarks).length).toFixed(1)}%
            </span>
          </div>
        )}
      </div>

      {/* Table */}
      {tests.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No tests found</h3>
            <p className="text-muted-foreground text-center">
              "Complete some mock tests to see your scores here"
            </p>
          </CardContent>
        </Card>
      ) : (
        <Card className="overflow-hidden border border-border shadow-xl bg-card">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-muted/30">
                  <TableRow className="border-b border-border">
                    <TableHead>
                      Test Name
                    </TableHead>
                    <TableHead>
                      Date
                    </TableHead>
                    <TableHead>
                      Category
                    </TableHead>
                    <TableHead className="text-center">
                      Score
                    </TableHead>
                    <TableHead className="text-center">Performance</TableHead>
                    <TableHead className="text-center">Review Status</TableHead>
                    <TableHead className="text-center">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tests.map((test, index) => {
                    const percentage = test.marksObtained && test.totalMarks 
                      ? (test.marksObtained / test.totalMarks) * 100 
                      : 0;
                    const performanceBadge = getPerformanceBadge(percentage);
                    const categoryName = getCategoryName(test.categoryId);
                    const categoryColor = getCategoryColor(test.categoryId);

                    return (
                      <motion.tr
                        key={test.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.05 }}
                        className="hover:bg-muted/50"
                      >
                        <TableCell>
                          <div>
                            <div className="font-medium line-clamp-1">{test.name}</div>
                            {test.testPaperUrl && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-auto p-0 text-xs text-blue-600 hover:text-blue-800"
                                onClick={() => window.open(test.testPaperUrl, '_blank')}
                              >
                                <ExternalLink className="h-3 w-3 mr-1" />
                                View Paper
                              </Button>
                            )}
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          <div className="flex items-center gap-2 text-sm">
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                            {new Date(test.date).toLocaleDateString()}
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          {categoryName ? (
                            <Badge 
                              variant="outline" 
                              className="text-xs"
                              style={{ 
                                borderColor: categoryColor,
                                color: categoryColor 
                              }}
                            >
                              {categoryName}
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="text-xs text-muted-foreground">
                              Uncategorized
                            </Badge>
                          )}
                        </TableCell>
                        
                        <TableCell className="text-center">
                          {test.marksObtained !== undefined && test.totalMarks !== undefined ? (
                            <div className="space-y-1">
                              <div className="font-semibold">
                                {test.marksObtained}/{test.totalMarks}
                              </div>
                              <div className={cn("text-sm font-medium", getPercentageColor(percentage))}>
                                {percentage.toFixed(1)}%
                              </div>
                              <Progress value={percentage} className="h-1 w-16 mx-auto" />
                            </div>
                          ) : (
                            <span className="text-muted-foreground text-sm">Not recorded</span>
                          )}
                        </TableCell>
                        
                        <TableCell className="text-center">
                          {test.marksObtained !== undefined && test.totalMarks !== undefined ? (
                            <Badge className={performanceBadge.color}>
                              {performanceBadge.label}
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="text-muted-foreground">
                              N/A
                            </Badge>
                          )}
                        </TableCell>
                        
                        <TableCell className="text-center">
                          <div className="flex items-center justify-center">
                            {test.isReviewed ? (
                              <div className="flex items-center gap-1 text-green-600">
                                <CheckCircle2 className="h-4 w-4" />
                                <span className="text-xs">Reviewed</span>
                              </div>
                            ) : (
                              <div className="flex items-center gap-1 text-orange-600">
                                <AlertCircle className="h-4 w-4" />
                                <span className="text-xs">Pending</span>
                              </div>
                            )}
                          </div>
                        </TableCell>
                        
                        <TableCell className="text-center">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onViewDetails(test)}
                            className="gap-2"
                          >
                            <Eye className="h-4 w-4" />
                            Details
                          </Button>
                        </TableCell>
                      </motion.tr>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
