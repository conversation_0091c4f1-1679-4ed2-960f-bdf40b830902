import { useMemo } from "react";
import { motion } from "framer-motion";
import { 
  Bar<PERSON>hart3, 
  TrendingUp, 
  Clock, 
  Target,
  AlertTriangle,
  CheckCircle,
  BookOpen,
  Zap,
  Calendar,
  PieChart
} from "lucide-react";
import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { DDayExam, chapterProgressUtils, studyHoursCalculator } from "@/utils/mockTestLocalStorage";
import { useSupabaseAuth } from "@/contexts/SupabaseAuthContext";
import { cn } from "@/lib/utils";

interface TestInsightsPanelProps {
  exams: DDayExam[];
  stats: {
    total: number;
    upcoming: number;
    completed: number;
    missed: number;
    byPriority: {
      critical: number;
      high: number;
      medium: number;
      low: number;
    };
    averageStudyHours: number;
  };
}

export function TestInsightsPanel({ exams, stats }: TestInsightsPanelProps) {
  // Calculate insights
  const insights = useMemo(() => {
    const now = new Date();
    const upcomingExams = exams.filter(exam => exam.status === 'upcoming');
    
    // Time-based insights
    const thisWeek = upcomingExams.filter(exam => {
      const examDate = new Date(exam.date);
      const daysUntil = Math.ceil((examDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      return daysUntil <= 7;
    });

    const thisMonth = upcomingExams.filter(exam => {
      const examDate = new Date(exam.date);
      const daysUntil = Math.ceil((examDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      return daysUntil <= 30;
    });

    // Preparation insights
    const totalStudyHours = 0; // Will be calculated from study sessions
    const averageConfidence = exams.reduce((sum, exam) => {
      if (exam.preparationData.chapters.length === 0) return sum + 3; // Default confidence
      return sum + (exam.preparationData.chapters.reduce((chSum, ch) => chSum + ch.confidenceLevel, 0) / exam.preparationData.chapters.length);
    }, 0) / exams.length || 0;

    const wellPreparedExams = exams.filter(exam => {
      const overallProgress = chapterProgressUtils.calculateOverallProgress(exam.preparationData.chapters);
      const avgConfidence = exam.preparationData.chapters.length > 0
        ? exam.preparationData.chapters.reduce((sum, ch) => sum + ch.confidenceLevel, 0) / exam.preparationData.chapters.length
        : 3;
      return overallProgress >= 80 && avgConfidence >= 4;
    });

    const needsAttentionExams = upcomingExams.filter(exam => {
      const examDate = new Date(exam.date);
      const daysUntil = Math.ceil((examDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      const overallProgress = chapterProgressUtils.calculateOverallProgress(exam.preparationData.chapters);
      const avgConfidence = exam.preparationData.chapters.length > 0
        ? exam.preparationData.chapters.reduce((sum, ch) => sum + ch.confidenceLevel, 0) / exam.preparationData.chapters.length
        : 3;

      return daysUntil <= 7 && (overallProgress < 50 || avgConfidence < 3);
    });

    // Priority distribution
    const priorityDistribution = [
      { name: 'Critical', value: stats.byPriority.critical, color: 'bg-red-500' },
      { name: 'High', value: stats.byPriority.high, color: 'bg-orange-500' },
      { name: 'Medium', value: stats.byPriority.medium, color: 'bg-yellow-500' },
      { name: 'Low', value: stats.byPriority.low, color: 'bg-green-500' },
    ];

    return {
      thisWeek: thisWeek.length,
      thisMonth: thisMonth.length,
      totalStudyHours,
      averageConfidence,
      wellPreparedCount: wellPreparedExams.length,
      needsAttentionCount: needsAttentionExams.length,
      priorityDistribution,
      needsAttentionExams,
    };
  }, [exams, stats]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <motion.div variants={itemVariants}>
          <Card className="bg-violet-50 dark:bg-violet-950/20 border-violet-200 dark:border-violet-800">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-violet-600/10">
                  <Calendar className="h-5 w-5 text-violet-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-violet-700 dark:text-violet-400">{insights.thisWeek}</p>
                  <p className="text-sm text-violet-600/70 dark:text-violet-400/70">This Week</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={itemVariants}>
          <Card className="bg-emerald-50 dark:bg-emerald-950/20 border-emerald-200 dark:border-emerald-800">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-emerald-600/10">
                  <Clock className="h-5 w-5 text-emerald-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-emerald-700 dark:text-emerald-400">{Math.round(insights.totalStudyHours)}</p>
                  <p className="text-sm text-emerald-600/70 dark:text-emerald-400/70">Study Hours</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={itemVariants}>
          <Card className="bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-blue-600/10">
                  <Target className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-700 dark:text-blue-400">{insights.wellPreparedCount}</p>
                  <p className="text-sm text-blue-600/70 dark:text-blue-400/70">Well Prepared</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={itemVariants}>
          <Card className="bg-rose-50 dark:bg-rose-950/20 border-rose-200 dark:border-rose-800">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-rose-600/10">
                  <AlertTriangle className="h-5 w-5 text-rose-600" />
                </div>
                <div>
                  <p className="text-2xl font-bold text-rose-700 dark:text-rose-400">{insights.needsAttentionCount}</p>
                  <p className="text-sm text-rose-600/70 dark:text-rose-400/70">Needs Attention</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Detailed Insights */}
      <div className="grid grid-cols-1 gap-6">
        {/* Priority Distribution */}
        <motion.div variants={itemVariants}>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5 text-violet-600" />
                Priority Distribution
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {insights.priorityDistribution.map((priority, index) => (
                <div key={priority.name} className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium">{priority.name}</span>
                    <span className="text-muted-foreground">{priority.value} exams</span>
                  </div>
                  <Progress 
                    value={(priority.value / Math.max(stats.total, 1)) * 100} 
                    className="h-2"
                  />
                </div>
              ))}
            </CardContent>
          </Card>
        </motion.div>


      </div>

      {/* Needs Attention */}
      {insights.needsAttentionExams.length > 0 && (
        <motion.div variants={itemVariants}>
          <Card className="border-orange-200 dark:border-orange-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-orange-700 dark:text-orange-400">
                <AlertTriangle className="h-5 w-5" />
                Exams Needing Attention
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {insights.needsAttentionExams.map((exam) => {
                  const examDate = new Date(exam.date);
                  const daysUntil = Math.ceil((examDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
                  const overallProgress = chapterProgressUtils.calculateOverallProgress(exam.preparationData.chapters);
                  const avgConfidence = exam.preparationData.chapters.length > 0
                    ? exam.preparationData.chapters.reduce((sum, ch) => sum + ch.confidenceLevel, 0) / exam.preparationData.chapters.length
                    : 3;

                  return (
                    <div key={exam.id} className="flex items-center justify-between p-3 rounded-lg bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800">
                      <div className="flex-1">
                        <h4 className="font-medium text-orange-900 dark:text-orange-100">{exam.name}</h4>
                        <div className="flex items-center gap-4 mt-1 text-sm text-orange-700 dark:text-orange-300">
                          <span>{daysUntil} days left</span>
                          <span>{overallProgress}% prepared</span>
                          <span>Confidence: {Math.round(avgConfidence * 10) / 10}/5</span>
                        </div>
                      </div>
                      <Badge variant="outline" className="border-orange-300 text-orange-700 dark:border-orange-700 dark:text-orange-300">
                        {exam.priority}
                      </Badge>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Performance Metrics */}
      <motion.div variants={itemVariants}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-violet-600" />
              Performance Metrics
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 rounded-lg bg-violet-50 dark:bg-violet-950/20">
                <p className="text-2xl font-bold text-violet-600">{insights.averageConfidence.toFixed(1)}/5</p>
                <p className="text-sm text-muted-foreground">Avg Confidence</p>
              </div>
              
              <div className="text-center p-4 rounded-lg bg-emerald-50 dark:bg-emerald-950/20">
                <p className="text-2xl font-bold text-emerald-600">{Math.round(stats.averageStudyHours)}</p>
                <p className="text-sm text-muted-foreground">Avg Study Hours</p>
              </div>
              
              <div className="text-center p-4 rounded-lg bg-blue-50 dark:bg-blue-950/20">
                <p className="text-2xl font-bold text-blue-600">
                  {Math.round((stats.completed / Math.max(stats.total, 1)) * 100)}%
                </p>
                <p className="text-sm text-muted-foreground">Completion Rate</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
