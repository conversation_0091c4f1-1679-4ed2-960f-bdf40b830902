import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Settings, 
  Save, 
  RotateCcw, 
  Bell, 
  Eye, 
  Archive,
  Trash2,
  Download,
  Upload,
  Palette,
  Clock,
  Target
} from 'lucide-react';
import { useEnhancedTodoStore } from '@/stores/enhancedTodoStore';
import { useSupabaseAuth } from '@/contexts/SupabaseAuthContext';

interface TaskSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface TaskSettings {
  // Display settings
  showCompletedTasks: boolean;
  showSubtasks: boolean;
  showDueDates: boolean;
  showAssignees: boolean;
  showPriority: boolean;
  showProgress: boolean;
  
  // Notification settings
  enableNotifications: boolean;
  notifyOnDueDate: boolean;
  notifyOnAssignment: boolean;
  notifyOnCompletion: boolean;
  
  // Behavior settings
  autoArchiveCompleted: boolean;
  autoArchiveDays: number;
  defaultPriority: 'low' | 'medium' | 'high';
  defaultDifficulty: 'easy' | 'medium' | 'hard';
  
  // View settings
  defaultView: 'kanban' | 'table' | 'calendar';
  tasksPerPage: number;
  sortBy: 'dueDate' | 'priority' | 'createdAt' | 'title';
  sortOrder: 'asc' | 'desc';
}

const defaultSettings: TaskSettings = {
  showCompletedTasks: true,
  showSubtasks: true,
  showDueDates: true,
  showAssignees: true,
  showPriority: true,
  showProgress: true,
  enableNotifications: true,
  notifyOnDueDate: true,
  notifyOnAssignment: true,
  notifyOnCompletion: false,
  autoArchiveCompleted: false,
  autoArchiveDays: 30,
  defaultPriority: 'medium',
  defaultDifficulty: 'medium',
  defaultView: 'kanban',
  tasksPerPage: 50,
  sortBy: 'dueDate',
  sortOrder: 'asc',
};

export function TaskSettingsModal({ isOpen, onClose }: TaskSettingsModalProps) {
  const { user } = useSupabaseAuth();
  const [settings, setSettings] = useState<TaskSettings>(defaultSettings);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Load settings from localStorage on mount
  useEffect(() => {
    if (isOpen && user) {
      const savedSettings = localStorage.getItem(`task-settings-${user.id}`);
      if (savedSettings) {
        try {
          const parsed = JSON.parse(savedSettings);
          setSettings({ ...defaultSettings, ...parsed });
        } catch (error) {
          console.error('Failed to parse saved settings:', error);
          setSettings(defaultSettings);
        }
      }
    }
  }, [isOpen, user]);

  const handleSettingChange = (key: keyof TaskSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    if (!user) return;
    
    setIsSaving(true);
    try {
      localStorage.setItem(`task-settings-${user.id}`, JSON.stringify(settings));
      setHasChanges(false);
      
      // You could also save to Supabase here if needed
      // await saveUserSettings(user.id, settings);
      
    } catch (error) {
      console.error('Failed to save settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    setSettings(defaultSettings);
    setHasChanges(true);
  };

  const handleExportSettings = () => {
    const dataStr = JSON.stringify(settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'task-settings.json';
    link.click();
    URL.revokeObjectURL(url);
  };

  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const imported = JSON.parse(e.target?.result as string);
        setSettings({ ...defaultSettings, ...imported });
        setHasChanges(true);
      } catch (error) {
        console.error('Failed to import settings:', error);
        alert('Invalid settings file');
      }
    };
    reader.readAsText(file);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto bg-white/95 dark:bg-[#030303]/95 backdrop-blur-md border-gray-200 dark:border-gray-800">
        <DialogHeader>
          <DialogTitle className="font-onest text-xl text-gray-900 dark:text-white flex items-center gap-2">
            <Settings className="h-5 w-5 text-violet-400" />
            Task Settings
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Display Settings */}
          <div className="space-y-4">
            <h3 className="font-onest text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <Eye className="h-4 w-4 text-violet-400" />
              Display Settings
            </h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="show-completed">Show completed tasks</Label>
                <Switch
                  id="show-completed"
                  checked={settings.showCompletedTasks}
                  onCheckedChange={(checked) => handleSettingChange('showCompletedTasks', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="show-subtasks">Show subtasks</Label>
                <Switch
                  id="show-subtasks"
                  checked={settings.showSubtasks}
                  onCheckedChange={(checked) => handleSettingChange('showSubtasks', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="show-due-dates">Show due dates</Label>
                <Switch
                  id="show-due-dates"
                  checked={settings.showDueDates}
                  onCheckedChange={(checked) => handleSettingChange('showDueDates', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="show-assignees">Show assignees</Label>
                <Switch
                  id="show-assignees"
                  checked={settings.showAssignees}
                  onCheckedChange={(checked) => handleSettingChange('showAssignees', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="show-priority">Show priority</Label>
                <Switch
                  id="show-priority"
                  checked={settings.showPriority}
                  onCheckedChange={(checked) => handleSettingChange('showPriority', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="show-progress">Show progress</Label>
                <Switch
                  id="show-progress"
                  checked={settings.showProgress}
                  onCheckedChange={(checked) => handleSettingChange('showProgress', checked)}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Notification Settings */}
          <div className="space-y-4">
            <h3 className="font-onest text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <Bell className="h-4 w-4 text-violet-400" />
              Notification Settings
            </h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="enable-notifications">Enable notifications</Label>
                <Switch
                  id="enable-notifications"
                  checked={settings.enableNotifications}
                  onCheckedChange={(checked) => handleSettingChange('enableNotifications', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="notify-due-date">Notify on due date</Label>
                <Switch
                  id="notify-due-date"
                  checked={settings.notifyOnDueDate}
                  onCheckedChange={(checked) => handleSettingChange('notifyOnDueDate', checked)}
                  disabled={!settings.enableNotifications}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="notify-assignment">Notify on assignment</Label>
                <Switch
                  id="notify-assignment"
                  checked={settings.notifyOnAssignment}
                  onCheckedChange={(checked) => handleSettingChange('notifyOnAssignment', checked)}
                  disabled={!settings.enableNotifications}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="notify-completion">Notify on completion</Label>
                <Switch
                  id="notify-completion"
                  checked={settings.notifyOnCompletion}
                  onCheckedChange={(checked) => handleSettingChange('notifyOnCompletion', checked)}
                  disabled={!settings.enableNotifications}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Behavior Settings */}
          <div className="space-y-4">
            <h3 className="font-onest text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <Target className="h-4 w-4 text-violet-400" />
              Behavior Settings
            </h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="default-priority">Default Priority</Label>
                <Select
                  value={settings.defaultPriority}
                  onValueChange={(value) => handleSettingChange('defaultPriority', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="default-difficulty">Default Difficulty</Label>
                <Select
                  value={settings.defaultDifficulty}
                  onValueChange={(value) => handleSettingChange('defaultDifficulty', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="easy">Easy</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="hard">Hard</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="default-view">Default View</Label>
                <Select
                  value={settings.defaultView}
                  onValueChange={(value) => handleSettingChange('defaultView', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="kanban">Kanban</SelectItem>
                    <SelectItem value="table">Table</SelectItem>
                    <SelectItem value="calendar">Calendar</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="tasks-per-page">Tasks per page</Label>
                <Input
                  id="tasks-per-page"
                  type="number"
                  min="10"
                  max="200"
                  value={settings.tasksPerPage}
                  onChange={(e) => handleSettingChange('tasksPerPage', parseInt(e.target.value))}
                />
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="auto-archive">Auto-archive completed tasks</Label>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Automatically archive tasks after completion
                </p>
              </div>
              <Switch
                id="auto-archive"
                checked={settings.autoArchiveCompleted}
                onCheckedChange={(checked) => handleSettingChange('autoArchiveCompleted', checked)}
              />
            </div>
            
            {settings.autoArchiveCompleted && (
              <div className="space-y-2">
                <Label htmlFor="archive-days">Archive after (days)</Label>
                <Input
                  id="archive-days"
                  type="number"
                  min="1"
                  max="365"
                  value={settings.autoArchiveDays}
                  onChange={(e) => handleSettingChange('autoArchiveDays', parseInt(e.target.value))}
                />
              </div>
            )}
          </div>

          <Separator />

          {/* Import/Export */}
          <div className="space-y-4">
            <h3 className="font-onest text-lg font-semibold text-gray-900 dark:text-white">
              Import/Export Settings
            </h3>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={handleExportSettings}
                className="flex-1"
              >
                <Download className="h-4 w-4 mr-2" />
                Export Settings
              </Button>
              
              <div className="flex-1">
                <input
                  type="file"
                  accept=".json"
                  onChange={handleImportSettings}
                  className="hidden"
                  id="import-settings"
                />
                <Button
                  variant="outline"
                  onClick={() => document.getElementById('import-settings')?.click()}
                  className="w-full"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Import Settings
                </Button>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-between pt-4">
            <Button
              variant="outline"
              onClick={handleReset}
              className="border-amber-500/50 text-amber-400 hover:bg-amber-500/20"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset to Defaults
            </Button>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={isSaving}
              >
                Cancel
              </Button>
              
              <Button
                onClick={handleSave}
                disabled={isSaving || !hasChanges}
                className="bg-violet-500 hover:bg-violet-600"
              >
                {isSaving ? (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="mr-2"
                    >
                      <Settings className="h-4 w-4" />
                    </motion.div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Settings
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
