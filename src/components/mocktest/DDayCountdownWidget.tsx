import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  Clock, 
  Calendar, 
  AlertTriangle, 
  Target,
  Zap,
  ChevronDown,
  ChevronUp,
  Edit,
  MoreHorizontal
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { DDayExam } from "@/utils/mockTestLocalStorage";
import { cn } from "@/lib/utils";
import { format } from "date-fns";

interface DDayCountdownWidgetProps {
  exams: DDayExam[];
  onExamUpdated?: () => void;
}

interface TimeLeft {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  totalSeconds: number;
}

export function DDayCountdownWidget({ exams, onExamUpdated }: DDayCountdownWidgetProps) {
  const [timeLeft, setTimeLeft] = useState<Record<string, TimeLeft>>({});
  const [closestExam, setClosestExam] = useState<DDayExam | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Calculate time left for all exams
  const calculateTimeLeft = () => {
    const now = new Date();
    const updatedTimeLeft: Record<string, TimeLeft> = {};
    let closest: { exam: DDayExam; totalSeconds: number } | null = null;

    exams.forEach(exam => {
      const examDateTime = new Date(exam.date);
      const [hours, minutes] = exam.time.split(':').map(Number);
      examDateTime.setHours(hours, minutes, 0, 0);

      const diff = examDateTime.getTime() - now.getTime();

      if (diff > 0) {
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diff % (1000 * 60)) / 1000);
        const totalSeconds = diff / 1000;

        updatedTimeLeft[exam.id] = { days, hours, minutes, seconds, totalSeconds };

        if (!closest || totalSeconds < closest.totalSeconds) {
          closest = { exam, totalSeconds };
        }
      }
    });

    setTimeLeft(updatedTimeLeft);
    setClosestExam(closest?.exam || null);
  };

  // Update countdown every second
  useEffect(() => {
    calculateTimeLeft();
    intervalRef.current = setInterval(calculateTimeLeft, 1000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [exams]);

  // Get priority color
  const getPriorityColor = (priority: DDayExam['priority']) => {
    switch (priority) {
      case 'critical':
        return 'from-red-500 to-red-600 text-white';
      case 'high':
        return 'from-orange-500 to-orange-600 text-white';
      case 'medium':
        return 'from-yellow-500 to-yellow-600 text-white';
      case 'low':
        return 'from-green-500 to-green-600 text-white';
      default:
        return 'from-gray-500 to-gray-600 text-white';
    }
  };

  // Get urgency indicator
  const getUrgencyIndicator = (days: number) => {
    if (days <= 1) return { color: 'text-red-600', icon: AlertTriangle, pulse: true };
    if (days <= 3) return { color: 'text-orange-600', icon: Zap, pulse: true };
    if (days <= 7) return { color: 'text-yellow-600', icon: Target, pulse: false };
    return { color: 'text-green-600', icon: Calendar, pulse: false };
  };

  if (exams.length === 0) {
    return (
      <Card className="bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900/50 dark:to-slate-800/50 border-slate-200 dark:border-slate-700">
        <CardContent className="flex flex-col items-center justify-center py-12">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            className="p-6 rounded-full bg-slate-100 dark:bg-slate-800 mb-4"
          >
            <Calendar className="h-12 w-12 text-slate-400" />
          </motion.div>
          <h3 className="text-xl font-semibold text-slate-700 dark:text-slate-300 mb-2">
            No upcoming exams
          </h3>
          <p className="text-slate-500 dark:text-slate-400 text-center">
            Add your first exam to start tracking your countdown
          </p>
        </CardContent>
      </Card>
    );
  }

  const upcomingExams = exams.filter(exam => timeLeft[exam.id]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-4"
    >
      {/* Main Countdown Display */}
      {closestExam && timeLeft[closestExam.id] && (
        <Card className="overflow-hidden bg-gradient-to-br from-violet-50 via-purple-50 to-rose-50 dark:from-violet-950/30 dark:via-purple-950/30 dark:to-rose-950/30 border-violet-200 dark:border-violet-800 shadow-xl">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  className="p-2 rounded-full bg-violet-600/10"
                >
                  <Clock className="h-6 w-6 text-violet-600" />
                </motion.div>
                <div>
                  <CardTitle className="text-xl font-bold bg-gradient-to-r from-violet-600 to-purple-600 bg-clip-text text-transparent">
                    Next Exam Countdown
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">
                    {closestExam.name}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Badge 
                  className={cn(
                    "bg-gradient-to-r",
                    getPriorityColor(closestExam.priority)
                  )}
                >
                  {closestExam.priority.toUpperCase()}
                </Badge>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Exam
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Countdown Display */}
            <div className="grid grid-cols-4 gap-4">
              {[
                { label: 'Days', value: timeLeft[closestExam.id].days, color: 'from-violet-500 to-violet-600' },
                { label: 'Hours', value: timeLeft[closestExam.id].hours, color: 'from-purple-500 to-purple-600' },
                { label: 'Minutes', value: timeLeft[closestExam.id].minutes, color: 'from-rose-500 to-rose-600' },
                { label: 'Seconds', value: timeLeft[closestExam.id].seconds, color: 'from-pink-500 to-pink-600' }
              ].map((item, index) => (
                <motion.div
                  key={item.label}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className={cn(
                    "rounded-xl p-4 bg-gradient-to-br text-white shadow-lg",
                    item.color
                  )}>
                    <motion.div
                      key={item.value}
                      initial={{ scale: 1.2, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ duration: 0.3 }}
                      className="text-2xl md:text-3xl font-bold"
                    >
                      {item.value.toString().padStart(2, '0')}
                    </motion.div>
                  </div>
                  <p className="text-sm font-medium text-muted-foreground mt-2">
                    {item.label}
                  </p>
                </motion.div>
              ))}
            </div>

            {/* Exam Details */}
            <div className="flex items-center justify-between p-4 rounded-lg bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm">
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5 text-violet-600" />
                <div>
                  <p className="font-medium">
                    {format(new Date(closestExam.date), 'EEEE, MMMM d, yyyy')}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    at {closestExam.time}
                  </p>
                </div>
              </div>
              
              {(() => {
                const urgency = getUrgencyIndicator(timeLeft[closestExam.id].days);
                const Icon = urgency.icon;
                return (
                  <motion.div
                    animate={urgency.pulse ? { scale: [1, 1.1, 1] } : {}}
                    transition={{ duration: 1, repeat: Infinity }}
                    className={cn("flex items-center gap-2", urgency.color)}
                  >
                    <Icon className="h-5 w-5" />
                    <span className="font-medium">
                      {timeLeft[closestExam.id].days} days left
                    </span>
                  </motion.div>
                );
              })()}
            </div>

            {/* Expand/Collapse Button */}
            {upcomingExams.length > 1 && (
              <Button
                variant="ghost"
                onClick={() => setIsExpanded(!isExpanded)}
                className="w-full"
              >
                {isExpanded ? (
                  <>
                    <ChevronUp className="h-4 w-4 mr-2" />
                    Show Less
                  </>
                ) : (
                  <>
                    <ChevronDown className="h-4 w-4 mr-2" />
                    Show All Exams ({upcomingExams.length - 1} more)
                  </>
                )}
              </Button>
            )}
          </CardContent>
        </Card>
      )}

      {/* Additional Exams */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            className="grid gap-4 md:grid-cols-2 lg:grid-cols-3"
          >
            {upcomingExams.slice(1).map((exam, index) => (
              <motion.div
                key={exam.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="h-full bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900/50 dark:to-slate-800/50 border-slate-200 dark:border-slate-700 hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <h4 className="font-semibold text-sm truncate flex-1">
                        {exam.name}
                      </h4>
                      <Badge 
                        variant="secondary"
                        className={cn(
                          "text-xs ml-2",
                          exam.priority === 'critical' && "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400",
                          exam.priority === 'high' && "bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-400",
                          exam.priority === 'medium' && "bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400",
                          exam.priority === 'low' && "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
                        )}
                      >
                        {exam.priority}
                      </Badge>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        {format(new Date(exam.date), 'MMM d, yyyy')}
                      </div>
                      
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Clock className="h-4 w-4" />
                        {exam.time}
                      </div>
                      
                      {timeLeft[exam.id] && (
                        <div className="text-center pt-2">
                          <p className="text-lg font-bold text-violet-600">
                            {timeLeft[exam.id].days}d {timeLeft[exam.id].hours}h {timeLeft[exam.id].minutes}m
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}
