import React, { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence, PanInfo } from 'framer-motion';
import { createPortal } from 'react-dom';
import { X, GripHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface BottomSheetModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  height?: 'auto' | 'half' | 'full';
  showHandle?: boolean;
  closeOnBackdrop?: boolean;
  className?: string;
}

export function BottomSheetModal({
  isOpen,
  onClose,
  title,
  children,
  height = 'auto',
  showHandle = true,
  closeOnBackdrop = true,
  className = '',
}: BottomSheetModalProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [dragY, setDragY] = useState(0);
  const sheetRef = useRef<HTMLDivElement>(null);

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      document.body.style.touchAction = 'none';
    } else {
      document.body.style.overflow = '';
      document.body.style.touchAction = '';
    }

    return () => {
      document.body.style.overflow = '';
      document.body.style.touchAction = '';
    };
  }, [isOpen]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  const getHeightClass = () => {
    switch (height) {
      case 'half':
        return 'h-1/2';
      case 'full':
        return 'h-full';
      default:
        return 'max-h-[90vh]';
    }
  };

  const handleDragStart = () => {
    setIsDragging(true);
  };

  const handleDrag = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    if (info.offset.y > 0) {
      setDragY(info.offset.y);
    }
  };

  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    setIsDragging(false);
    setDragY(0);

    // Close if dragged down more than 100px or with sufficient velocity
    if (info.offset.y > 100 || info.velocity.y > 500) {
      onClose();
    }
  };

  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 },
  };

  const sheetVariants = {
    hidden: { 
      y: '100%',
      transition: { type: 'spring', damping: 30, stiffness: 300 }
    },
    visible: { 
      y: 0,
      transition: { type: 'spring', damping: 30, stiffness: 300 }
    },
    exit: { 
      y: '100%',
      transition: { type: 'spring', damping: 30, stiffness: 300 }
    },
  };

  if (!isOpen) return null;

  const modalContent = (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50 flex items-end"
        initial="hidden"
        animate="visible"
        exit="exit"
      >
        {/* Backdrop */}
        <motion.div
          variants={backdropVariants}
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          onClick={closeOnBackdrop ? onClose : undefined}
        />

        {/* Bottom Sheet */}
        <motion.div
          ref={sheetRef}
          variants={sheetVariants}
          drag="y"
          dragConstraints={{ top: 0, bottom: 0 }}
          dragElastic={{ top: 0, bottom: 0.2 }}
          onDragStart={handleDragStart}
          onDrag={handleDrag}
          onDragEnd={handleDragEnd}
          style={{ y: dragY }}
          className={`
            relative w-full bg-gray-900 border-t border-gray-700 rounded-t-2xl
            ${getHeightClass()} ${className}
            ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}
          `}
        >
          {/* Handle */}
          {showHandle && (
            <div className="flex justify-center pt-3 pb-2">
              <div className="w-12 h-1 bg-gray-600 rounded-full" />
            </div>
          )}

          {/* Header */}
          {title && (
            <div className="flex items-center justify-between px-6 py-4 border-b border-gray-700">
              <h2 className="text-lg font-semibold text-white">{title}</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="text-gray-400 hover:text-white"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
          )}

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {children}
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );

  return createPortal(modalContent, document.body);
}

// Hook for managing bottom sheet state
export function useBottomSheet() {
  const [isOpen, setIsOpen] = useState(false);
  const [content, setContent] = useState<React.ReactNode>(null);
  const [title, setTitle] = useState<string>('');
  const [height, setHeight] = useState<'auto' | 'half' | 'full'>('auto');

  const openBottomSheet = (
    sheetContent: React.ReactNode,
    options?: {
      title?: string;
      height?: 'auto' | 'half' | 'full';
    }
  ) => {
    setContent(sheetContent);
    setTitle(options?.title || '');
    setHeight(options?.height || 'auto');
    setIsOpen(true);
  };

  const closeBottomSheet = () => {
    setIsOpen(false);
    setTimeout(() => {
      setContent(null);
      setTitle('');
      setHeight('auto');
    }, 300);
  };

  return {
    isOpen,
    content,
    title,
    height,
    openBottomSheet,
    closeBottomSheet,
  };
}

// Mobile-optimized task action sheet
interface TaskActionSheetProps {
  taskId: string;
  taskTitle: string;
  isOpen: boolean;
  onClose: () => void;
  onEdit: () => void;
  onDelete: () => void;
  onDuplicate: () => void;
  onMarkComplete: () => void;
}

export function TaskActionSheet({
  taskId,
  taskTitle,
  isOpen,
  onClose,
  onEdit,
  onDelete,
  onDuplicate,
  onMarkComplete,
}: TaskActionSheetProps) {
  const actions = [
    {
      label: 'Edit Task',
      icon: '✏️',
      action: onEdit,
      color: 'text-blue-400',
    },
    {
      label: 'Mark Complete',
      icon: '✅',
      action: onMarkComplete,
      color: 'text-green-400',
    },
    {
      label: 'Duplicate',
      icon: '📋',
      action: onDuplicate,
      color: 'text-yellow-400',
    },
    {
      label: 'Delete',
      icon: '🗑️',
      action: onDelete,
      color: 'text-red-400',
    },
  ];

  return (
    <BottomSheetModal
      isOpen={isOpen}
      onClose={onClose}
      title={taskTitle}
      height="auto"
    >
      <div className="p-6 space-y-2">
        {actions.map((action, index) => (
          <button
            key={index}
            onClick={() => {
              action.action();
              onClose();
            }}
            className="w-full flex items-center gap-4 p-4 rounded-lg bg-gray-800/50 hover:bg-gray-700/50 transition-colors text-left"
          >
            <span className="text-xl">{action.icon}</span>
            <span className={`font-medium ${action.color}`}>{action.label}</span>
          </button>
        ))}
      </div>
    </BottomSheetModal>
  );
}

// Pull-to-refresh component
interface PullToRefreshProps {
  onRefresh: () => Promise<void>;
  children: React.ReactNode;
  threshold?: number;
  className?: string;
}

export function PullToRefresh({
  onRefresh,
  children,
  threshold = 80,
  className = '',
}: PullToRefreshProps) {
  const [isPulling, setIsPulling] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pullDistance, setPullDistance] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleTouchStart = (e: React.TouchEvent) => {
    if (containerRef.current?.scrollTop === 0) {
      setIsPulling(true);
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isPulling || isRefreshing) return;

    const touch = e.touches[0];
    const startY = touch.clientY;
    const currentY = touch.clientY;
    const distance = Math.max(0, currentY - startY);

    setPullDistance(Math.min(distance, threshold * 1.5));
  };

  const handleTouchEnd = async () => {
    if (!isPulling || isRefreshing) return;

    setIsPulling(false);

    if (pullDistance >= threshold) {
      setIsRefreshing(true);
      try {
        await onRefresh();
      } finally {
        setIsRefreshing(false);
      }
    }

    setPullDistance(0);
  };

  const refreshProgress = Math.min((pullDistance / threshold) * 100, 100);

  return (
    <div
      ref={containerRef}
      className={`relative overflow-auto ${className}`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Pull indicator */}
      <AnimatePresence>
        {(isPulling || isRefreshing) && (
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="absolute top-0 left-0 right-0 z-10 flex items-center justify-center py-4 bg-gray-900/90 backdrop-blur-sm"
            style={{ transform: `translateY(${pullDistance - threshold}px)` }}
          >
            <div className="flex items-center gap-2 text-white">
              {isRefreshing ? (
                <>
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="w-5 h-5 border-2 border-violet-500 border-t-transparent rounded-full"
                  />
                  <span>Refreshing...</span>
                </>
              ) : (
                <>
                  <motion.div
                    animate={{ rotate: refreshProgress >= 100 ? 180 : 0 }}
                    className="text-violet-500"
                  >
                    ↓
                  </motion.div>
                  <span>
                    {refreshProgress >= 100 ? 'Release to refresh' : 'Pull to refresh'}
                  </span>
                </>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {children}
    </div>
  );
}
