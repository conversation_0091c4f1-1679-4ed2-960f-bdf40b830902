export interface DashboardLayout {
  widgets: WidgetConfig[];
  gridColumns: number;
  customizations: LayoutCustomization;
}

export interface WidgetConfig {
  id: string;
  type: WidgetType;
  position: GridPosition;
  size: WidgetSize;
  isVisible: boolean;
  customProps?: Record<string, any>;
}

export interface GridPosition {
  x: number;
  y: number;
}

export interface WidgetSize {
  width: number;
  height: number;
}

export interface LayoutCustomization {
  theme: 'light' | 'dark' | 'system';
  defaultView: string;
  widgetSettings: Record<string, any>;
  notifications: NotificationSettings;
}

export interface NotificationSettings {
  examReminders: boolean;
  taskDeadlines: boolean;
  streakReminders: boolean;
  leaderboardUpdates: boolean;
}

export type WidgetType = 
  | 'tasks'
  | 'exams'
  | 'progress'
  | 'swot'
  | 'leaderboard'
  | 'streak'
  | 'countdown'
  | 'analytics';

export interface DashboardConfig {
  id: string;
  userId: string;
  layout: DashboardLayout;
  preferences: DashboardPreferences;
  createdAt: Date;
  updatedAt: Date;
}

export interface DashboardPreferences {
  theme: 'light' | 'dark' | 'system';
  defaultView: string;
  widgetSettings: Record<string, any>;
  notifications: NotificationSettings;
}

export interface Notification {
  id: string;
  type: 'exam' | 'task' | 'achievement' | 'system';
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  actionUrl?: string;
}

export interface NavigationItem {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string; size?: string | number }>;
  path: string;
  badge?: number;
  isActive: boolean;
}

export interface DashboardAnalytics {
  studyTime: {
    today: number;
    thisWeek: number;
    thisMonth: number;
    trend: number;
  };
  taskCompletion: {
    completed: number;
    total: number;
    completionRate: number;
  };
  examPreparation: {
    upcomingCount: number;
    averagePreparation: number;
    urgentCount: number;
  };
  subjectProgress: {
    totalChapters: number;
    completedChapters: number;
    averageProgress: number;
  };
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  priority: 'high' | 'medium' | 'low';
  dueTime?: Date;
  estimatedDuration?: number;
  actualDuration?: number;
  category: string;
  isCompleted: boolean;
  isRecurring: boolean;
}

export interface Exam {
  id: string;
  name: string;
  subject: string;
  date: Date;
  time: string;
  location?: string;
  instructor?: string;
  preparationProgress: number;
  studyMaterials: StudyMaterial[];
  countdownDays: number;
  isUrgent: boolean;
}

export interface StudyMaterial {
  id: string;
  title: string;
  type: 'pdf' | 'video' | 'link' | 'note';
  url: string;
  isCompleted: boolean;
}

export interface Chapter {
  id: string;
  title: string;
  subject: string;
  completionPercentage: number;
  difficultyLevel: 'easy' | 'medium' | 'hard';
  estimatedTime: number;
  hasNotes: boolean;
  hasQuiz: boolean;
  lastAccessed?: Date;
}

export interface SWOTAnalysis {
  strengths: string[];
  weaknesses: string[];
  opportunities: string[];
  threats: string[];
  lastUpdated: Date;
  aiInsights?: AIInsights;
}

export interface AIInsights {
  studyMethodsAnalysis: string;
  gritAssessment: string;
  recommendations: string[];
  improvementAreas: string[];
}

export interface LeaderboardEntry {
  userId: string;
  username: string;
  avatar?: string;
  rank: number;
  points: number;
  studyTime: number;
  tasksCompleted: number;
  achievements: Achievement[];
  isCurrentUser: boolean;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  unlockedAt: Date;
}

export interface StreakDay {
  date: Date;
  studyTime: number;
  isStreakDay: boolean;
}

export interface Milestone {
  days: number;
  title: string;
  badge: string;
  isAchieved: boolean;
}